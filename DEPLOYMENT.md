# 🚀 Deployment Guide - Cloudflare Pages

## 📋 Pre-Deployment Checklist

✅ **Build Test Passed** - `npm run build` berhasil  
✅ **Environment Variables Ready** - FAL AI key tersedia  
✅ **Files Ready** - `_redirects`, `wrangler.toml`, `.gitignore`  
✅ **Repository Ready** - Code sudah di GitHub  

## 🌐 Method 1: Git Integration (Recommended)

### Step 1: Push to GitHub
```bash
# Add all files
git add .

# Commit changes
git commit -m "🚀 Ready for Cloudflare Pages deployment"

# Push to GitHub
git push origin main
```

### Step 2: Connect to Cloudflare Pages
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Navigate to **Pages** in the sidebar
3. Click **"Create a project"**
4. Choose **"Connect to Git"**
5. Select your GitHub account and repository

### Step 3: Configure Build Settings
```
Project name: kidzplayai
Production branch: main
Framework preset: Vite
Build command: npm run build
Build output directory: dist
Root directory: / (leave empty)
```

### Step 4: Environment Variables
In Cloudflare Pages settings, add:
```
VITE_FAL_KEY = your_fal_ai_key_here
NODE_VERSION = 18
```

### Step 5: Deploy
- Click **"Save and Deploy"**
- Wait for build to complete (~2-3 minutes)
- Your app will be available at: `https://kidzplayai.pages.dev`

## ⚡ Method 2: Wrangler CLI (Advanced)

### Step 1: Install Wrangler
```bash
npm install -g wrangler
```

### Step 2: Login to Cloudflare
```bash
wrangler login
```

### Step 3: Deploy
```bash
# Build the project
npm run build

# Deploy to Cloudflare Pages
wrangler pages deploy dist --project-name kidzplayai
```

## 🔧 Custom Domain Setup

### Step 1: Add Custom Domain
1. In Cloudflare Pages dashboard
2. Go to **Custom domains**
3. Click **"Set up a custom domain"**
4. Enter your domain (e.g., `kidzplayai.com`)

### Step 2: Update DNS
Add CNAME record in your domain DNS:
```
Type: CNAME
Name: @ (or www)
Target: kidzplayai.pages.dev
```

## 🛡️ Security & Performance

### Headers Configuration
Create `public/_headers` file:
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
```

### Cache Configuration
Cloudflare automatically optimizes:
- ✅ Static assets caching
- ✅ Brotli compression
- ✅ HTTP/2 & HTTP/3
- ✅ Global CDN

## 🔍 Monitoring & Analytics

### Enable Web Analytics
1. Go to Cloudflare Dashboard
2. Navigate to **Web Analytics**
3. Add your domain
4. Copy tracking code (optional)

### Performance Monitoring
- **Core Web Vitals** - Automatic tracking
- **Real User Monitoring** - Built-in
- **Error Tracking** - Console logs

## 🚨 Troubleshooting

### Build Fails
```bash
# Clear cache and rebuild
rm -rf node_modules dist
npm install
npm run build
```

### Environment Variables Not Working
- Ensure variables start with `VITE_`
- Check spelling and case sensitivity
- Redeploy after adding variables

### 404 Errors on Refresh
- Ensure `_redirects` file exists in `public/`
- Content should be: `/* /index.html 200`

### Images Not Loading
- Check image paths are relative
- Ensure images are in `public/` folder
- Verify build includes all assets

## 📊 Build Output Analysis

Current build size:
```
dist/index.html                   0.91 kB │ gzip:  0.47 kB
dist/assets/index-bd8a481e.css   35.43 kB │ gzip:  6.49 kB
dist/assets/ui-055c1b4d.js        5.93 kB │ gzip:  2.51 kB
dist/assets/index-ca28c812.js    90.92 kB │ gzip: 20.62 kB
dist/assets/vendor-bf17a190.js  176.43 kB │ gzip: 58.08 kB
```

**Total:** ~309 KB (gzipped: ~88 KB) ✅ Excellent performance!

## 🎯 Post-Deployment

### Test Checklist
- [ ] Landing page loads correctly
- [ ] Dashboard responsive on mobile
- [ ] Memory card game works
- [ ] Image generation works (with FAL key)
- [ ] Language toggle works
- [ ] All routes work (no 404s)
- [ ] Landscape mode optimized

### Performance Optimization
- ✅ Code splitting implemented
- ✅ Asset optimization enabled
- ✅ Gzip compression active
- ✅ CDN distribution global

## 🔄 Continuous Deployment

Every push to `main` branch will automatically:
1. Trigger new build
2. Run tests (if configured)
3. Deploy to production
4. Update live site

## 📞 Support

If you encounter issues:
1. Check [Cloudflare Pages docs](https://developers.cloudflare.com/pages/)
2. Review build logs in dashboard
3. Test locally with `npm run preview`
4. Check environment variables

---

🎉 **Congratulations!** Your KidzPlay AI app is now live on Cloudflare Pages!

**Live URL:** `https://kidzplayai.pages.dev`
