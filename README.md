# 🎮 KidzPlay AI

Aplikasi permainan edukatif untuk anak-anak dengan AI image generation.

## 🚀 Features

- 🃏 **Memory Card Game** - Permainan kartu memori yang seru
- 🧩 **Puzzle Game** - Drag & drop puzzle dengan gambar favorit
- 🔄 **Sliding Puzzle** - Puzzle geser angka yang menantang
- 🎨 **AI Image Generator** - Buat gambar dengan AI (fal.ai)
- 📸 **Image Collection** - Kelola koleksi gambar berdasarkan tema
- 🏆 **Progress Tracking** - Lacak pencapaian dan progres
- 🌍 **Bilingual** - Bahasa Indonesia & English
- 📱 **Mobile-Friendly** - Responsive design dengan landscape mode

## 🛠️ Tech Stack

- **Frontend:** React 18 + Vite
- **Styling:** Tailwind CSS
- **Routing:** React Router DOM
- **AI:** fal.ai (minimax/image-01)
- **Icons:** Lucide React
- **Canvas:** Konva.js
- **Storage:** localStorage

## 📦 Installation

```bash
# Clone repository
git clone <repository-url>
cd kidzplayai

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Add your FAL AI key to .env
VITE_FAL_KEY=your_fal_ai_key_here

# Start development server
npm run dev
```

## 🌐 Deployment to Cloudflare Pages

### Method 1: Git Integration (Recommended)

1. **Push to GitHub:**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Connect to Cloudflare Pages:**
   - Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
   - Navigate to Pages
   - Click "Create a project"
   - Connect your GitHub repository
   - Select `kidzplayai` repository

3. **Configure Build Settings:**
   ```
   Framework preset: Vite
   Build command: npm run build
   Build output directory: dist
   Root directory: /
   ```

4. **Add Environment Variables:**
   - Go to Settings > Environment variables
   - Add: `VITE_FAL_KEY` = your_fal_ai_key

5. **Deploy:**
   - Click "Save and Deploy"
   - Wait for build to complete

### Method 2: Direct Upload

1. **Build locally:**
   ```bash
   npm run build
   ```

2. **Upload dist folder:**
   - Go to Cloudflare Pages
   - Click "Upload assets"
   - Upload the `dist` folder contents

### Method 3: Wrangler CLI

1. **Install Wrangler:**
   ```bash
   npm install -g wrangler
   ```

2. **Login to Cloudflare:**
   ```bash
   wrangler login
   ```

3. **Deploy:**
   ```bash
   npm run build
   wrangler pages deploy dist --project-name kidzplayai
   ```

## 🔧 Build Configuration

The project includes optimized build settings:

- **Code Splitting:** Vendor, UI, AI, and Canvas chunks
- **Minification:** Terser for optimal compression
- **Asset Optimization:** Automatic asset bundling
- **SPA Routing:** `_redirects` file for client-side routing

## 📱 Mobile Optimization

- **Landscape Mode:** Optimized for gaming on smartphones
- **Touch-Friendly:** Large touch targets and responsive design
- **Performance:** Optimized for mobile devices
- **Progressive:** Works offline with cached assets

## 🎯 Target Audience

- **Age:** 3+ years old
- **Platform:** Web browsers (mobile & desktop)
- **Language:** Indonesian (default) & English
- **Accessibility:** Kid-friendly interface

## 🔑 Environment Variables

```env
VITE_FAL_KEY=your_fal_ai_key_here
VITE_APP_NAME=KidzPlay AI
VITE_APP_VERSION=1.0.0
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📞 Support

For support and questions, please open an issue in the repository.

---

Made with ❤️ for kids everywhere! 🌟
