// Mock API endpoint for image generation
// In a real implementation, this would be a proper backend endpoint

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { prompt, model } = req.body

  if (!prompt) {
    return res.status(400).json({ error: 'Prompt is required' })
  }

  // Simulate API delay
  setTimeout(() => {
    // Return a placeholder image URL
    // In real implementation, this would call fal.ai API
    const imageUrl = `https://picsum.photos/512/512?random=${Date.now()}`
    
    res.status(200).json({
      imageUrl: imageUrl,
      prompt: prompt,
      model: model || 'fal-ai/minimax/image-01'
    })
  }, 2000)
}
