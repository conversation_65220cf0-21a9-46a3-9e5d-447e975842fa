import{r as n,a as De,u as ce,B as Re,R as Ge,b as ae,N as re,c as Be}from"./vendor-bf17a190.js";import{G as Le,P as xe,S as Ne,C as Oe,H as $e,T as he,a as Ve,b as oe,c as Ue,d as _e,I as Ke,W as Ye,X as We,e as qe,f as ke,A as X,g as ge,h as Fe,R as pe,i as Se,V as He,j as Je}from"./ui-055c1b4d.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const p of l.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&r(p)}).observe(document,{childList:!0,subtree:!0});function s(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=s(i);fetch(i.href,l)}})();var Te={exports:{}},de={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xe=n,Qe=Symbol.for("react.element"),Ze=Symbol.for("react.fragment"),et=Object.prototype.hasOwnProperty,tt=Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,st={key:!0,ref:!0,__self:!0,__source:!0};function Ce(a,t,s){var r,i={},l=null,p=null;s!==void 0&&(l=""+s),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(p=t.ref);for(r in t)et.call(t,r)&&!st.hasOwnProperty(r)&&(i[r]=t[r]);if(a&&a.defaultProps)for(r in t=a.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Qe,type:a,key:l,ref:p,props:i,_owner:tt.current}}de.Fragment=Ze;de.jsx=Ce;de.jsxs=Ce;Te.exports=de;var e=Te.exports,ue={},fe=De;ue.createRoot=fe.createRoot,ue.hydrateRoot=fe.hydrateRoot;let at=class{constructor(){this.bgMusic=null,this.soundEffects={},this.isMuted=!1,this.bgMusicVolume=.3,this.sfxVolume=.5,this.userHasInteracted=!1,this.initializeAudio(),this.setupUserInteractionListener()}setupUserInteractionListener(){const t=()=>{this.userHasInteracted=!0,this.playBackgroundMusic(),document.removeEventListener("click",t),document.removeEventListener("touchstart",t),document.removeEventListener("keydown",t)};document.addEventListener("click",t),document.addEventListener("touchstart",t),document.addEventListener("keydown",t)}initializeAudio(){try{const t="/music/music-bgm.mp3";this.bgMusic=new Audio(t),this.bgMusic.loop=!0,this.bgMusic.volume=this.bgMusicVolume,this.bgMusic.addEventListener("error",s=>{console.log("Background music failed to load:",s),this.bgMusic=null}),this.bgMusic.addEventListener("canplaythrough",()=>{console.log("Background music loaded successfully")})}catch(t){console.log("Error initializing background music:",t),this.bgMusic=null}this.createSoundEffect("click",this.generateClickSound()),this.createSoundEffect("success",this.generateSuccessSound()),this.createSoundEffect("cardMatch",this.generateCardMatchSound()),this.createSoundEffect("sparkle",this.generateSparkleSound()),this.createSoundEffect("cardFlip",this.generateCardFlipSound()),this.createSoundEffect("achievement",this.generateAchievementSound()),this.createSoundEffect("packOpen",this.generatePackOpenSound())}generateClickSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(800,t.currentTime),s.frequency.exponentialRampToValueAtTime(400,t.currentTime+.1),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.3,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.1),s.start(t.currentTime),s.stop(t.currentTime+.1)}}generateSuccessSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(523,t.currentTime),s.frequency.setValueAtTime(659,t.currentTime+.1),s.frequency.setValueAtTime(784,t.currentTime+.2),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.4,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),s.start(t.currentTime),s.stop(t.currentTime+.3)}}generateCardMatchSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination);const i=t.createOscillator(),l=t.createGain();i.connect(l),l.connect(t.destination),[{main:523,harmony:659},{main:659,harmony:784},{main:784,harmony:1047}].forEach((b,C)=>{const y=t.currentTime+C*.15;s.frequency.setValueAtTime(b.main,y),i.frequency.setValueAtTime(b.harmony,y)}),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.3,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),l.gain.setValueAtTime(0,t.currentTime),l.gain.linearRampToValueAtTime(this.sfxVolume*.2,t.currentTime+.01),l.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),s.start(t.currentTime),s.stop(t.currentTime+.5),i.start(t.currentTime),i.stop(t.currentTime+.5)}}generateSparkleSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(1200,t.currentTime),s.frequency.exponentialRampToValueAtTime(2e3,t.currentTime+.1),s.frequency.exponentialRampToValueAtTime(800,t.currentTime+.2),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.15,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.2),s.start(t.currentTime),s.stop(t.currentTime+.2)}}generateCardFlipSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(300,t.currentTime),s.frequency.linearRampToValueAtTime(600,t.currentTime+.05),s.frequency.linearRampToValueAtTime(200,t.currentTime+.1),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.2,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.1),s.start(t.currentTime),s.stop(t.currentTime+.1)}}generateAchievementSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),[523,659,784,1047].forEach((l,p)=>{s.frequency.setValueAtTime(l,t.currentTime+p*.15)}),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.5,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.6),s.start(t.currentTime),s.stop(t.currentTime+.6)}}generatePackOpenSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(200,t.currentTime),s.frequency.exponentialRampToValueAtTime(800,t.currentTime+.3),s.frequency.exponentialRampToValueAtTime(1200,t.currentTime+.5),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.4,t.currentTime+.1),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),s.start(t.currentTime),s.stop(t.currentTime+.5)}}createSoundEffect(t,s){this.soundEffects[t]=s}async playBackgroundMusic(){if(this.isMuted||!this.bgMusic){console.log("Cannot play background music:",{muted:this.isMuted,bgMusic:!!this.bgMusic});return}try{this.userHasInteracted?(console.log("Attempting to play background music..."),await this.bgMusic.play(),console.log("Background music started successfully")):console.log("Waiting for user interaction to play background music")}catch(t){console.log("Background music autoplay blocked by browser:",t),this.userHasInteracted=!1}}stopBackgroundMusic(){this.bgMusic&&(this.bgMusic.pause(),this.bgMusic.currentTime=0)}playSoundEffect(t){if(!(this.isMuted||!this.soundEffects[t]))try{this.soundEffects[t]()}catch(s){console.log("Sound effect error:",s)}}toggleMute(){return this.isMuted=!this.isMuted,this.isMuted?this.stopBackgroundMusic():this.playBackgroundMusic(),this.isMuted}setBgMusicVolume(t){this.bgMusicVolume=Math.max(0,Math.min(1,t)),this.bgMusic&&(this.bgMusic.volume=this.bgMusicVolume)}setSfxVolume(t){this.sfxVolume=Math.max(0,Math.min(1,t))}resumeAudioContext(){if(window.AudioContext||window.webkitAudioContext){const t=new(window.AudioContext||window.webkitAudioContext);t.state==="suspended"&&t.resume()}}};const Q=new at,j=()=>Q.playSoundEffect("click"),ee=()=>Q.playSoundEffect("success"),rt=()=>Q.playSoundEffect("cardMatch"),lt=()=>Q.playSoundEffect("sparkle"),ze=()=>Q.playSoundEffect("cardFlip"),nt=()=>Q.playSoundEffect("achievement"),it=()=>Q.playSoundEffect("packOpen"),ot=({onStartAdventure:a})=>{const t=[{icon:e.jsx(Le,{className:"w-8 h-8"}),title:"Permainan Memori",description:"Permainan mencocokkan kartu yang seru untuk melatih ingatan!"},{icon:e.jsx(xe,{className:"w-8 h-8"}),title:"Puzzle Seru",description:"Seret & lepas potongan puzzle dengan gambar favoritmu!"},{icon:e.jsx(Ne,{className:"w-8 h-8"}),title:"Puzzle Geser",description:"Tantang dirimu dengan permainan geser angka!"},{icon:e.jsx(Oe,{className:"w-8 h-8"}),title:"Pembuat Gambar AI",description:"Buat gambar menakjubkan dengan keajaiban AI!"},{icon:e.jsx($e,{className:"w-8 h-8"}),title:"Galeri Pribadi",description:"Kumpulkan dan gunakan gambarmu di semua permainan!"},{icon:e.jsx(he,{className:"w-8 h-8"}),title:"Pelacakan Progres",description:"Lihat pencapaianmu dan terus berkembang!"}];return e.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-white",children:[e.jsxs("div",{className:"text-center mb-8 sm:mb-12 max-w-4xl",children:[e.jsx("div",{className:"mb-4 sm:mb-6",children:e.jsxs("h1",{className:"text-4xl sm:text-6xl md:text-8xl font-bold mb-4 animate-bounce-slow",children:["🎮 ",e.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent",children:"KidzPlay AI"})," 🎨"]})}),e.jsx("p",{className:"text-lg sm:text-2xl md:text-3xl font-semibold mb-6 sm:mb-8 text-yellow-100 px-4",children:'"Di Mana Imajinasi Bertemu Kesenangan Interaktif!"'}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8 text-sm sm:text-lg",children:[e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"👶 Ramah Anak"}),e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"🎯 Usia 3+"}),e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"🆓 Gratis Sepenuhnya"})]})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-6xl px-4",children:t.map((s,r)=>e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105",children:[e.jsx("div",{className:"text-yellow-300 mb-3 sm:mb-4 flex justify-center",children:s.icon}),e.jsx("h3",{className:"text-lg sm:text-xl font-bold mb-2",children:s.title}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base",children:s.description})]},r))}),e.jsxs("div",{className:"text-center px-4",children:[e.jsx("button",{onClick:()=>{j(),a()},className:"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold text-lg sm:text-2xl py-3 sm:py-4 px-8 sm:px-12 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 animate-pulse-slow",children:"🚀 MULAI PETUALANGANMU! 🚀"}),e.jsx("p",{className:"mt-4 text-white/70 text-base sm:text-lg",children:"Dapatkan 6 gambar menakjubkan gratis saat memulai!"})]}),e.jsx("div",{className:"fixed top-10 left-10 text-4xl animate-bounce",children:"🌟"}),e.jsx("div",{className:"fixed top-20 right-20 text-3xl animate-pulse",children:"🎈"}),e.jsx("div",{className:"fixed bottom-20 left-20 text-3xl animate-wiggle",children:"🦄"}),e.jsx("div",{className:"fixed bottom-10 right-10 text-4xl animate-bounce-slow",children:"✨"})]})},je=()=>localStorage.getItem("gameApp_childName")||"",ct=a=>{localStorage.setItem("gameApp_childName",a)},we=a=>localStorage.getItem(`gameApp_hasOpenedStarterPack_${a}`)==="true",dt=a=>{localStorage.setItem(`gameApp_hasOpenedStarterPack_${a}`,"true")},_=a=>{const t=`gameApp_imageCollection_${a}`,s=localStorage.getItem(t);if(s){const r=JSON.parse(s);return r.starter&&!r.themes?{themes:[{id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:r.starter||[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"},{id:"generated",name:"My Creations",description:"Pictures I created with AI",images:r.generated||[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-green-500 to-teal-600"}]}:r}return{themes:[{id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"}]}},le=(a,t)=>{const s=`gameApp_imageCollection_${a}`;localStorage.setItem(s,JSON.stringify(t))},Pe=(a,t,s="",r="")=>{const i=_(a),l={id:`theme_${Date.now()}`,name:t,description:s,prompt:r,images:[],createdAt:new Date().toISOString(),isDefault:!1,color:ht()};return i.themes.push(l),le(a,i),l.id},mt=(a,t,s)=>{const r=_(a),i=r.themes.find(l=>l.id===t);i&&!i.isDefault&&(i.name=s,le(a,r))},ut=(a,t)=>{const s=_(a),r=s.themes.findIndex(i=>i.id===t&&!i.isDefault);r!==-1&&(s.themes.splice(r,1),le(a,s))},ve=(a,t,s)=>{const r=_(a),i=r.themes.find(l=>l.id===t);if(i){const l={id:`img_${Date.now()}`,name:s.name,dataUrl:s.dataUrl,url:s.url,prompt:s.prompt,createdAt:new Date().toISOString()};i.images.push(l),le(a,r)}},xt=(a,t,s=null)=>{const r=_(a);if(s){ve(a,s,t);return}let i=r.themes.find(l=>l.id==="generated");i||(i={id:"generated",name:"My Creations",description:"Pictures I created with AI",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-green-500 to-teal-600"},r.themes.push(i)),ve(a,"generated",t)},ht=()=>{const a=["from-pink-500 to-rose-600","from-purple-500 to-indigo-600","from-blue-500 to-cyan-600","from-green-500 to-emerald-600","from-yellow-500 to-orange-600","from-red-500 to-pink-600","from-indigo-500 to-purple-600","from-teal-500 to-blue-600"];return a[Math.floor(Math.random()*a.length)]},ne=a=>{const t=`gameApp_gameProgress_${a}`,s=localStorage.getItem(t);return s?JSON.parse(s):{memoryCard:{level:1,bestScore:null,gamesPlayed:0},puzzle:{completed:0,favoriteImage:null,bestTime:null},slidingPuzzle:{bestTime:null,difficulty:"easy",solved:0}}},gt=(a,t)=>{const s=`gameApp_gameProgress_${a}`;localStorage.setItem(s,JSON.stringify(t))},be=(a,t,s)=>{const r=ne(a);r[t]={...r[t],...s},gt(a,r)},Ae=a=>{const t=`gameApp_achievements_${a}`,s=localStorage.getItem(t);return s?JSON.parse(s):{starterPack:{unlocked:!1,date:null},artist:{unlocked:!1,progress:0,target:5},genius:{unlocked:!1,progress:0,target:10},memoryMaster:{unlocked:!1,progress:0,target:5},puzzleMaster:{unlocked:!1,progress:0,target:3},slidingMaster:{unlocked:!1,progress:0,target:5}}},pt=(a,t)=>{const s=`gameApp_achievements_${a}`;localStorage.setItem(s,JSON.stringify(t))},me=(a,t,s={})=>{const r=Ae(a);r[t]={...r[t],unlocked:!0,date:new Date().toISOString(),...s},pt(a,r)},Ie=()=>{const a="";return[{id:"starter_1",name:"Picture 1",url:`${a}/images/img1.jpg`,fallback:"https://picsum.photos/400/400?random=1",dataUrl:"https://images.unsplash.com/photo-1574158622682-e40e69881006?w=400&h=400&fit=crop&crop=center"},{id:"starter_2",name:"Picture 2",url:`${a}/images/img2.jpg`,fallback:"https://picsum.photos/400/400?random=2",dataUrl:"https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=400&fit=crop&crop=center"},{id:"starter_3",name:"Picture 3",url:`${a}/images/img3.jpg`,fallback:"https://picsum.photos/400/400?random=3",dataUrl:"https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400&h=400&fit=crop&crop=center"},{id:"starter_4",name:"Picture 4",url:`${a}/images/img4.jpg`,fallback:"https://picsum.photos/400/400?random=4",dataUrl:"https://images.unsplash.com/photo-1522770179533-24471fcdba45?w=400&h=400&fit=crop&crop=center"},{id:"starter_5",name:"Picture 5",url:`${a}/images/img5.jpg`,fallback:"https://picsum.photos/400/400?random=5",dataUrl:"https://images.unsplash.com/photo-1444418776041-9c7e33cc5a9c?w=400&h=400&fit=crop&crop=center"},{id:"starter_6",name:"Picture 6",url:`${a}/images/img6.jpg`,fallback:"https://picsum.photos/400/400?random=6",dataUrl:"https://images.unsplash.com/photo-1533827432537-70133748f5c8?w=400&h=400&fit=crop&crop=center"}]},bt=a=>{const t=_(a);let s=t.themes.find(i=>i.id==="starter");s||(s={id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"},t.themes.push(s));const r=Ie();s.images=r.map(i=>({...i,createdAt:new Date().toISOString()})),le(a,t),dt(a),me(a,"starterPack")},Me=n.createContext(),Z=()=>{const a=n.useContext(Me);if(!a)throw new Error("useLanguage must be used within a LanguageProvider");return a},ie={id:{appTitle:"KidzPlay AI",welcomeBack:"Selamat datang kembali, {name}! 🌟",searchGames:"Cari permainan...",myPictureCollection:"📸 KOLEKSI GAMBARKU ({count})",myThemeCollections:"🎨 KOLEKSI TEMA GAMBARKU",organizeYourPictures:"Atur gambar-gambarmu berdasarkan tema",generateNewImage:"BUAT GAMBAR BARU",viewByThemes:"Lihat berdasarkan tema",seeAll:"Lihat semua →",createNewTheme:"Buat Tema Baru",themeName:"Nama Tema",themeDescription:"Deskripsi Tema",enterThemeName:"Masukkan nama tema...",enterThemeDescription:"Ceritakan tentang tema ini...",optional:"opsional",cancel:"Batal",create:"Buat",pictures:"gambar",generate:"Buat",noImagesYet:"Belum ada gambar",selectTheme:"Pilih Tema",createAmazingPictures:"Buat gambar menakjub dengan AI",describeYourPicture:"Ceritakan gambar yang kamu inginkan",promptPlaceholder:"Contoh: Seekor kucing lucu bermain di taman yang penuh bunga warna-warni...",generating:"Sedang membuat",generateImage:"Buat Gambar",generateAnother:"Buat Lagi",saveToCollection:"Simpan ke Koleksi",backToDashboard:"Kembali ke Dashboard",backToMenu:"Kembali ke Menu",chooseDifficulty:"Pilih Tingkat Kesulitan",easy:"Mudah",medium:"Sedang",hard:"Sulit",level:"Level",bestScore:"Skor Terbaik",gamesPlayed:"Permainan Dimainkan",startGame:"Mulai Permainan",playAgain:"Main Lagi",restart:"Mulai Ulang",moves:"Langkah",time:"Waktu",score:"Skor",finalScore:"Skor Akhir",congratulations:"Selamat!",needMoreImages:"Butuh lebih banyak gambar",pairs:"pasang",memoryGameCompleted:"Kamu berhasil menyelesaikan permainan memori!",puzzlesCompleted:"Puzzle Selesai",bestTime:"Waktu Terbaik",availableImages:"Gambar Tersedia",pieces:"potongan",chooseImage:"Pilih Gambar",noImagesAvailable:"Tidak ada gambar tersedia",generateSomeImages:"Buat beberapa gambar terlebih dahulu!",puzzleBoard:"Papan Puzzle",puzzlePieces:"Potongan Puzzle",workspace:"Area Kerja",shuffle:"Acak",puzzleCompleted:"Puzzle Selesai!",greatJob:"Kerja bagus! Kamu berhasil menyelesaikan puzzle!",completionTime:"Waktu Selesai",selectImageForPuzzle:"Pilih gambar untuk puzzle",selectYourImage:"Pilih Gambarmu",dragPiecesHere:"Seret potongan puzzle ke sini untuk menyusunnya",dragToWorkspace:"Seret ke area kerja untuk menyusun puzzle",puzzlesSolved:"Puzzle Terpecahkan",lastDifficulty:"Kesulitan Terakhir",grid:"kotak",howToPlay:"Cara Bermain",slidingRule1:"Klik angka yang bersebelahan dengan kotak kosong untuk memindahkannya",slidingRule2:"Susun angka dari 1 hingga terakhir secara berurutan",slidingRule3:"Kotak kosong harus berada di pojok kanan bawah",puzzleSolved:"Puzzle Terpecahkan!",excellentWork:"Kerja yang luar biasa! Kamu berhasil menyelesaikan sliding puzzle!",startPlaying:"🎮 MULAI BERMAIN",memoryCardGame:"Permainan Kartu Memori",memoryCardDesc:"Cocokkan pasangan kartu",puzzleGame:"Permainan Puzzle",puzzleDesc:"Seret & lepas potongan puzzle",slidingPuzzle:"Puzzle Geser",slidingDesc:"Geser angka untuk menyelesaikan",start:"MULAI",continue:"LANJUTKAN",new:"BARU!",levelShort:"LV",yourAchievements:"🏆 PENCAPAIANMU",starterTitle:"🎁 Pemula",starterDesc:"Membuka paket kartu pertama!",artistTitle:"🎨 Seniman",artistDesc:"Buat 5 gambar",geniusTitle:"🧠 Jenius",geniusDesc:"Selesaikan 10 permainan",unlocked:"✅ TERBUKA",locked:"⏳ TERKUNCI",progress:"Progres",language:"Bahasa",indonesian:"Indonesia",english:"English"},en:{appTitle:"KidzPlay AI",welcomeBack:"Welcome back, {name}! 🌟",searchGames:"Search games...",myPictureCollection:"📸 MY PICTURE COLLECTION ({count})",myThemeCollections:"🎨 MY THEME COLLECTIONS",organizeYourPictures:"Organize your pictures by themes",generateNewImage:"GENERATE NEW IMAGE",viewByThemes:"View by themes",seeAll:"See all →",createNewTheme:"Create New Theme",themeName:"Theme Name",themeDescription:"Theme Description",enterThemeName:"Enter theme name...",enterThemeDescription:"Tell us about this theme...",optional:"optional",cancel:"Cancel",create:"Create",pictures:"pictures",generate:"Generate",noImagesYet:"No images yet",selectTheme:"Select Theme",createAmazingPictures:"Create amazing pictures with AI",describeYourPicture:"Describe the picture you want",promptPlaceholder:"Example: A cute cat playing in a garden full of colorful flowers...",generating:"Generating",generateImage:"Generate Image",generateAnother:"Generate Another",saveToCollection:"Save to Collection",backToDashboard:"Back to Dashboard",backToMenu:"Back to Menu",chooseDifficulty:"Choose Difficulty",easy:"Easy",medium:"Medium",hard:"Hard",level:"Level",bestScore:"Best Score",gamesPlayed:"Games Played",startGame:"Start Game",playAgain:"Play Again",restart:"Restart",moves:"Moves",time:"Time",score:"Score",finalScore:"Final Score",congratulations:"Congratulations!",needMoreImages:"Need more images",pairs:"pairs",memoryGameCompleted:"You completed the memory game!",puzzlesCompleted:"Puzzles Completed",bestTime:"Best Time",availableImages:"Available Images",pieces:"pieces",chooseImage:"Choose Image",noImagesAvailable:"No images available",generateSomeImages:"Generate some images first!",puzzleBoard:"Puzzle Board",puzzlePieces:"Puzzle Pieces",workspace:"Workspace",shuffle:"Shuffle",puzzleCompleted:"Puzzle Completed!",greatJob:"Great job! You completed the puzzle!",completionTime:"Completion Time",selectImageForPuzzle:"Select image for puzzle",selectYourImage:"Select Your Image",dragPiecesHere:"Drag puzzle pieces here to assemble them",dragToWorkspace:"Drag to workspace to assemble puzzle",puzzlesSolved:"Puzzles Solved",lastDifficulty:"Last Difficulty",grid:"grid",howToPlay:"How to Play",slidingRule1:"Click numbers adjacent to the empty space to move them",slidingRule2:"Arrange numbers from 1 to last in order",slidingRule3:"Empty space should be at bottom right corner",puzzleSolved:"Puzzle Solved!",excellentWork:"Excellent work! You solved the sliding puzzle!",startPlaying:"🎮 START PLAYING",memoryCardGame:"Memory Card Game",memoryCardDesc:"Match pairs of cards",puzzleGame:"Puzzle Game",puzzleDesc:"Drag & drop puzzle pieces",slidingPuzzle:"Sliding Puzzle",slidingDesc:"Slide numbers to solve",start:"START",continue:"CONTINUE",new:"NEW!",levelShort:"LV",yourAchievements:"🏆 YOUR ACHIEVEMENTS",starterTitle:"🎁 Starter",starterDesc:"Opened first card pack!",artistTitle:"🎨 Artist",artistDesc:"Generate 5 images",geniusTitle:"🧠 Genius",geniusDesc:"Complete 10 games",unlocked:"✅ UNLOCKED",locked:"⏳ LOCKED",progress:"Progress",language:"Language",indonesian:"Indonesia",english:"English"}},ft=({children:a})=>{const[t,s]=n.useState("id");n.useEffect(()=>{const l=localStorage.getItem("kidzplay-language");l&&ie[l]&&s(l)},[]);const r=l=>{ie[l]&&(s(l),localStorage.setItem("kidzplay-language",l))},i=(l,p={})=>{let b=ie[t][l]||ie.id[l]||l;return Object.keys(p).forEach(C=>{b=b.replace(`{${C}}`,p[C])}),b};return e.jsx(Me.Provider,{value:{language:t,changeLanguage:r,t:i},children:a})},jt=()=>{const{language:a,changeLanguage:t,t:s}=Z(),r=i=>{j(),t(i)};return e.jsxs("div",{className:"relative group",children:[e.jsxs("button",{className:"flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-white hover:bg-white/30 transition-colors",children:[e.jsx(Ve,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm font-medium",children:a==="id"?"ID":"EN"})]}),e.jsxs("div",{className:"absolute right-0 top-full mt-2 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50",children:[e.jsxs("button",{onClick:()=>r("id"),className:`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${a==="id"?"bg-blue-100 text-blue-800":"text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:"🇮🇩"}),e.jsx("span",{className:"font-medium",children:s("indonesian")}),a==="id"&&e.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]}),e.jsxs("button",{onClick:()=>r("en"),className:`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${a==="en"?"bg-blue-100 text-blue-800":"text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:"🇺🇸"}),e.jsx("span",{className:"font-medium",children:s("english")}),a==="en"&&e.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]})]})]})},wt=({childName:a,onGenerateImage:t,onImageSelect:s})=>{const[r,i]=n.useState({themes:[]}),[l,p]=n.useState(null),[b,C]=n.useState(""),[y,S]=n.useState(""),[u,D]=n.useState(!1),{t:f}=Z();n.useEffect(()=>{a&&i(_(a))},[a]);const O=()=>{b.trim()&&(j(),Pe(a,b.trim(),y.trim()),i(_(a)),C(""),S(""),D(!1))},h=(m,M)=>{M.trim()&&(j(),mt(a,m,M.trim()),i(_(a)),p(null))},L=m=>{j(),ut(a,m),i(_(a))},A=(m,M)=>{j(),t&&t(m,M)},U=r.themes.reduce((m,M)=>m+M.images.length,0);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-3xl font-bold text-white flex items-center gap-2",children:[e.jsx(xe,{className:"w-8 h-8"}),f("myThemeCollections")," (",U,")"]}),e.jsx("p",{className:"text-white/80 mt-1",children:f("organizeYourPictures")})]}),e.jsxs("button",{onClick:()=>{j(),D(!0)},className:"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-6 py-3 rounded-2xl font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center gap-2",children:[e.jsx(oe,{className:"w-5 h-5"}),f("createNewTheme")]})]}),u&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:f("createNewTheme")}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-2",children:f("themeName")}),e.jsx("input",{type:"text",value:b,onChange:m=>C(m.target.value),placeholder:f("enterThemeName"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none",maxLength:30})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-bold text-gray-700 mb-2",children:[f("themeDescription")," (",f("optional"),")"]}),e.jsx("textarea",{value:y,onChange:m=>S(m.target.value),placeholder:f("enterThemeDescription"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none",rows:3,maxLength:100})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{onClick:()=>{j(),D(!1),C(""),S("")},className:"flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:f("cancel")}),e.jsx("button",{onClick:O,disabled:!b.trim(),className:"flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:f("create")})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.themes.map(m=>e.jsxs("div",{className:`bg-gradient-to-br ${m.color} rounded-3xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 overflow-hidden`,children:[e.jsxs("div",{className:"p-6 text-white",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[l===m.id?e.jsx("input",{type:"text",defaultValue:m.name,onBlur:M=>h(m.id,M.target.value),onKeyPress:M=>{M.key==="Enter"&&h(m.id,M.target.value)},className:"bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 rounded-lg px-3 py-1 text-xl font-bold focus:outline-none focus:ring-2 focus:ring-white/50",autoFocus:!0}):e.jsx("h3",{className:"text-xl font-bold truncate",children:m.name}),e.jsx("p",{className:"text-white/80 text-sm mt-1 line-clamp-2",children:m.description})]}),e.jsx("div",{className:"flex gap-2 ml-3",children:!m.isDefault&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>{j(),p(l===m.id?null:m.id)},className:"p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors",children:e.jsx(Ue,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>L(m.id),className:"p-2 rounded-lg bg-white/20 hover:bg-red-500/50 transition-colors",children:e.jsx(_e,{className:"w-4 h-4"})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-white/90 text-sm font-medium",children:[m.images.length," ",f("pictures")]}),e.jsxs("button",{onClick:()=>A(m.id,m.name),className:"bg-white/20 hover:bg-white/30 backdrop-blur-sm px-3 py-1 rounded-lg text-sm font-bold transition-colors flex items-center gap-1",children:[e.jsx(oe,{className:"w-4 h-4"}),f("generate")]})]})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm p-4",children:m.images.length>0?e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[m.images.slice(0,6).map((M,G)=>e.jsx("div",{className:"aspect-square rounded-lg overflow-hidden cursor-pointer hover:scale-105 transition-transform",onClick:()=>{j(),s&&s(M)},children:e.jsx("img",{src:M.url||M.dataUrl,alt:M.name,className:"w-full h-full object-cover"})},M.id)),m.images.length>6&&e.jsxs("div",{className:"aspect-square rounded-lg bg-white/20 flex items-center justify-center text-white font-bold text-sm",children:["+",m.images.length-6]})]}):e.jsxs("div",{className:"aspect-square rounded-lg border-2 border-dashed border-white/30 flex flex-col items-center justify-center text-white/60",children:[e.jsx(Ke,{className:"w-8 h-8 mb-2"}),e.jsx("span",{className:"text-sm font-medium",children:f("noImagesYet")})]})})]},m.id))})]})},vt=({childName:a,onClose:t,onImageGenerated:s,selectedThemeId:r=null,selectedThemeName:i=null})=>{const[l,p]=n.useState(""),[b,C]=n.useState(!1),[y,S]=n.useState(null),[u,D]=n.useState(r||"generated"),[f,O]=n.useState(""),[h,L]=n.useState(!1),[A,U]=n.useState({themes:[]}),{t:m}=Z();n.useEffect(()=>{a&&U(_(a)),i&&i!=="My Creations"&&p(`${i} themed picture for kids`)},[a,i]);const M=async()=>{if(l.trim()){C(!0),j();try{const T=await fetch("/api/generate-image",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:`${l}, child-friendly, colorful, cartoon style, safe for kids aged 3-9`,model:"fal-ai/minimax/image-01"})});if(T.ok){const V=await T.json();S({dataUrl:V.imageUrl,prompt:l}),ee()}else setTimeout(()=>{S({dataUrl:`https://picsum.photos/512/512?random=${Date.now()}`,prompt:l}),ee()},2e3)}catch(T){console.error("Error generating image:",T),setTimeout(()=>{S({dataUrl:`https://picsum.photos/512/512?random=${Date.now()}`,prompt:l}),ee()},2e3)}finally{C(!1)}}},G=()=>{if(f.trim()){j();const T=Pe(a,f.trim(),`Pictures about ${f}`,l);U(_(a)),D(T),O(""),L(!1)}},W=()=>{if(!y)return;j();const T={name:`${l.slice(0,30)}...`,dataUrl:y.dataUrl,prompt:y.prompt};u!=="new"&&(xt(a,T,u),s&&s(),t())},d=A.themes||[];return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-2xl w-full shadow-2xl max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl",children:e.jsx(Ye,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:m("generateNewImage")}),e.jsx("p",{className:"text-gray-600",children:m("createAmazingPictures")})]})]}),e.jsx("button",{onClick:()=>{j(),t()},className:"p-2 rounded-xl hover:bg-gray-100 transition-colors",children:e.jsx(We,{className:"w-6 h-6 text-gray-500"})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-3",children:m("selectTheme")}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[d.map(T=>e.jsx("button",{onClick:()=>{j(),D(T.id)},className:`p-4 rounded-xl border-2 transition-all duration-200 text-left ${u===T.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-4 h-4 rounded-full bg-gradient-to-r ${T.color}`}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-gray-800",children:T.name}),e.jsxs("div",{className:"text-xs text-gray-500",children:[T.images.length," ",m("pictures")]})]})]})},T.id)),e.jsxs("button",{onClick:()=>{j(),L(!0)},className:"p-4 rounded-xl border-2 border-dashed border-gray-300 hover:border-blue-500 transition-all duration-200 flex items-center justify-center gap-2 text-gray-600 hover:text-blue-600",children:[e.jsx(oe,{className:"w-5 h-5"}),e.jsx("span",{className:"font-bold",children:m("createNewTheme")})]})]})]}),h&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60",children:e.jsxs("div",{className:"bg-white rounded-2xl p-6 max-w-md w-full shadow-xl",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:m("createNewTheme")}),e.jsx("input",{type:"text",value:f,onChange:T=>O(T.target.value),placeholder:m("enterThemeName"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none mb-4",maxLength:30}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{j(),L(!1),O("")},className:"flex-1 px-4 py-2 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:m("cancel")}),e.jsx("button",{onClick:G,disabled:!f.trim(),className:"flex-1 px-4 py-2 rounded-xl bg-blue-500 text-white font-bold hover:bg-blue-600 transition-colors disabled:opacity-50",children:m("create")})]})]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-3",children:m("describeYourPicture")}),e.jsx("textarea",{value:l,onChange:T=>p(T.target.value),placeholder:m("promptPlaceholder"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none",rows:4,maxLength:200}),e.jsxs("div",{className:"text-right text-xs text-gray-500 mt-1",children:[l.length,"/200"]})]}),e.jsx("button",{onClick:M,disabled:!l.trim()||b,className:"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 mb-6",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-white"}),m("generating"),"..."]}):e.jsxs(e.Fragment,{children:[e.jsx(Ne,{className:"w-6 h-6"}),m("generateImage")]})}),y&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-6 text-center",children:[e.jsx("img",{src:y.dataUrl,alt:"Generated",className:"w-full max-w-md mx-auto rounded-xl shadow-lg"}),e.jsxs("p",{className:"text-sm text-gray-600 mt-3 italic",children:['"',y.prompt,'"']})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{j(),S(null)},className:"flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:m("generateAnother")}),e.jsx("button",{onClick:W,className:"flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold hover:shadow-lg transition-all duration-200",children:m("saveToCollection")})]})]})]})})},yt=({childName:a})=>{const[t,s]=n.useState({themes:[]}),[r,i]=n.useState({}),[l,p]=n.useState({}),[b,C]=n.useState(!1),[y,S]=n.useState(!1),[u,D]=n.useState(null),[f,O]=n.useState(null),{t:h}=Z(),L=ce();n.useEffect(()=>{a&&(s(_(a)),i(ne(a)),p(Ae(a)))},[a]);const A=t.themes?.reduce((d,T)=>[...d,...T.images],[])||[],U=A.length,m=(d=null,T=null)=>{D(d),O(T),S(!0)},M=()=>{s(_(a))},G=[{id:"memory",title:h("memoryCardGame"),icon:"🃏",description:h("memoryCardDesc"),progress:r.memoryCard,color:"from-blue-500 to-purple-600",thumbnail:"/images/memory-card.jpg"},{id:"puzzle",title:h("puzzleGame"),icon:"🧩",description:h("puzzleDesc"),progress:r.puzzle,color:"from-green-500 to-teal-600",thumbnail:"/images/puzzle.jpg"},{id:"sliding",title:h("slidingPuzzle"),icon:"🔄",description:h("slidingDesc"),progress:r.slidingPuzzle,color:"from-orange-500 to-red-600",thumbnail:"/images/sliding-puzzle.jpg"}],W=[{key:"starterPack",title:h("starterTitle"),description:h("starterDesc"),unlocked:l.starterPack?.unlocked},{key:"artist",title:h("artistTitle"),description:h("artistDesc"),unlocked:l.artist?.unlocked,progress:l.artist?.progress,target:l.artist?.target},{key:"genius",title:h("geniusTitle"),description:h("geniusDesc"),unlocked:l.genius?.unlocked,progress:l.genius?.progress,target:l.genius?.target}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 landscape-optimized portrait-optimized",children:e.jsxs("div",{className:"dashboard-container",children:[e.jsxs("header",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-8 bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 landscape-header",children:[e.jsxs("div",{className:"mb-4 sm:mb-0",children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-white flex items-center gap-2 landscape-text-lg",children:["🏠 ",h("appTitle")]}),e.jsx("p",{className:"text-white/80 text-base sm:text-lg landscape-text-sm",children:h("welcomeBack",{name:a})})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4 w-full sm:w-auto",children:[e.jsx(jt,{}),e.jsxs("div",{className:"relative flex-1 sm:flex-none",children:[e.jsx(qe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("input",{type:"text",placeholder:h("searchGames"),className:"w-full sm:w-auto pl-8 sm:pl-10 pr-4 py-2 text-sm sm:text-base rounded-full bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"})]})]})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2 sm:gap-0",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:h("myPictureCollection",{count:U})}),e.jsxs("div",{className:"flex gap-2 sm:gap-3 text-sm sm:text-base",children:[e.jsxs("button",{onClick:()=>{j(),C(!0)},className:"text-white/80 hover:text-white transition-colors flex items-center gap-1 sm:gap-2",children:[e.jsx(xe,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"hidden sm:inline",children:h("viewByThemes")}),e.jsx("span",{className:"sm:hidden",children:"Tema"})]}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors",children:h("seeAll")})]})]}),e.jsxs("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-4",children:[A.slice(0,5).map(d=>e.jsxs("div",{className:"bg-white rounded-xl sm:rounded-2xl shadow-lg p-2 sm:p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer",onClick:()=>j(),children:[e.jsx("img",{src:d.url||d.dataUrl,alt:d.name,className:"w-full aspect-square object-cover rounded-lg sm:rounded-xl mb-1 sm:mb-2"}),e.jsx("p",{className:"text-xs font-bold text-gray-700 text-center truncate landscape-text-sm",children:d.name})]},d.id)),e.jsxs("div",{className:"bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl sm:rounded-2xl shadow-lg p-2 sm:p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer flex flex-col items-center justify-center text-white min-h-[80px] sm:min-h-[100px]",onClick:()=>m(),children:[e.jsx(oe,{className:"w-6 h-6 sm:w-8 sm:h-8 mb-1"}),e.jsx("p",{className:"text-xs font-bold text-center landscape-text-sm",children:h("generateNewImage")})]})]})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:h("startPlaying")}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors text-sm sm:text-base",children:h("seeAll")})]}),e.jsx("div",{className:"responsive-grid landscape-games-grid portrait-games-grid",children:G.map(d=>e.jsxs("div",{className:`relative aspect-square landscape-game-card portrait-game-card bg-gradient-to-br ${d.color} rounded-xl sm:rounded-2xl shadow-lg text-white hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer overflow-hidden`,onClick:T=>{T.stopPropagation(),j(),L(`/game/${d.id}`)},children:[e.jsx("div",{className:"absolute inset-0 opacity-20",children:e.jsx("img",{src:d.thumbnail,alt:d.title,className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"relative z-10 h-full p-3 sm:p-6 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-4",children:[e.jsx("div",{className:"text-2xl sm:text-3xl",children:d.icon}),e.jsx("div",{className:"text-xs bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full font-bold landscape-text-sm",children:d.progress?.gamesPlayed>0?`${h("level")} ${d.progress.level||1}`:h("new")})]}),e.jsxs("div",{className:"flex-1 flex flex-col justify-center text-center",children:[e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-1 sm:mb-2 landscape-text-sm",children:d.title}),e.jsx("p",{className:"text-white/80 text-xs sm:text-sm mb-2 sm:mb-4 landscape-text-sm hidden sm:block",children:d.description})]}),e.jsxs("div",{className:"flex flex-col items-center space-y-1 sm:space-y-2",children:[d.progress?.gamesPlayed>0&&e.jsx("div",{className:"flex justify-center",children:[...Array(3)].map((T,V)=>e.jsx(ke,{className:"w-3 h-3 sm:w-4 sm:h-4 text-yellow-300 fill-current"},V))}),e.jsx("button",{className:"bg-white/30 backdrop-blur-sm hover:bg-white/40 rounded-full px-4 sm:px-6 py-1 sm:py-2 text-xs sm:text-sm font-bold transition-colors landscape-text-sm",onClick:T=>{T.stopPropagation(),j(),L(`/game/${d.id}`)},children:h("start")})]})]})]},d.id))})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:h("yourAchievements")}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors text-sm sm:text-base",children:h("seeAll")})]}),e.jsx("div",{className:"responsive-grid",children:W.map(d=>e.jsx("div",{className:`bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 text-white border-2 ${d.unlocked?"border-yellow-400":"border-white/20"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl sm:text-3xl mb-2 sm:mb-3",children:d.title.split(" ")[0]}),e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-1 sm:mb-2 landscape-text-sm",children:d.title.split(" ").slice(1).join(" ")}),e.jsx("p",{className:"text-white/80 mb-3 sm:mb-4 text-xs sm:text-sm landscape-text-sm",children:d.description}),d.unlocked?e.jsx("div",{className:"bg-yellow-400 text-yellow-900 rounded-full px-4 py-2 text-sm font-bold",children:h("unlocked")}):d.progress!==void 0?e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm",children:[h("progress"),": ",d.progress,"/",d.target]}),e.jsx("div",{className:"bg-white/20 rounded-full h-2",children:e.jsx("div",{className:"bg-yellow-400 rounded-full h-2 transition-all duration-300",style:{width:`${d.progress/d.target*100}%`}})})]}):e.jsx("div",{className:"bg-white/20 rounded-full px-4 py-2 text-sm font-bold",children:h("locked")})]})},d.key))})]}),b&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 rounded-3xl p-8 max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-3xl font-bold text-white",children:h("myThemeCollections")}),e.jsx("button",{onClick:()=>{j(),C(!1)},className:"text-white/80 hover:text-white text-2xl font-bold",children:"✕"})]}),e.jsx(wt,{childName:a,onGenerateImage:m,onImageSelect:()=>{}})]})}),y&&e.jsx(vt,{childName:a,selectedThemeId:u,selectedThemeName:f,onClose:()=>{S(!1),D(null),O(null)},onImageGenerated:M}),e.jsx("div",{className:"landscape-hint",children:"📱 Putar layar untuk pengalaman bermain yang lebih baik!"})]})})},Nt=({delay:a=0})=>e.jsx("div",{className:"absolute pointer-events-none",style:{animation:`sparkle 2s ease-in-out ${a}s infinite`,left:`${Math.random()*100}%`,top:`${Math.random()*100}%`},children:e.jsx("div",{className:"text-yellow-400 text-2xl animate-pulse",children:"✨"})}),kt=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState([]),[l,p]=n.useState([]),[b,C]=n.useState([]),[y,S]=n.useState([]),[u,D]=n.useState(0),[f,O]=n.useState(0),[h,L]=n.useState(null),[A,U]=n.useState("easy"),[m,M]=n.useState([]),[G,W]=n.useState({}),[d,T]=n.useState(0),[V,F]=n.useState(null),{t:E}=Z(),I=ce(),Y={easy:{pairs:6,gridCols:4,gridRows:3,baseScore:1e3,perfectTime:60},medium:{pairs:8,gridCols:4,gridRows:4,baseScore:1500,perfectTime:90},hard:{pairs:12,gridCols:6,gridRows:4,baseScore:2e3,perfectTime:120}};n.useEffect(()=>{if(a){const o=_(a).themes?.reduce((g,w)=>[...g,...w.images],[])||[];M(o);const x=ne(a);W(x.memoryCard||{level:1,bestScore:null,gamesPlayed:0})}},[a]),n.useEffect(()=>{let v=null;return t==="playing"&&h&&(v=setInterval(()=>{O(Math.floor((Date.now()-h)/1e3))},1e3)),()=>{v&&clearInterval(v)}},[t,h]),n.useEffect(()=>{if(l.length===2){const[v,o]=l;r[v].imageId===r[o].imageId?(rt(),S([v,o]),setTimeout(()=>{lt()},200),setTimeout(()=>{S([])},2e3),C(x=>[...x,v,o]),p([]),T(x=>x+100)):setTimeout(()=>{p([])},1e3),D(x=>x+1)}},[l,r,f]),n.useEffect(()=>{if(b.length===r.length&&r.length>0&&t==="playing"){const v=Y[A],o=d,x=Math.max(0,Math.floor((v.perfectTime-f)*10)),g=v.pairs,w=Math.max(0,(g*2-u)*20),P=v.baseScore;let c=o+x+w+P;c=Math.max(c,100),F({baseScore:o,timeBonus:x,moveBonus:w,difficultyBonus:P,finalScore:c}),s("completed"),T(c);const N={...G,gamesPlayed:(G.gamesPlayed||0)+1,bestScore:Math.max(G.bestScore||0,c),level:Math.min((G.level||1)+(c>(G.bestScore||0)?1:0),10)};be(a,"memoryCard",N),W(N),N.gamesPlayed>=5&&me(a,"memoryMaster")}},[b.length,r.length,t]);const K=v=>{const o=[...v];for(let x=o.length-1;x>0;x--){const g=Math.floor(Math.random()*(x+1));[o[x],o[g]]=[o[g],o[x]]}return o},J=n.useCallback(()=>{if(m.length<Y[A].pairs){alert(E("needMoreImages"));return}j();const o=K(m).slice(0,Y[A].pairs).flatMap((g,w)=>[{id:w*2,imageId:g.id,image:g},{id:w*2+1,imageId:g.id,image:g}]),x=K(o);i(x),p([]),C([]),S([]),D(0),O(0),T(0),F(null),L(Date.now()),s("playing")},[m,A,E]),te=v=>{t!=="playing"||l.length>=2||l.includes(v)||b.includes(v)||(ze(),p(o=>[...o,v]))},se=()=>{j(),s("menu"),i([]),p([]),C([]),S([]),D(0),O(0),T(0),F(null),L(null)},H=v=>{const o=Math.floor(v/60),x=v%60;return`${o}:${x.toString().padStart(2,"0")}`};return t==="menu"?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-2 sm:p-4 landscape-optimized",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-4 sm:mb-8 gap-4 sm:gap-0",children:[e.jsxs("button",{onClick:()=>{j(),I("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:E("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-xl sm:text-3xl font-bold text-white mb-1 sm:mb-2 landscape-text-lg",children:["🃏 ",E("memoryCardGame")]}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base landscape-text-sm hidden sm:block",children:E("memoryCardDesc")})]}),e.jsx("div",{className:"w-8 sm:w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-4 sm:mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-3 sm:gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:G.level||1}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:E("level")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:G.bestScore||0}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:E("bestScore")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:G.gamesPlayed||0}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:E("gamesPlayed")})]})]})}),e.jsxs("div",{className:"bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-lg sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6 text-center landscape-text-base",children:E("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mb-4 sm:mb-8",children:Object.entries(Y).map(([v,o])=>e.jsx("button",{onClick:()=>{j(),U(v)},className:`p-3 sm:p-6 rounded-xl sm:rounded-2xl border-2 transition-all duration-200 ${A===v?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl sm:text-2xl mb-1 sm:mb-2",children:v==="easy"?"😊":v==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-1 sm:mb-2 text-sm sm:text-base landscape-text-sm",children:E(v)}),e.jsxs("div",{className:"text-xs sm:text-sm text-gray-600 landscape-text-sm",children:[o.pairs," ",E("pairs")," • ",o.gridCols,"×",o.gridRows]})]})},v))}),e.jsxs("div",{className:"text-center",children:[e.jsx("button",{onClick:J,disabled:m.length<Y[A].pairs,className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:E("startGame")}),m.length<Y[A].pairs&&e.jsxs("p",{className:"text-red-500 text-sm mt-2",children:[E("needMoreImages")," (",m.length,"/",Y[A].pairs,")"]})]})]})]})}):e.jsxs("div",{className:"min-h-screen p-2 sm:p-4 relative landscape-optimized",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/30 via-purple-500/30 to-pink-500/30"}),e.jsx("style",{jsx:!0,children:`
        @keyframes sparkle {
          0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
          }
        }

        @keyframes celebration {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        .celebration-card {
          animation: celebration 0.6s ease-in-out;
        }

        .sparkle-container {
          position: absolute;
          inset: 0;
          pointer-events: none;
          overflow: hidden;
          border-radius: 1rem;
        }
      `}),e.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-3 sm:mb-6 gap-3 sm:gap-0",children:[e.jsxs("button",{onClick:()=>{j(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:E("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl px-3 sm:px-6 py-2 sm:py-3",children:[e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(ge,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:H(f)})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(Fe,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsxs("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:[u," ",e.jsx("span",{className:"hidden sm:inline",children:E("moves")})]})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(ke,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:d})]})]}),e.jsxs("button",{onClick:se,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:E("restart")})]})]}),e.jsx("div",{className:"grid gap-2 sm:gap-4 lg:gap-6 mx-auto",style:{gridTemplateColumns:`repeat(${Y[A].gridCols}, 1fr)`,maxWidth:`${Y[A].gridCols*(window.innerWidth<640?80:window.innerWidth<1024?120:160)}px`},children:r.map((v,o)=>{const x=l.includes(o)||b.includes(o),g=y.includes(o);return e.jsx("div",{onClick:()=>te(o),className:`aspect-square cursor-pointer transition-all duration-300 transform hover:scale-105 ${g?"celebration-card":""}`,style:{perspective:"1000px"},children:e.jsxs("div",{className:`
                    relative w-full h-full transition-transform duration-700
                    ${x?"rotate-y-180":""}
                    ${b.includes(o)?"ring-4 ring-green-400 rounded-2xl":""}
                  `,style:{transformStyle:"preserve-3d",transform:x?"rotateY(180deg)":"rotateY(0deg)"},children:[g&&e.jsx("div",{className:"sparkle-container",children:[...Array(8)].map((w,P)=>e.jsx(Nt,{delay:P*.2},P))}),e.jsx("div",{className:"absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg",style:{backfaceVisibility:"hidden"},children:e.jsx("div",{className:"text-3xl sm:text-4xl lg:text-6xl drop-shadow-lg",children:"🎈"})}),e.jsx("div",{className:"absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-white p-1 sm:p-2 shadow-lg",style:{backfaceVisibility:"hidden",transform:"rotateY(180deg)"},children:e.jsx("img",{src:v.image.url||v.image.dataUrl,alt:v.image.name,className:"w-full h-full object-cover rounded-lg sm:rounded-xl"})})]})},v.id)})}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:E("congratulations")}),e.jsx("p",{className:"text-gray-600 mb-6",children:E("memoryGameCompleted")}),e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:H(f)}),e.jsx("div",{className:"text-sm text-gray-600",children:E("time")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:u}),e.jsx("div",{className:"text-sm text-gray-600",children:E("moves")})]})]}),V&&e.jsx("div",{className:"border-t pt-4",children:e.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Pasangan ditemukan:"}),e.jsxs("span",{className:"font-bold",children:["+",V.baseScore]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus waktu:"}),e.jsxs("span",{className:"font-bold text-blue-600",children:["+",V.timeBonus]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus efisiensi:"}),e.jsxs("span",{className:"font-bold text-green-600",children:["+",V.moveBonus]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus tingkat kesulitan:"}),e.jsxs("span",{className:"font-bold text-orange-600",children:["+",V.difficultyBonus]})]}),e.jsxs("div",{className:"border-t pt-2 flex justify-between text-lg font-bold",children:[e.jsx("span",{children:"Total Skor:"}),e.jsx("span",{className:"text-purple-600",children:V.finalScore})]})]})})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:J,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:E("playAgain")}),e.jsx("button",{onClick:()=>{j(),I("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:E("backToDashboard")})]})]})})]})]})},St=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState(null),[l,p]=n.useState(0),[b,C]=n.useState(null),[y,S]=n.useState("easy"),[u,D]=n.useState([]),[f,O]=n.useState({}),[h,L]=n.useState(0),[A,U]=n.useState([]),[m,M]=n.useState(null),[G,W]=n.useState(null),[d,T]=n.useState({x:0,y:0}),[V,F]=n.useState(window.innerWidth<640),E=n.useRef(null),{t:I}=Z(),Y=ce(),K={easy:{pieces:4,cols:2,rows:2,timeBonus:300},medium:{pieces:9,cols:3,rows:3,timeBonus:200},hard:{pieces:16,cols:4,rows:4,timeBonus:100}};n.useEffect(()=>{if(a){const N=_(a).themes?.reduce(($,z)=>[...$,...z.images],[])||[];D(N);const k=ne(a);O(k.puzzle||{completed:0,favoriteImage:null,bestTime:null})}},[a]),n.useEffect(()=>{let c=null;return t==="playing"&&b&&(c=setInterval(()=>{p(Math.floor((Date.now()-b)/1e3))},1e3)),()=>{c&&clearInterval(c)}},[t,b]),n.useEffect(()=>{const c=()=>{F(window.innerWidth<640)};return window.addEventListener("resize",c),()=>window.removeEventListener("resize",c)},[]);const J=c=>{console.log("Initializing simple puzzle with image:",c);const{cols:N,rows:k}=K[y],$=[];for(let B=0;B<k;B++)for(let R=0;R<N;R++){const q=B*N+R;$.push({id:q,row:B,col:R,currentRow:B,currentCol:R,correctPosition:{row:B,col:R},isPlaced:!1,image:c.url||c.dataUrl})}const z=[...$];for(let B=z.length-1;B>0;B--){const R=Math.floor(Math.random()*(B+1)),q=z[B].currentRow,Ee=z[B].currentCol;z[B].currentRow=z[R].currentRow,z[B].currentCol=z[R].currentCol,z[R].currentRow=q,z[R].currentCol=Ee}U(z),console.log("Puzzle pieces created:",z)},te=(c,N)=>{M(N);const k=c.target.getBoundingClientRect();T({x:c.clientX-k.left,y:c.clientY-k.top})},se=c=>{c.preventDefault()},H=(c,N,k)=>{if(c.preventDefault(),!m)return;const $=A.find(z=>z.currentRow===N&&z.currentCol===k);if($&&$.id!==m.id){const z=A.map(B=>B.id===m.id?{...B,currentRow:N,currentCol:k}:B.id===$.id?{...B,currentRow:m.currentRow,currentCol:m.currentCol}:B);U(z),j(),v(z)}M(null)},v=c=>{c.every(k=>k.currentRow===k.correctPosition.row&&k.currentCol===k.correctPosition.col)&&setTimeout(()=>{s("completed"),ee();const k=Math.floor((Date.now()-b)/1e3);p(k);const $=Math.max(K[y].timeBonus-k,50);L($);const z={...f,completed:(f.completed||0)+1,bestTime:f.bestTime?Math.min(f.bestTime,k):k,favoriteImage:r?.id};be(a,"puzzle",z),O(z),z.completed>=3&&me(a,"puzzleMaster")},500)},o=(c,N,k)=>{if(j(),G)if(G.id!==c.id){const $=A.map(z=>z.id===G.id?{...z,currentRow:N,currentCol:k}:z.id===c.id?{...z,currentRow:G.currentRow,currentCol:G.currentCol}:z);U($),W(null),v($)}else W(null);else W(c)},x=c=>{j(),i(c),p(0),L(0),C(Date.now()),s("playing"),setTimeout(()=>{J(c)},100)},g=()=>{if(j(),A.length>0){const c=[...A];for(let N=c.length-1;N>0;N--){const k=Math.floor(Math.random()*(N+1)),$=c[N].currentRow,z=c[N].currentCol;c[N].currentRow=c[k].currentRow,c[N].currentCol=c[k].currentCol,c[k].currentRow=$,c[k].currentCol=z}U(c)}},w=()=>{j(),r?J(r):s("menu")},P=c=>{const N=Math.floor(c/60),k=c%60;return`${N}:${k.toString().padStart(2,"0")}`};return t==="menu"?e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"}),e.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{j(),Y("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:I("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🧩 ",I("puzzleGame")]}),e.jsx("p",{className:"text-white/80",children:I("puzzleDesc")})]}),e.jsx("div",{className:"w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:f.completed||0}),e.jsx("div",{className:"text-sm opacity-80",children:I("puzzlesCompleted")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:f.bestTime?P(f.bestTime):"--:--"}),e.jsx("div",{className:"text-sm opacity-80",children:I("bestTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:u.length}),e.jsx("div",{className:"text-sm opacity-80",children:I("availableImages")})]})]})}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:I("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Object.entries(K).map(([c,N])=>e.jsx("button",{onClick:()=>{j(),S(c)},className:`p-6 rounded-2xl border-2 transition-all duration-200 ${y===c?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:c==="easy"?"😊":c==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-2",children:I(c)}),e.jsxs("div",{className:"text-sm text-gray-600",children:[N.cols,"x",N.rows," (",N.pieces," ",I("pieces"),")"]})]})},c))})]}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:I("chooseImage")}),u.length===0?e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📸"}),e.jsx("p",{children:I("noImagesAvailable")}),e.jsx("p",{className:"text-sm mt-2",children:I("generateSomeImages")})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:u.map(c=>e.jsxs("button",{onClick:()=>x(c),className:"group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[e.jsx("img",{src:c.url||c.dataUrl,alt:c.name,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("span",{className:"text-2xl",children:"🧩"})})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2",children:e.jsx("p",{className:"text-white text-xs font-bold truncate",children:c.name})})]},c.id))})]})]})]}):e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"}),e.jsxs("div",{className:"max-w-7xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("button",{onClick:()=>{j(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:I("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(ge,{className:"w-5 h-5"}),e.jsx("span",{className:"font-bold",children:P(l)})]}),e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(he,{className:"w-5 h-5"}),e.jsxs("span",{className:"font-bold",children:[K[y].cols,"x",K[y].rows," (",K[y].pieces," ",I("pieces"),")"]})]})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:g,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Se,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:I("shuffle")})]}),e.jsxs("button",{onClick:w,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:I("restart")})]})]})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-3xl p-6",children:[e.jsxs("div",{className:"mb-4 text-center",children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:I("puzzleGame")}),e.jsx("p",{className:"text-white/70 text-sm hidden sm:block",children:I("dragPiecesToSolve")}),e.jsx("p",{className:"text-white/70 text-sm sm:hidden",children:"Ketuk untuk memilih, ketuk lagi untuk menukar posisi"})]}),A.length>0&&e.jsx("div",{className:"flex justify-center px-4",children:e.jsx("div",{ref:E,className:"bg-white rounded-2xl p-3 sm:p-4 md:p-6 shadow-2xl max-w-full",style:{display:"grid",gridTemplateColumns:`repeat(${K[y].cols}, 1fr)`,gap:V?"2px":"6px",width:"fit-content"},children:Array.from({length:K[y].pieces}).map((c,N)=>{const{cols:k,rows:$}=K[y],z=Math.floor(N/k),B=N%k,R=A.find(q=>q.currentRow===z&&q.currentCol===B);return e.jsx("div",{className:"relative w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36 border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-100",onDragOver:se,onDrop:q=>H(q,z,B),children:R&&e.jsxs("div",{draggable:!0,onDragStart:q=>te(q,R),onClick:()=>o(R,z,B),className:`w-full h-full cursor-move relative group transition-all duration-200 ${G?.id===R.id?"ring-4 ring-blue-500 ring-opacity-70":""}`,style:{backgroundImage:`url(${R.image})`,backgroundSize:`${k*100}% ${$*100}%`,backgroundPosition:`${k>1?R.correctPosition.col*100/(k-1):0}% ${$>1?R.correctPosition.row*100/($-1):0}%`},children:[e.jsx("div",{className:"absolute top-1 left-1 bg-black/50 text-white text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity",children:R.id+1}),G?.id===R.id&&e.jsx("div",{className:"absolute inset-0 border-2 border-blue-500 bg-blue-500/20 rounded",children:e.jsx("div",{className:"absolute top-1 right-1 text-blue-600 animate-pulse",children:"👆"})}),R.currentRow===R.correctPosition.row&&R.currentCol===R.correctPosition.col&&e.jsx("div",{className:"absolute inset-0 border-2 border-green-500 bg-green-500/20 rounded",children:e.jsx("div",{className:"absolute top-1 right-1 text-green-600",children:"✓"})})]})},N)})})})]}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:I("puzzleCompleted")}),e.jsx("p",{className:"text-gray-600 mb-6",children:I("greatJob")}),e.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:P(l)}),e.jsx("div",{className:"text-sm text-gray-600",children:I("completionTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:h}),e.jsx("div",{className:"text-sm text-gray-600",children:I("score")})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>s("menu"),className:"flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:I("playAgain")}),e.jsx("button",{onClick:()=>{j(),Y("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:I("backToDashboard")})]})]})})]})]})},Tt=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState([]),[l,p]=n.useState({row:0,col:0}),[b,C]=n.useState(0),[y,S]=n.useState(0),[u,D]=n.useState(null),[f,O]=n.useState("easy"),[h,L]=n.useState({}),[A,U]=n.useState(0),[m,M]=n.useState(null),[G,W]=n.useState([]),{t:d}=Z(),T=ce(),V={easy:{size:3,timeBonus:300},medium:{size:4,timeBonus:500},hard:{size:5,timeBonus:800}};n.useEffect(()=>{if(a){const x=_(a).themes?.reduce((w,P)=>[...w,...P.images],[])||[];W(x);const g=ne(a);L(g.slidingPuzzle||{bestTime:null,difficulty:"easy",solved:0})}},[a]),n.useEffect(()=>{let o=null;return t==="playing"&&u&&(o=setInterval(()=>{S(Math.floor((Date.now()-u)/1e3))},1e3)),()=>{o&&clearInterval(o)}},[t,u]),n.useEffect(()=>{if(t==="playing"&&r.length>0){const o=V[f].size;if(r.every((g,w)=>g.every((P,c)=>w===o-1&&c===o-1?P===0:P===w*o+c+1))){s("completed"),ee();const g=Math.max(V[f].timeBonus-y-b,50);U(g);const w={...h,solved:(h.solved||0)+1,bestTime:h.bestTime?Math.min(h.bestTime,y):y,difficulty:f};be(a,"slidingPuzzle",w),L(w),w.solved>=5&&me(a,"slidingMaster")}}},[r,t,f,y,b,h,a]);const F=n.useCallback(o=>{const x=[];for(let g=0;g<o;g++){const w=[];for(let P=0;P<o;P++)g===o-1&&P===o-1?w.push(0):w.push(g*o+P+1);x.push(w)}return x},[]),E=n.useCallback(o=>{const x=o.length;let g=o.map(c=>[...c]),w=x-1,P=x-1;for(let c=0;c<1e3;c++){const N=[];if(w>0&&N.push({row:w-1,col:P}),w<x-1&&N.push({row:w+1,col:P}),P>0&&N.push({row:w,col:P-1}),P<x-1&&N.push({row:w,col:P+1}),N.length>0){const k=N[Math.floor(Math.random()*N.length)];g[w][P]=g[k.row][k.col],g[k.row][k.col]=0,w=k.row,P=k.col}}return{board:g,emptyPosition:{row:w,col:P}}},[]),I=n.useCallback(o=>{j(),M(o);const x=V[f].size,g=F(x),{board:w,emptyPosition:P}=E(g);i(w),p(P),C(0),S(0),U(0),D(Date.now()),s("playing")},[f,F,E]),Y=()=>{j(),s("imageSelect")},K=(o,x)=>{const g=Math.abs(o-l.row),w=Math.abs(x-l.col);return g===1&&w===0||g===0&&w===1},J=(o,x)=>{if(!K(o,x)||t!=="playing")return;j();const g=r.map(w=>[...w]);g[l.row][l.col]=g[o][x],g[o][x]=0,i(g),p({row:o,col:x}),C(w=>w+1)},te=()=>{j(),s("imageSelect"),i([]),C(0),S(0),U(0),D(null),M(null)},se=()=>{if(t!=="playing")return;j();const{board:o,emptyPosition:x}=E(r);i(o),p(x),C(g=>g+10)},H=o=>{const x=Math.floor(o/60),g=o%60;return`${x}:${g.toString().padStart(2,"0")}`};if(t==="menu")return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{j(),T("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🔄 ",d("slidingPuzzle")]}),e.jsx("p",{className:"text-white/80",children:d("slidingDesc")})]}),e.jsx("div",{className:"w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:h.solved||0}),e.jsx("div",{className:"text-sm opacity-80",children:d("puzzlesSolved")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:h.bestTime?H(h.bestTime):"--:--"}),e.jsx("div",{className:"text-sm opacity-80",children:d("bestTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold capitalize",children:h.difficulty||"easy"}),e.jsx("div",{className:"text-sm opacity-80",children:d("lastDifficulty")})]})]})}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:d("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:Object.entries(V).map(([o,x])=>e.jsx("button",{onClick:()=>{j(),O(o)},className:`p-6 rounded-2xl border-2 transition-all duration-200 ${f===o?"border-orange-500 bg-orange-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:o==="easy"?"😊":o==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-2",children:d(o)}),e.jsxs("div",{className:"text-sm text-gray-600",children:[x.size,"×",x.size," ",d("grid")]})]})},o))}),e.jsx("div",{className:"text-center",children:e.jsx("button",{onClick:Y,className:"bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200",children:d("chooseImage")})}),e.jsxs("div",{className:"mt-8 bg-gray-50 rounded-2xl p-6",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-3",children:d("howToPlay")}),e.jsxs("ul",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("li",{children:["• ",d("slidingRule1")]}),e.jsxs("li",{children:["• ",d("slidingRule2")]}),e.jsxs("li",{children:["• ",d("slidingRule3")]})]})]})]})]})]});if(t==="imageSelect")return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{j(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToMenu")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🖼️ ",d("chooseImage")]}),e.jsx("p",{className:"text-white/80",children:d("selectImageForPuzzle")})]}),e.jsx("div",{className:"w-32"})]}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:d("selectYourImage")}),G.length===0?e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📸"}),e.jsx("p",{children:d("noImagesAvailable")}),e.jsx("p",{className:"text-sm mt-2",children:d("generateSomeImages")})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:G.map(o=>e.jsxs("button",{onClick:()=>I(o),className:"group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[e.jsx("img",{src:o.url||o.dataUrl,alt:o.name,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("span",{className:"text-2xl",children:"🔄"})})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2",children:e.jsx("p",{className:"text-white text-xs font-bold truncate",children:o.name})})]},o.id))})]})]})]});const v=V[f].size;return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-6 gap-4",children:[e.jsxs("button",{onClick:()=>{j(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(X,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-4 sm:px-6 py-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(ge,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:H(y)})]}),e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(he,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsxs("span",{className:"font-bold text-sm sm:text-base",children:[b," ",d("moves")]})]})]}),e.jsxs("div",{className:"flex gap-2 sm:gap-3",children:[e.jsxs("button",{onClick:se,className:"flex items-center gap-1 sm:gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Se,{className:"w-5 h-5 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base hidden sm:inline",children:d("shuffle")})]}),e.jsxs("button",{onClick:te,className:"flex items-center gap-1 sm:gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-5 h-5 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base hidden sm:inline",children:d("restart")})]})]})]}),e.jsx("div",{className:"text-center mb-4",children:e.jsxs("p",{className:"text-white/80 text-sm",children:[e.jsx("span",{className:"hidden sm:inline",children:d("clickAdjacentTiles")}),e.jsx("span",{className:"sm:hidden",children:"Ketuk kotak di sebelah area kosong untuk memindahkannya"})]})}),e.jsx("div",{className:"flex justify-center px-4",children:e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-3xl p-4 sm:p-6 md:p-8 max-w-full",children:e.jsx("div",{className:"grid gap-1 sm:gap-2 mx-auto",style:{gridTemplateColumns:`repeat(${v}, 1fr)`,width:"min(90vw, 400px)",aspectRatio:"1"},children:r.map((o,x)=>o.map((g,w)=>{if(g===0)return e.jsx("div",{className:"aspect-square bg-gray-200/50 border-2 border-dashed border-gray-400/50 rounded-lg sm:rounded-xl cursor-default flex items-center justify-center",children:e.jsx("div",{className:"text-gray-400 text-2xl",children:"⬜"})},`${x}-${w}`);const P=Math.floor((g-1)/v),c=(g-1)%v;return e.jsxs("button",{onClick:()=>J(x,w),className:`
                        aspect-square rounded-lg sm:rounded-xl transition-all duration-200 transform overflow-hidden relative
                        ${K(x,w)?"hover:scale-105 shadow-lg cursor-pointer hover:shadow-xl border-2 border-yellow-400 hover:border-yellow-500":"shadow-md cursor-default border-2 border-gray-300"}
                      `,style:{backgroundImage:m?`url(${m.url||m.dataUrl})`:"none",backgroundPosition:`${c*(100/(v-1))}% ${P*(100/(v-1))}%`,backgroundSize:`${v*100}% ${v*100}%`,backgroundRepeat:"no-repeat",backgroundColor:m?"transparent":"#f3f4f6"},children:[!m&&e.jsx("span",{className:"font-bold text-xl text-gray-800",children:g}),K(x,w)&&e.jsx("div",{className:"absolute inset-0 bg-yellow-400/20 flex items-center justify-center",children:e.jsx("div",{className:"bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold animate-pulse",children:"↕"})})]},`${x}-${w}`)}))})})}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:d("puzzleSolved")}),e.jsx("p",{className:"text-gray-600 mb-6",children:d("excellentWork")}),e.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:H(y)}),e.jsx("div",{className:"text-sm text-gray-600",children:d("time")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:b}),e.jsx("div",{className:"text-sm text-gray-600",children:d("moves")})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("div",{className:"text-3xl font-bold text-orange-600",children:A}),e.jsx("div",{className:"text-sm text-gray-600",children:d("finalScore")})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>I(m),className:"flex-1 bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:d("playAgain")}),e.jsx("button",{onClick:()=>{j(),T("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:d("backToDashboard")})]})]})})]})]})},Ct=({onComplete:a,onClose:t})=>{const[s,r]=n.useState(""),[i,l]=n.useState(!1),p=b=>{if(b.preventDefault(),s.trim().length<2){alert("Please enter at least 2 characters for your name!");return}l(!0),ee(),ct(s.trim()),setTimeout(()=>{a(s.trim())},500)};return e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl transform animate-bounce-slow",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"text-6xl mb-4",children:"👋"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Hi there, friend!"}),e.jsx("p",{className:"text-gray-600 text-lg",children:"What should we call you?"})]}),e.jsxs("form",{onSubmit:p,className:"space-y-6",children:[e.jsx("div",{children:e.jsx("input",{type:"text",value:s,onChange:b=>r(b.target.value),placeholder:"Type your name here...",className:"w-full px-4 py-3 text-xl text-center border-2 border-gray-300 rounded-2xl focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-200 transition-all duration-200",maxLength:20,autoFocus:!0,disabled:i})}),e.jsx("button",{type:"submit",disabled:!s.trim()||i,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-300 disabled:to-gray-400 text-white font-bold text-xl py-3 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none transition-all duration-200 disabled:cursor-not-allowed",children:i?e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Getting ready..."]}):"🎮 LET'S GO! 🎮"})]}),e.jsxs("div",{className:"mt-6 flex justify-center gap-4 text-2xl",children:[e.jsx("span",{className:"animate-bounce",children:"🌟"}),e.jsx("span",{className:"animate-pulse",children:"🎈"}),e.jsx("span",{className:"animate-bounce-slow",children:"🦄"}),e.jsx("span",{className:"animate-wiggle",children:"✨"})]})]})})})},ye=({src:a,fallback:t,dataUrl:s,alt:r,className:i,onLoad:l,onError:p,...b})=>{const[C,y]=n.useState(a),[S,u]=n.useState(!1),[D,f]=n.useState(0);n.useEffect(()=>{y(a),u(!1),f(0)},[a]);const O=L=>{if(u(!0),f(A=>A+1),D===0&&s&&C!==s)y(s);else if(D===1&&t&&C!==t)y(t);else if(D===2&&!C.includes("picsum")){const A=Math.floor(Math.random()*1e3);y(`https://picsum.photos/400/400?random=${A}`)}p&&p(L)},h=L=>{u(!1),l&&l(L)};return e.jsx("img",{src:C,alt:r,className:i,onError:O,onLoad:h,...b})},zt=({childName:a,onComplete:t,onClose:s})=>{const[r,i]=n.useState("unopened"),[l,p]=n.useState([]),b=Ie(),C=()=>{it(),i("opening"),setTimeout(()=>{i("revealing"),y()},2e3)},y=()=>{b.forEach((u,D)=>{setTimeout(()=>{ze(),p(f=>[...f,u])},D*500)}),setTimeout(()=>{nt(),i("complete"),bt(a)},b.length*500+1e3)},S=()=>{j(),t()};return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-4xl w-full shadow-2xl",children:[r==="unopened"&&e.jsxs("div",{className:"text-center",children:[e.jsxs("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:["🎁 ",a?`Welcome ${a}!`:"Welcome!"," 🎁"]}),e.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Here's your FREE starter pack with 6 amazing pictures!"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"w-48 h-64 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer flex flex-col items-center justify-center text-white",onClick:C,children:[e.jsx("div",{className:"text-6xl mb-4",children:"📦"}),e.jsx("div",{className:"text-2xl font-bold mb-2",children:"MYSTERY"}),e.jsx("div",{className:"text-xl font-bold mb-4",children:"PACK"}),e.jsx("div",{className:"bg-white/20 rounded-full px-4 py-2 text-sm font-bold",children:"CLICK ME!"})]})}),e.jsx("p",{className:"text-lg text-gray-500",children:"✨ 6 Amazing Pictures Inside! ✨"})]}),r==="opening"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-8",children:"🎉 Opening Your Pack! 🎉"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"w-48 h-64 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-2xl animate-pulse flex flex-col items-center justify-center text-white",children:[e.jsx("div",{className:"text-6xl mb-4 animate-bounce",children:"✨"}),e.jsx("div",{className:"text-xl font-bold",children:"OPENING..."})]})}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),r==="revealing"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-8",children:"🌟 Your New Pictures! 🌟"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-8",children:b.map((u,D)=>e.jsx("div",{className:"relative",children:l.includes(u)?e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg p-4 transform animate-bounce-slow",children:[e.jsx(ye,{src:u.url,dataUrl:u.dataUrl,fallback:u.fallback,alt:u.name,className:"w-full h-32 object-cover rounded-xl mb-2"}),e.jsx("p",{className:"text-sm font-bold text-gray-700",children:u.name})]}):e.jsx("div",{className:"bg-gray-200 rounded-2xl shadow-lg p-4 h-44 flex items-center justify-center",children:e.jsx("div",{className:"text-4xl text-gray-400",children:"?"})})},u.id))}),e.jsx("p",{className:"text-lg text-gray-600",children:"Revealing your amazing pictures..."})]}),r==="complete"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"🎉 AWESOME! 🎉"}),e.jsxs("p",{className:"text-xl text-gray-600 mb-8",children:["⭐ These pictures are now yours! ⭐",e.jsx("br",{}),"You can use them in all your games!"]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-8",children:b.map(u=>e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg p-4 transform hover:scale-105 transition-all duration-200",children:[e.jsx(ye,{src:u.url,dataUrl:u.dataUrl,fallback:u.fallback,alt:u.name,className:"w-full h-32 object-cover rounded-xl mb-2"}),e.jsx("p",{className:"text-sm font-bold text-gray-700",children:u.name})]},u.id))}),e.jsx("button",{onClick:S,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold text-2xl py-4 px-8 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",children:"🎮 AWESOME! LET'S PLAY! 🎮"})]})]})})},Pt=()=>{const[a,t]=n.useState(!1),[s,r]=n.useState(null),[i,l]=n.useState(!1),[p,b]=n.useState(!1);n.useEffect(()=>{(()=>{try{const u=new Audio("/music/music-bgm.mp3");u.loop=!0,u.volume=.3,u.preload="auto",u.addEventListener("canplaythrough",()=>{console.log("Background music loaded successfully"),l(!0)}),u.addEventListener("error",D=>{console.log("Background music failed to load:",D),l(!1)}),r(u)}catch(u){console.log("Error initializing audio:",u)}})();const S=()=>{b(!0),document.removeEventListener("click",S),document.removeEventListener("touchstart",S),document.removeEventListener("keydown",S)};return document.addEventListener("click",S),document.addEventListener("touchstart",S),document.addEventListener("keydown",S),()=>{document.removeEventListener("click",S),document.removeEventListener("touchstart",S),document.removeEventListener("keydown",S),s&&(s.pause(),s.src="")}},[]),n.useEffect(()=>{s&&i&&p&&!a&&(async()=>{try{await s.play(),console.log("Background music started")}catch(S){console.log("Failed to play background music:",S)}})()},[s,i,p,a]);const C=()=>{s&&(a?s.play().catch(console.log):s.pause()),t(!a)};return e.jsx("button",{onClick:C,className:"fixed top-4 right-4 z-50 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110",title:a?"Unmute":"Mute",children:a?e.jsx(He,{size:24}):e.jsx(Je,{size:24})})};function At(){const[a,t]=n.useState(""),[s,r]=n.useState(!1),[i,l]=n.useState(!1),[p,b]=n.useState(!1);n.useEffect(()=>{const u=je();u&&(t(u),b(!0),we(u)||l(!0))},[]);const C=()=>{const u=je();u?(t(u),b(!0),we(u)||l(!0)):r(!0)},y=u=>{t(u),r(!1),l(!0),b(!0)},S=()=>{l(!1)};return e.jsx(Re,{children:e.jsx(ft,{children:e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500",children:[e.jsxs(Ge,{children:[e.jsx(ae,{path:"/",element:p?e.jsx(re,{to:"/dashboard",replace:!0}):e.jsx(ot,{onStartAdventure:C})}),e.jsx(ae,{path:"/dashboard",element:p?e.jsx(yt,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/memory",element:p?e.jsx(kt,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/puzzle",element:p?e.jsx(St,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/sliding",element:p?e.jsx(Tt,{childName:a}):e.jsx(re,{to:"/",replace:!0})})]}),s&&e.jsx(Ct,{onComplete:y,onClose:()=>r(!1)}),i&&e.jsx(zt,{childName:a,onComplete:S,onClose:()=>l(!1)}),e.jsx(Pt,{})]})})})}ue.createRoot(document.getElementById("root")).render(e.jsx(Be.StrictMode,{children:e.jsx(At,{})}));
