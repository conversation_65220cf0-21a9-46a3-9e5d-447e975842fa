import{r as n,a as Re,u as ce,B as Ge,R as Be,b as ae,N as re,c as Oe}from"./vendor-bf17a190.js";import{G as Le,P as xe,S as ke,C as $e,H as Ve,T as he,a as _e,b as oe,c as Ue,d as Ke,I as Ye,W as qe,X as We,e as Fe,f as Te,A as Q,g as ge,h as He,R as pe,i as Se,V as Je,j as Xe}from"./ui-055c1b4d.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const v of l.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&r(v)}).observe(document,{childList:!0,subtree:!0});function s(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=s(i);fetch(i.href,l)}})();var Ce={exports:{}},de={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qe=n,Ze=Symbol.for("react.element"),et=Symbol.for("react.fragment"),tt=Object.prototype.hasOwnProperty,st=Qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,at={key:!0,ref:!0,__self:!0,__source:!0};function ze(a,t,s){var r,i={},l=null,v=null;s!==void 0&&(l=""+s),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(v=t.ref);for(r in t)tt.call(t,r)&&!at.hasOwnProperty(r)&&(i[r]=t[r]);if(a&&a.defaultProps)for(r in t=a.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Ze,type:a,key:l,ref:v,props:i,_owner:st.current}}de.Fragment=et;de.jsx=ze;de.jsxs=ze;Ce.exports=de;var e=Ce.exports,ue={},fe=Re;ue.createRoot=fe.createRoot,ue.hydrateRoot=fe.hydrateRoot;class rt{constructor(){this.bgMusic=null,this.soundEffects={},this.isMuted=!1,this.bgMusicVolume=.3,this.sfxVolume=.5,this.userHasInteracted=!1,this.initializeAudio(),this.setupUserInteractionListener()}setupUserInteractionListener(){const t=()=>{this.userHasInteracted=!0,this.playBackgroundMusic(),document.removeEventListener("click",t),document.removeEventListener("touchstart",t),document.removeEventListener("keydown",t)};document.addEventListener("click",t),document.addEventListener("touchstart",t),document.addEventListener("keydown",t)}initializeAudio(){this.bgMusic=new Audio("/music/music-bgm.mp3"),this.bgMusic.loop=!0,this.bgMusic.volume=this.bgMusicVolume,this.createSoundEffect("click",this.generateClickSound()),this.createSoundEffect("success",this.generateSuccessSound()),this.createSoundEffect("cardMatch",this.generateCardMatchSound()),this.createSoundEffect("sparkle",this.generateSparkleSound()),this.createSoundEffect("cardFlip",this.generateCardFlipSound()),this.createSoundEffect("achievement",this.generateAchievementSound()),this.createSoundEffect("packOpen",this.generatePackOpenSound())}generateClickSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(800,t.currentTime),s.frequency.exponentialRampToValueAtTime(400,t.currentTime+.1),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.3,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.1),s.start(t.currentTime),s.stop(t.currentTime+.1)}}generateSuccessSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(523,t.currentTime),s.frequency.setValueAtTime(659,t.currentTime+.1),s.frequency.setValueAtTime(784,t.currentTime+.2),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.4,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),s.start(t.currentTime),s.stop(t.currentTime+.3)}}generateCardMatchSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination);const i=t.createOscillator(),l=t.createGain();i.connect(l),l.connect(t.destination),[{main:523,harmony:659},{main:659,harmony:784},{main:784,harmony:1047}].forEach((j,P)=>{const T=t.currentTime+P*.15;s.frequency.setValueAtTime(j.main,T),i.frequency.setValueAtTime(j.harmony,T)}),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.3,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),l.gain.setValueAtTime(0,t.currentTime),l.gain.linearRampToValueAtTime(this.sfxVolume*.2,t.currentTime+.01),l.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),s.start(t.currentTime),s.stop(t.currentTime+.5),i.start(t.currentTime),i.stop(t.currentTime+.5)}}generateSparkleSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(1200,t.currentTime),s.frequency.exponentialRampToValueAtTime(2e3,t.currentTime+.1),s.frequency.exponentialRampToValueAtTime(800,t.currentTime+.2),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.15,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.2),s.start(t.currentTime),s.stop(t.currentTime+.2)}}generateCardFlipSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(300,t.currentTime),s.frequency.linearRampToValueAtTime(600,t.currentTime+.05),s.frequency.linearRampToValueAtTime(200,t.currentTime+.1),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.2,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.1),s.start(t.currentTime),s.stop(t.currentTime+.1)}}generateAchievementSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),[523,659,784,1047].forEach((l,v)=>{s.frequency.setValueAtTime(l,t.currentTime+v*.15)}),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.5,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.6),s.start(t.currentTime),s.stop(t.currentTime+.6)}}generatePackOpenSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(200,t.currentTime),s.frequency.exponentialRampToValueAtTime(800,t.currentTime+.3),s.frequency.exponentialRampToValueAtTime(1200,t.currentTime+.5),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.4,t.currentTime+.1),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),s.start(t.currentTime),s.stop(t.currentTime+.5)}}createSoundEffect(t,s){this.soundEffects[t]=s}async playBackgroundMusic(){if(!(this.isMuted||!this.bgMusic))try{this.userHasInteracted&&await this.bgMusic.play()}catch(t){console.log("Background music autoplay blocked by browser:",t),this.userHasInteracted=!1}}stopBackgroundMusic(){this.bgMusic&&(this.bgMusic.pause(),this.bgMusic.currentTime=0)}playSoundEffect(t){if(!(this.isMuted||!this.soundEffects[t]))try{this.soundEffects[t]()}catch(s){console.log("Sound effect error:",s)}}toggleMute(){return this.isMuted=!this.isMuted,this.isMuted?this.stopBackgroundMusic():this.playBackgroundMusic(),this.isMuted}setBgMusicVolume(t){this.bgMusicVolume=Math.max(0,Math.min(1,t)),this.bgMusic&&(this.bgMusic.volume=this.bgMusicVolume)}setSfxVolume(t){this.sfxVolume=Math.max(0,Math.min(1,t))}resumeAudioContext(){if(window.AudioContext||window.webkitAudioContext){const t=new(window.AudioContext||window.webkitAudioContext);t.state==="suspended"&&t.resume()}}}const F=new rt,je=()=>F.playBackgroundMusic(),b=()=>F.playSoundEffect("click"),ee=()=>F.playSoundEffect("success"),lt=()=>F.playSoundEffect("cardMatch"),nt=()=>F.playSoundEffect("sparkle"),Pe=()=>F.playSoundEffect("cardFlip"),it=()=>F.playSoundEffect("achievement"),ot=()=>F.playSoundEffect("packOpen"),ct=()=>F.toggleMute(),we=()=>F.resumeAudioContext(),dt=({onStartAdventure:a})=>{const t=[{icon:e.jsx(Le,{className:"w-8 h-8"}),title:"Permainan Memori",description:"Permainan mencocokkan kartu yang seru untuk melatih ingatan!"},{icon:e.jsx(xe,{className:"w-8 h-8"}),title:"Puzzle Seru",description:"Seret & lepas potongan puzzle dengan gambar favoritmu!"},{icon:e.jsx(ke,{className:"w-8 h-8"}),title:"Puzzle Geser",description:"Tantang dirimu dengan permainan geser angka!"},{icon:e.jsx($e,{className:"w-8 h-8"}),title:"Pembuat Gambar AI",description:"Buat gambar menakjubkan dengan keajaiban AI!"},{icon:e.jsx(Ve,{className:"w-8 h-8"}),title:"Galeri Pribadi",description:"Kumpulkan dan gunakan gambarmu di semua permainan!"},{icon:e.jsx(he,{className:"w-8 h-8"}),title:"Pelacakan Progres",description:"Lihat pencapaianmu dan terus berkembang!"}];return e.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-white",children:[e.jsxs("div",{className:"text-center mb-8 sm:mb-12 max-w-4xl",children:[e.jsx("div",{className:"mb-4 sm:mb-6",children:e.jsxs("h1",{className:"text-4xl sm:text-6xl md:text-8xl font-bold mb-4 animate-bounce-slow",children:["🎮 ",e.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent",children:"KidzPlay AI"})," 🎨"]})}),e.jsx("p",{className:"text-lg sm:text-2xl md:text-3xl font-semibold mb-6 sm:mb-8 text-yellow-100 px-4",children:'"Di Mana Imajinasi Bertemu Kesenangan Interaktif!"'}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8 text-sm sm:text-lg",children:[e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"👶 Ramah Anak"}),e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"🎯 Usia 3+"}),e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"🆓 Gratis Sepenuhnya"})]})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-6xl px-4",children:t.map((s,r)=>e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105",children:[e.jsx("div",{className:"text-yellow-300 mb-3 sm:mb-4 flex justify-center",children:s.icon}),e.jsx("h3",{className:"text-lg sm:text-xl font-bold mb-2",children:s.title}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base",children:s.description})]},r))}),e.jsxs("div",{className:"text-center px-4",children:[e.jsx("button",{onClick:()=>{b(),a()},className:"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold text-lg sm:text-2xl py-3 sm:py-4 px-8 sm:px-12 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 animate-pulse-slow",children:"🚀 MULAI PETUALANGANMU! 🚀"}),e.jsx("p",{className:"mt-4 text-white/70 text-base sm:text-lg",children:"Dapatkan 6 gambar menakjubkan gratis saat memulai!"})]}),e.jsx("div",{className:"fixed top-10 left-10 text-4xl animate-bounce",children:"🌟"}),e.jsx("div",{className:"fixed top-20 right-20 text-3xl animate-pulse",children:"🎈"}),e.jsx("div",{className:"fixed bottom-20 left-20 text-3xl animate-wiggle",children:"🦄"}),e.jsx("div",{className:"fixed bottom-10 right-10 text-4xl animate-bounce-slow",children:"✨"})]})},ve=()=>localStorage.getItem("gameApp_childName")||"",mt=a=>{localStorage.setItem("gameApp_childName",a)},ye=a=>localStorage.getItem(`gameApp_hasOpenedStarterPack_${a}`)==="true",ut=a=>{localStorage.setItem(`gameApp_hasOpenedStarterPack_${a}`,"true")},U=a=>{const t=`gameApp_imageCollection_${a}`,s=localStorage.getItem(t);if(s){const r=JSON.parse(s);return r.starter&&!r.themes?{themes:[{id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:r.starter||[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"},{id:"generated",name:"My Creations",description:"Pictures I created with AI",images:r.generated||[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-green-500 to-teal-600"}]}:r}return{themes:[{id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"}]}},le=(a,t)=>{const s=`gameApp_imageCollection_${a}`;localStorage.setItem(s,JSON.stringify(t))},Ae=(a,t,s="",r="")=>{const i=U(a),l={id:`theme_${Date.now()}`,name:t,description:s,prompt:r,images:[],createdAt:new Date().toISOString(),isDefault:!1,color:pt()};return i.themes.push(l),le(a,i),l.id},xt=(a,t,s)=>{const r=U(a),i=r.themes.find(l=>l.id===t);i&&!i.isDefault&&(i.name=s,le(a,r))},ht=(a,t)=>{const s=U(a),r=s.themes.findIndex(i=>i.id===t&&!i.isDefault);r!==-1&&(s.themes.splice(r,1),le(a,s))},Ne=(a,t,s)=>{const r=U(a),i=r.themes.find(l=>l.id===t);if(i){const l={id:`img_${Date.now()}`,name:s.name,dataUrl:s.dataUrl,url:s.url,prompt:s.prompt,createdAt:new Date().toISOString()};i.images.push(l),le(a,r)}},gt=(a,t,s=null)=>{const r=U(a);if(s){Ne(a,s,t);return}let i=r.themes.find(l=>l.id==="generated");i||(i={id:"generated",name:"My Creations",description:"Pictures I created with AI",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-green-500 to-teal-600"},r.themes.push(i)),Ne(a,"generated",t)},pt=()=>{const a=["from-pink-500 to-rose-600","from-purple-500 to-indigo-600","from-blue-500 to-cyan-600","from-green-500 to-emerald-600","from-yellow-500 to-orange-600","from-red-500 to-pink-600","from-indigo-500 to-purple-600","from-teal-500 to-blue-600"];return a[Math.floor(Math.random()*a.length)]},ne=a=>{const t=`gameApp_gameProgress_${a}`,s=localStorage.getItem(t);return s?JSON.parse(s):{memoryCard:{level:1,bestScore:null,gamesPlayed:0},puzzle:{completed:0,favoriteImage:null,bestTime:null},slidingPuzzle:{bestTime:null,difficulty:"easy",solved:0}}},bt=(a,t)=>{const s=`gameApp_gameProgress_${a}`;localStorage.setItem(s,JSON.stringify(t))},be=(a,t,s)=>{const r=ne(a);r[t]={...r[t],...s},bt(a,r)},Ie=a=>{const t=`gameApp_achievements_${a}`,s=localStorage.getItem(t);return s?JSON.parse(s):{starterPack:{unlocked:!1,date:null},artist:{unlocked:!1,progress:0,target:5},genius:{unlocked:!1,progress:0,target:10},memoryMaster:{unlocked:!1,progress:0,target:5},puzzleMaster:{unlocked:!1,progress:0,target:3},slidingMaster:{unlocked:!1,progress:0,target:5}}},ft=(a,t)=>{const s=`gameApp_achievements_${a}`;localStorage.setItem(s,JSON.stringify(t))},me=(a,t,s={})=>{const r=Ie(a);r[t]={...r[t],unlocked:!0,date:new Date().toISOString(),...s},ft(a,r)},Me=()=>[{id:"starter_1",name:"Picture 1",url:"/images/img1.jpg",fallback:"https://picsum.photos/400/400?random=1"},{id:"starter_2",name:"Picture 2",url:"/images/img2.jpg",fallback:"https://picsum.photos/400/400?random=2"},{id:"starter_3",name:"Picture 3",url:"/images/img3.jpg",fallback:"https://picsum.photos/400/400?random=3"},{id:"starter_4",name:"Picture 4",url:"/images/img4.jpg",fallback:"https://picsum.photos/400/400?random=4"},{id:"starter_5",name:"Picture 5",url:"/images/img5.jpg",fallback:"https://picsum.photos/400/400?random=5"},{id:"starter_6",name:"Picture 6",url:"/images/img6.jpg",fallback:"https://picsum.photos/400/400?random=6"}],jt=a=>{const t=U(a);let s=t.themes.find(i=>i.id==="starter");s||(s={id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"},t.themes.push(s));const r=Me();s.images=r.map(i=>({...i,createdAt:new Date().toISOString()})),le(a,t),ut(a),me(a,"starterPack")},Ee=n.createContext(),Z=()=>{const a=n.useContext(Ee);if(!a)throw new Error("useLanguage must be used within a LanguageProvider");return a},ie={id:{appTitle:"KidzPlay AI",welcomeBack:"Selamat datang kembali, {name}! 🌟",searchGames:"Cari permainan...",myPictureCollection:"📸 KOLEKSI GAMBARKU ({count})",myThemeCollections:"🎨 KOLEKSI TEMA GAMBARKU",organizeYourPictures:"Atur gambar-gambarmu berdasarkan tema",generateNewImage:"BUAT GAMBAR BARU",viewByThemes:"Lihat berdasarkan tema",seeAll:"Lihat semua →",createNewTheme:"Buat Tema Baru",themeName:"Nama Tema",themeDescription:"Deskripsi Tema",enterThemeName:"Masukkan nama tema...",enterThemeDescription:"Ceritakan tentang tema ini...",optional:"opsional",cancel:"Batal",create:"Buat",pictures:"gambar",generate:"Buat",noImagesYet:"Belum ada gambar",selectTheme:"Pilih Tema",createAmazingPictures:"Buat gambar menakjub dengan AI",describeYourPicture:"Ceritakan gambar yang kamu inginkan",promptPlaceholder:"Contoh: Seekor kucing lucu bermain di taman yang penuh bunga warna-warni...",generating:"Sedang membuat",generateImage:"Buat Gambar",generateAnother:"Buat Lagi",saveToCollection:"Simpan ke Koleksi",backToDashboard:"Kembali ke Dashboard",backToMenu:"Kembali ke Menu",chooseDifficulty:"Pilih Tingkat Kesulitan",easy:"Mudah",medium:"Sedang",hard:"Sulit",level:"Level",bestScore:"Skor Terbaik",gamesPlayed:"Permainan Dimainkan",startGame:"Mulai Permainan",playAgain:"Main Lagi",restart:"Mulai Ulang",moves:"Langkah",time:"Waktu",score:"Skor",finalScore:"Skor Akhir",congratulations:"Selamat!",needMoreImages:"Butuh lebih banyak gambar",pairs:"pasang",memoryGameCompleted:"Kamu berhasil menyelesaikan permainan memori!",puzzlesCompleted:"Puzzle Selesai",bestTime:"Waktu Terbaik",availableImages:"Gambar Tersedia",pieces:"potongan",chooseImage:"Pilih Gambar",noImagesAvailable:"Tidak ada gambar tersedia",generateSomeImages:"Buat beberapa gambar terlebih dahulu!",puzzleBoard:"Papan Puzzle",puzzlePieces:"Potongan Puzzle",workspace:"Area Kerja",shuffle:"Acak",puzzleCompleted:"Puzzle Selesai!",greatJob:"Kerja bagus! Kamu berhasil menyelesaikan puzzle!",completionTime:"Waktu Selesai",selectImageForPuzzle:"Pilih gambar untuk puzzle",selectYourImage:"Pilih Gambarmu",dragPiecesHere:"Seret potongan puzzle ke sini untuk menyusunnya",dragToWorkspace:"Seret ke area kerja untuk menyusun puzzle",puzzlesSolved:"Puzzle Terpecahkan",lastDifficulty:"Kesulitan Terakhir",grid:"kotak",howToPlay:"Cara Bermain",slidingRule1:"Klik angka yang bersebelahan dengan kotak kosong untuk memindahkannya",slidingRule2:"Susun angka dari 1 hingga terakhir secara berurutan",slidingRule3:"Kotak kosong harus berada di pojok kanan bawah",puzzleSolved:"Puzzle Terpecahkan!",excellentWork:"Kerja yang luar biasa! Kamu berhasil menyelesaikan sliding puzzle!",startPlaying:"🎮 MULAI BERMAIN",memoryCardGame:"Permainan Kartu Memori",memoryCardDesc:"Cocokkan pasangan kartu",puzzleGame:"Permainan Puzzle",puzzleDesc:"Seret & lepas potongan puzzle",slidingPuzzle:"Puzzle Geser",slidingDesc:"Geser angka untuk menyelesaikan",start:"MULAI",continue:"LANJUTKAN",new:"BARU!",levelShort:"LV",yourAchievements:"🏆 PENCAPAIANMU",starterTitle:"🎁 Pemula",starterDesc:"Membuka paket kartu pertama!",artistTitle:"🎨 Seniman",artistDesc:"Buat 5 gambar",geniusTitle:"🧠 Jenius",geniusDesc:"Selesaikan 10 permainan",unlocked:"✅ TERBUKA",locked:"⏳ TERKUNCI",progress:"Progres",language:"Bahasa",indonesian:"Indonesia",english:"English"},en:{appTitle:"KidzPlay AI",welcomeBack:"Welcome back, {name}! 🌟",searchGames:"Search games...",myPictureCollection:"📸 MY PICTURE COLLECTION ({count})",myThemeCollections:"🎨 MY THEME COLLECTIONS",organizeYourPictures:"Organize your pictures by themes",generateNewImage:"GENERATE NEW IMAGE",viewByThemes:"View by themes",seeAll:"See all →",createNewTheme:"Create New Theme",themeName:"Theme Name",themeDescription:"Theme Description",enterThemeName:"Enter theme name...",enterThemeDescription:"Tell us about this theme...",optional:"optional",cancel:"Cancel",create:"Create",pictures:"pictures",generate:"Generate",noImagesYet:"No images yet",selectTheme:"Select Theme",createAmazingPictures:"Create amazing pictures with AI",describeYourPicture:"Describe the picture you want",promptPlaceholder:"Example: A cute cat playing in a garden full of colorful flowers...",generating:"Generating",generateImage:"Generate Image",generateAnother:"Generate Another",saveToCollection:"Save to Collection",backToDashboard:"Back to Dashboard",backToMenu:"Back to Menu",chooseDifficulty:"Choose Difficulty",easy:"Easy",medium:"Medium",hard:"Hard",level:"Level",bestScore:"Best Score",gamesPlayed:"Games Played",startGame:"Start Game",playAgain:"Play Again",restart:"Restart",moves:"Moves",time:"Time",score:"Score",finalScore:"Final Score",congratulations:"Congratulations!",needMoreImages:"Need more images",pairs:"pairs",memoryGameCompleted:"You completed the memory game!",puzzlesCompleted:"Puzzles Completed",bestTime:"Best Time",availableImages:"Available Images",pieces:"pieces",chooseImage:"Choose Image",noImagesAvailable:"No images available",generateSomeImages:"Generate some images first!",puzzleBoard:"Puzzle Board",puzzlePieces:"Puzzle Pieces",workspace:"Workspace",shuffle:"Shuffle",puzzleCompleted:"Puzzle Completed!",greatJob:"Great job! You completed the puzzle!",completionTime:"Completion Time",selectImageForPuzzle:"Select image for puzzle",selectYourImage:"Select Your Image",dragPiecesHere:"Drag puzzle pieces here to assemble them",dragToWorkspace:"Drag to workspace to assemble puzzle",puzzlesSolved:"Puzzles Solved",lastDifficulty:"Last Difficulty",grid:"grid",howToPlay:"How to Play",slidingRule1:"Click numbers adjacent to the empty space to move them",slidingRule2:"Arrange numbers from 1 to last in order",slidingRule3:"Empty space should be at bottom right corner",puzzleSolved:"Puzzle Solved!",excellentWork:"Excellent work! You solved the sliding puzzle!",startPlaying:"🎮 START PLAYING",memoryCardGame:"Memory Card Game",memoryCardDesc:"Match pairs of cards",puzzleGame:"Puzzle Game",puzzleDesc:"Drag & drop puzzle pieces",slidingPuzzle:"Sliding Puzzle",slidingDesc:"Slide numbers to solve",start:"START",continue:"CONTINUE",new:"NEW!",levelShort:"LV",yourAchievements:"🏆 YOUR ACHIEVEMENTS",starterTitle:"🎁 Starter",starterDesc:"Opened first card pack!",artistTitle:"🎨 Artist",artistDesc:"Generate 5 images",geniusTitle:"🧠 Genius",geniusDesc:"Complete 10 games",unlocked:"✅ UNLOCKED",locked:"⏳ LOCKED",progress:"Progress",language:"Language",indonesian:"Indonesia",english:"English"}},wt=({children:a})=>{const[t,s]=n.useState("id");n.useEffect(()=>{const l=localStorage.getItem("kidzplay-language");l&&ie[l]&&s(l)},[]);const r=l=>{ie[l]&&(s(l),localStorage.setItem("kidzplay-language",l))},i=(l,v={})=>{let j=ie[t][l]||ie.id[l]||l;return Object.keys(v).forEach(P=>{j=j.replace(`{${P}}`,v[P])}),j};return e.jsx(Ee.Provider,{value:{language:t,changeLanguage:r,t:i},children:a})},vt=()=>{const{language:a,changeLanguage:t,t:s}=Z(),r=i=>{b(),t(i)};return e.jsxs("div",{className:"relative group",children:[e.jsxs("button",{className:"flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-white hover:bg-white/30 transition-colors",children:[e.jsx(_e,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm font-medium",children:a==="id"?"ID":"EN"})]}),e.jsxs("div",{className:"absolute right-0 top-full mt-2 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50",children:[e.jsxs("button",{onClick:()=>r("id"),className:`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${a==="id"?"bg-blue-100 text-blue-800":"text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:"🇮🇩"}),e.jsx("span",{className:"font-medium",children:s("indonesian")}),a==="id"&&e.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]}),e.jsxs("button",{onClick:()=>r("en"),className:`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${a==="en"?"bg-blue-100 text-blue-800":"text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:"🇺🇸"}),e.jsx("span",{className:"font-medium",children:s("english")}),a==="en"&&e.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]})]})]})},yt=({childName:a,onGenerateImage:t,onImageSelect:s})=>{const[r,i]=n.useState({themes:[]}),[l,v]=n.useState(null),[j,P]=n.useState(""),[T,D]=n.useState(""),[h,M]=n.useState(!1),{t:u}=Z();n.useEffect(()=>{a&&i(U(a))},[a]);const $=()=>{j.trim()&&(b(),Ae(a,j.trim(),T.trim()),i(U(a)),P(""),D(""),M(!1))},g=(m,A)=>{A.trim()&&(b(),xt(a,m,A.trim()),i(U(a)),v(null))},V=m=>{b(),ht(a,m),i(U(a))},E=(m,A)=>{b(),t&&t(m,A)},_=r.themes.reduce((m,A)=>m+A.images.length,0);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-3xl font-bold text-white flex items-center gap-2",children:[e.jsx(xe,{className:"w-8 h-8"}),u("myThemeCollections")," (",_,")"]}),e.jsx("p",{className:"text-white/80 mt-1",children:u("organizeYourPictures")})]}),e.jsxs("button",{onClick:()=>{b(),M(!0)},className:"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-6 py-3 rounded-2xl font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center gap-2",children:[e.jsx(oe,{className:"w-5 h-5"}),u("createNewTheme")]})]}),h&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:u("createNewTheme")}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-2",children:u("themeName")}),e.jsx("input",{type:"text",value:j,onChange:m=>P(m.target.value),placeholder:u("enterThemeName"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none",maxLength:30})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-bold text-gray-700 mb-2",children:[u("themeDescription")," (",u("optional"),")"]}),e.jsx("textarea",{value:T,onChange:m=>D(m.target.value),placeholder:u("enterThemeDescription"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none",rows:3,maxLength:100})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{onClick:()=>{b(),M(!1),P(""),D("")},className:"flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:u("cancel")}),e.jsx("button",{onClick:$,disabled:!j.trim(),className:"flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:u("create")})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.themes.map(m=>e.jsxs("div",{className:`bg-gradient-to-br ${m.color} rounded-3xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 overflow-hidden`,children:[e.jsxs("div",{className:"p-6 text-white",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[l===m.id?e.jsx("input",{type:"text",defaultValue:m.name,onBlur:A=>g(m.id,A.target.value),onKeyPress:A=>{A.key==="Enter"&&g(m.id,A.target.value)},className:"bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 rounded-lg px-3 py-1 text-xl font-bold focus:outline-none focus:ring-2 focus:ring-white/50",autoFocus:!0}):e.jsx("h3",{className:"text-xl font-bold truncate",children:m.name}),e.jsx("p",{className:"text-white/80 text-sm mt-1 line-clamp-2",children:m.description})]}),e.jsx("div",{className:"flex gap-2 ml-3",children:!m.isDefault&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>{b(),v(l===m.id?null:m.id)},className:"p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors",children:e.jsx(Ue,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>V(m.id),className:"p-2 rounded-lg bg-white/20 hover:bg-red-500/50 transition-colors",children:e.jsx(Ke,{className:"w-4 h-4"})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-white/90 text-sm font-medium",children:[m.images.length," ",u("pictures")]}),e.jsxs("button",{onClick:()=>E(m.id,m.name),className:"bg-white/20 hover:bg-white/30 backdrop-blur-sm px-3 py-1 rounded-lg text-sm font-bold transition-colors flex items-center gap-1",children:[e.jsx(oe,{className:"w-4 h-4"}),u("generate")]})]})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm p-4",children:m.images.length>0?e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[m.images.slice(0,6).map((A,G)=>e.jsx("div",{className:"aspect-square rounded-lg overflow-hidden cursor-pointer hover:scale-105 transition-transform",onClick:()=>{b(),s&&s(A)},children:e.jsx("img",{src:A.url||A.dataUrl,alt:A.name,className:"w-full h-full object-cover"})},A.id)),m.images.length>6&&e.jsxs("div",{className:"aspect-square rounded-lg bg-white/20 flex items-center justify-center text-white font-bold text-sm",children:["+",m.images.length-6]})]}):e.jsxs("div",{className:"aspect-square rounded-lg border-2 border-dashed border-white/30 flex flex-col items-center justify-center text-white/60",children:[e.jsx(Ye,{className:"w-8 h-8 mb-2"}),e.jsx("span",{className:"text-sm font-medium",children:u("noImagesYet")})]})})]},m.id))})]})},Nt=({childName:a,onClose:t,onImageGenerated:s,selectedThemeId:r=null,selectedThemeName:i=null})=>{const[l,v]=n.useState(""),[j,P]=n.useState(!1),[T,D]=n.useState(null),[h,M]=n.useState(r||"generated"),[u,$]=n.useState(""),[g,V]=n.useState(!1),[E,_]=n.useState({themes:[]}),{t:m}=Z();n.useEffect(()=>{a&&_(U(a)),i&&i!=="My Creations"&&v(`${i} themed picture for kids`)},[a,i]);const A=async()=>{if(l.trim()){P(!0),b();try{const k=await fetch("/api/generate-image",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:`${l}, child-friendly, colorful, cartoon style, safe for kids aged 3-9`,model:"fal-ai/minimax/image-01"})});if(k.ok){const L=await k.json();D({dataUrl:L.imageUrl,prompt:l}),ee()}else setTimeout(()=>{D({dataUrl:`https://picsum.photos/512/512?random=${Date.now()}`,prompt:l}),ee()},2e3)}catch(k){console.error("Error generating image:",k),setTimeout(()=>{D({dataUrl:`https://picsum.photos/512/512?random=${Date.now()}`,prompt:l}),ee()},2e3)}finally{P(!1)}}},G=()=>{if(u.trim()){b();const k=Ae(a,u.trim(),`Pictures about ${u}`,l);_(U(a)),M(k),$(""),V(!1)}},q=()=>{if(!T)return;b();const k={name:`${l.slice(0,30)}...`,dataUrl:T.dataUrl,prompt:T.prompt};h!=="new"&&(gt(a,k,h),s&&s(),t())},d=E.themes||[];return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-2xl w-full shadow-2xl max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl",children:e.jsx(qe,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:m("generateNewImage")}),e.jsx("p",{className:"text-gray-600",children:m("createAmazingPictures")})]})]}),e.jsx("button",{onClick:()=>{b(),t()},className:"p-2 rounded-xl hover:bg-gray-100 transition-colors",children:e.jsx(We,{className:"w-6 h-6 text-gray-500"})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-3",children:m("selectTheme")}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[d.map(k=>e.jsx("button",{onClick:()=>{b(),M(k.id)},className:`p-4 rounded-xl border-2 transition-all duration-200 text-left ${h===k.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-4 h-4 rounded-full bg-gradient-to-r ${k.color}`}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-gray-800",children:k.name}),e.jsxs("div",{className:"text-xs text-gray-500",children:[k.images.length," ",m("pictures")]})]})]})},k.id)),e.jsxs("button",{onClick:()=>{b(),V(!0)},className:"p-4 rounded-xl border-2 border-dashed border-gray-300 hover:border-blue-500 transition-all duration-200 flex items-center justify-center gap-2 text-gray-600 hover:text-blue-600",children:[e.jsx(oe,{className:"w-5 h-5"}),e.jsx("span",{className:"font-bold",children:m("createNewTheme")})]})]})]}),g&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60",children:e.jsxs("div",{className:"bg-white rounded-2xl p-6 max-w-md w-full shadow-xl",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:m("createNewTheme")}),e.jsx("input",{type:"text",value:u,onChange:k=>$(k.target.value),placeholder:m("enterThemeName"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none mb-4",maxLength:30}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{b(),V(!1),$("")},className:"flex-1 px-4 py-2 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:m("cancel")}),e.jsx("button",{onClick:G,disabled:!u.trim(),className:"flex-1 px-4 py-2 rounded-xl bg-blue-500 text-white font-bold hover:bg-blue-600 transition-colors disabled:opacity-50",children:m("create")})]})]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-3",children:m("describeYourPicture")}),e.jsx("textarea",{value:l,onChange:k=>v(k.target.value),placeholder:m("promptPlaceholder"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none",rows:4,maxLength:200}),e.jsxs("div",{className:"text-right text-xs text-gray-500 mt-1",children:[l.length,"/200"]})]}),e.jsx("button",{onClick:A,disabled:!l.trim()||j,className:"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 mb-6",children:j?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-white"}),m("generating"),"..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ke,{className:"w-6 h-6"}),m("generateImage")]})}),T&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-6 text-center",children:[e.jsx("img",{src:T.dataUrl,alt:"Generated",className:"w-full max-w-md mx-auto rounded-xl shadow-lg"}),e.jsxs("p",{className:"text-sm text-gray-600 mt-3 italic",children:['"',T.prompt,'"']})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{b(),D(null)},className:"flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:m("generateAnother")}),e.jsx("button",{onClick:q,className:"flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold hover:shadow-lg transition-all duration-200",children:m("saveToCollection")})]})]})]})})},kt=({childName:a})=>{const[t,s]=n.useState({themes:[]}),[r,i]=n.useState({}),[l,v]=n.useState({}),[j,P]=n.useState(!1),[T,D]=n.useState(!1),[h,M]=n.useState(null),[u,$]=n.useState(null),{t:g}=Z(),V=ce();n.useEffect(()=>{a&&(s(U(a)),i(ne(a)),v(Ie(a)))},[a]);const E=t.themes?.reduce((d,k)=>[...d,...k.images],[])||[],_=E.length,m=(d=null,k=null)=>{M(d),$(k),D(!0)},A=()=>{s(U(a))},G=[{id:"memory",title:g("memoryCardGame"),icon:"🃏",description:g("memoryCardDesc"),progress:r.memoryCard,color:"from-blue-500 to-purple-600",thumbnail:"/images/memory-card.jpg"},{id:"puzzle",title:g("puzzleGame"),icon:"🧩",description:g("puzzleDesc"),progress:r.puzzle,color:"from-green-500 to-teal-600",thumbnail:"/images/puzzle.jpg"},{id:"sliding",title:g("slidingPuzzle"),icon:"🔄",description:g("slidingDesc"),progress:r.slidingPuzzle,color:"from-orange-500 to-red-600",thumbnail:"/images/sliding-puzzle.jpg"}],q=[{key:"starterPack",title:g("starterTitle"),description:g("starterDesc"),unlocked:l.starterPack?.unlocked},{key:"artist",title:g("artistTitle"),description:g("artistDesc"),unlocked:l.artist?.unlocked,progress:l.artist?.progress,target:l.artist?.target},{key:"genius",title:g("geniusTitle"),description:g("geniusDesc"),unlocked:l.genius?.unlocked,progress:l.genius?.progress,target:l.genius?.target}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 landscape-optimized portrait-optimized",children:e.jsxs("div",{className:"dashboard-container",children:[e.jsxs("header",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-8 bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 landscape-header",children:[e.jsxs("div",{className:"mb-4 sm:mb-0",children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-white flex items-center gap-2 landscape-text-lg",children:["🏠 ",g("appTitle")]}),e.jsx("p",{className:"text-white/80 text-base sm:text-lg landscape-text-sm",children:g("welcomeBack",{name:a})})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4 w-full sm:w-auto",children:[e.jsx(vt,{}),e.jsxs("div",{className:"relative flex-1 sm:flex-none",children:[e.jsx(Fe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("input",{type:"text",placeholder:g("searchGames"),className:"w-full sm:w-auto pl-8 sm:pl-10 pr-4 py-2 text-sm sm:text-base rounded-full bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"})]})]})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2 sm:gap-0",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:g("myPictureCollection",{count:_})}),e.jsxs("div",{className:"flex gap-2 sm:gap-3 text-sm sm:text-base",children:[e.jsxs("button",{onClick:()=>{b(),P(!0)},className:"text-white/80 hover:text-white transition-colors flex items-center gap-1 sm:gap-2",children:[e.jsx(xe,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"hidden sm:inline",children:g("viewByThemes")}),e.jsx("span",{className:"sm:hidden",children:"Tema"})]}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors",children:g("seeAll")})]})]}),e.jsxs("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-4",children:[E.slice(0,5).map(d=>e.jsxs("div",{className:"bg-white rounded-xl sm:rounded-2xl shadow-lg p-2 sm:p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer",onClick:()=>b(),children:[e.jsx("img",{src:d.url||d.dataUrl,alt:d.name,className:"w-full aspect-square object-cover rounded-lg sm:rounded-xl mb-1 sm:mb-2"}),e.jsx("p",{className:"text-xs font-bold text-gray-700 text-center truncate landscape-text-sm",children:d.name})]},d.id)),e.jsxs("div",{className:"bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl sm:rounded-2xl shadow-lg p-2 sm:p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer flex flex-col items-center justify-center text-white min-h-[80px] sm:min-h-[100px]",onClick:()=>m(),children:[e.jsx(oe,{className:"w-6 h-6 sm:w-8 sm:h-8 mb-1"}),e.jsx("p",{className:"text-xs font-bold text-center landscape-text-sm",children:g("generateNewImage")})]})]})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:g("startPlaying")}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors text-sm sm:text-base",children:g("seeAll")})]}),e.jsx("div",{className:"responsive-grid landscape-games-grid portrait-games-grid",children:G.map(d=>e.jsxs("div",{className:`relative aspect-square landscape-game-card portrait-game-card bg-gradient-to-br ${d.color} rounded-xl sm:rounded-2xl shadow-lg text-white hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer overflow-hidden`,onClick:k=>{k.stopPropagation(),b(),V(`/game/${d.id}`)},children:[e.jsx("div",{className:"absolute inset-0 opacity-20",children:e.jsx("img",{src:d.thumbnail,alt:d.title,className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"relative z-10 h-full p-3 sm:p-6 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-4",children:[e.jsx("div",{className:"text-2xl sm:text-3xl",children:d.icon}),e.jsx("div",{className:"text-xs bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full font-bold landscape-text-sm",children:d.progress?.gamesPlayed>0?`${g("level")} ${d.progress.level||1}`:g("new")})]}),e.jsxs("div",{className:"flex-1 flex flex-col justify-center text-center",children:[e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-1 sm:mb-2 landscape-text-sm",children:d.title}),e.jsx("p",{className:"text-white/80 text-xs sm:text-sm mb-2 sm:mb-4 landscape-text-sm hidden sm:block",children:d.description})]}),e.jsxs("div",{className:"flex flex-col items-center space-y-1 sm:space-y-2",children:[d.progress?.gamesPlayed>0&&e.jsx("div",{className:"flex justify-center",children:[...Array(3)].map((k,L)=>e.jsx(Te,{className:"w-3 h-3 sm:w-4 sm:h-4 text-yellow-300 fill-current"},L))}),e.jsx("button",{className:"bg-white/30 backdrop-blur-sm hover:bg-white/40 rounded-full px-4 sm:px-6 py-1 sm:py-2 text-xs sm:text-sm font-bold transition-colors landscape-text-sm",onClick:k=>{k.stopPropagation(),b(),V(`/game/${d.id}`)},children:g("start")})]})]})]},d.id))})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:g("yourAchievements")}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors text-sm sm:text-base",children:g("seeAll")})]}),e.jsx("div",{className:"responsive-grid",children:q.map(d=>e.jsx("div",{className:`bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 text-white border-2 ${d.unlocked?"border-yellow-400":"border-white/20"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl sm:text-3xl mb-2 sm:mb-3",children:d.title.split(" ")[0]}),e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-1 sm:mb-2 landscape-text-sm",children:d.title.split(" ").slice(1).join(" ")}),e.jsx("p",{className:"text-white/80 mb-3 sm:mb-4 text-xs sm:text-sm landscape-text-sm",children:d.description}),d.unlocked?e.jsx("div",{className:"bg-yellow-400 text-yellow-900 rounded-full px-4 py-2 text-sm font-bold",children:g("unlocked")}):d.progress!==void 0?e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm",children:[g("progress"),": ",d.progress,"/",d.target]}),e.jsx("div",{className:"bg-white/20 rounded-full h-2",children:e.jsx("div",{className:"bg-yellow-400 rounded-full h-2 transition-all duration-300",style:{width:`${d.progress/d.target*100}%`}})})]}):e.jsx("div",{className:"bg-white/20 rounded-full px-4 py-2 text-sm font-bold",children:g("locked")})]})},d.key))})]}),j&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 rounded-3xl p-8 max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-3xl font-bold text-white",children:g("myThemeCollections")}),e.jsx("button",{onClick:()=>{b(),P(!1)},className:"text-white/80 hover:text-white text-2xl font-bold",children:"✕"})]}),e.jsx(yt,{childName:a,onGenerateImage:m,onImageSelect:()=>{}})]})}),T&&e.jsx(Nt,{childName:a,selectedThemeId:h,selectedThemeName:u,onClose:()=>{D(!1),M(null),$(null)},onImageGenerated:A}),e.jsx("div",{className:"landscape-hint",children:"📱 Putar layar untuk pengalaman bermain yang lebih baik!"})]})})},Tt=({delay:a=0})=>e.jsx("div",{className:"absolute pointer-events-none",style:{animation:`sparkle 2s ease-in-out ${a}s infinite`,left:`${Math.random()*100}%`,top:`${Math.random()*100}%`},children:e.jsx("div",{className:"text-yellow-400 text-2xl animate-pulse",children:"✨"})}),St=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState([]),[l,v]=n.useState([]),[j,P]=n.useState([]),[T,D]=n.useState([]),[h,M]=n.useState(0),[u,$]=n.useState(0),[g,V]=n.useState(null),[E,_]=n.useState("easy"),[m,A]=n.useState([]),[G,q]=n.useState({}),[d,k]=n.useState(0),[L,H]=n.useState(null),{t:I}=Z(),z=ce(),Y={easy:{pairs:6,gridCols:4,gridRows:3,baseScore:1e3,perfectTime:60},medium:{pairs:8,gridCols:4,gridRows:4,baseScore:1500,perfectTime:90},hard:{pairs:12,gridCols:6,gridRows:4,baseScore:2e3,perfectTime:120}};n.useEffect(()=>{if(a){const o=U(a).themes?.reduce((p,f)=>[...p,...f.images],[])||[];A(o);const x=ne(a);q(x.memoryCard||{level:1,bestScore:null,gamesPlayed:0})}},[a]),n.useEffect(()=>{let w=null;return t==="playing"&&g&&(w=setInterval(()=>{$(Math.floor((Date.now()-g)/1e3))},1e3)),()=>{w&&clearInterval(w)}},[t,g]),n.useEffect(()=>{if(l.length===2){const[w,o]=l;r[w].imageId===r[o].imageId?(lt(),D([w,o]),setTimeout(()=>{nt()},200),setTimeout(()=>{D([])},2e3),P(x=>[...x,w,o]),v([]),k(x=>x+100)):setTimeout(()=>{v([])},1e3),M(x=>x+1)}},[l,r,u]),n.useEffect(()=>{if(j.length===r.length&&r.length>0&&t==="playing"){const w=Y[E],o=d,x=Math.max(0,Math.floor((w.perfectTime-u)*10)),p=w.pairs,f=Math.max(0,(p*2-h)*20),C=w.baseScore;let c=o+x+f+C;c=Math.max(c,100),H({baseScore:o,timeBonus:x,moveBonus:f,difficultyBonus:C,finalScore:c}),s("completed"),k(c);const y={...G,gamesPlayed:(G.gamesPlayed||0)+1,bestScore:Math.max(G.bestScore||0,c),level:Math.min((G.level||1)+(c>(G.bestScore||0)?1:0),10)};be(a,"memoryCard",y),q(y),y.gamesPlayed>=5&&me(a,"memoryMaster")}},[j.length,r.length,t]);const K=w=>{const o=[...w];for(let x=o.length-1;x>0;x--){const p=Math.floor(Math.random()*(x+1));[o[x],o[p]]=[o[p],o[x]]}return o},X=n.useCallback(()=>{if(m.length<Y[E].pairs){alert(I("needMoreImages"));return}b();const o=K(m).slice(0,Y[E].pairs).flatMap((p,f)=>[{id:f*2,imageId:p.id,image:p},{id:f*2+1,imageId:p.id,image:p}]),x=K(o);i(x),v([]),P([]),D([]),M(0),$(0),k(0),H(null),V(Date.now()),s("playing")},[m,E,I]),te=w=>{t!=="playing"||l.length>=2||l.includes(w)||j.includes(w)||(Pe(),v(o=>[...o,w]))},se=()=>{b(),s("menu"),i([]),v([]),P([]),D([]),M(0),$(0),k(0),H(null),V(null)},J=w=>{const o=Math.floor(w/60),x=w%60;return`${o}:${x.toString().padStart(2,"0")}`};return t==="menu"?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-2 sm:p-4 landscape-optimized",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-4 sm:mb-8 gap-4 sm:gap-0",children:[e.jsxs("button",{onClick:()=>{b(),z("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:I("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-xl sm:text-3xl font-bold text-white mb-1 sm:mb-2 landscape-text-lg",children:["🃏 ",I("memoryCardGame")]}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base landscape-text-sm hidden sm:block",children:I("memoryCardDesc")})]}),e.jsx("div",{className:"w-8 sm:w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-4 sm:mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-3 sm:gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:G.level||1}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:I("level")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:G.bestScore||0}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:I("bestScore")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:G.gamesPlayed||0}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:I("gamesPlayed")})]})]})}),e.jsxs("div",{className:"bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-lg sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6 text-center landscape-text-base",children:I("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mb-4 sm:mb-8",children:Object.entries(Y).map(([w,o])=>e.jsx("button",{onClick:()=>{b(),_(w)},className:`p-3 sm:p-6 rounded-xl sm:rounded-2xl border-2 transition-all duration-200 ${E===w?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl sm:text-2xl mb-1 sm:mb-2",children:w==="easy"?"😊":w==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-1 sm:mb-2 text-sm sm:text-base landscape-text-sm",children:I(w)}),e.jsxs("div",{className:"text-xs sm:text-sm text-gray-600 landscape-text-sm",children:[o.pairs," ",I("pairs")," • ",o.gridCols,"×",o.gridRows]})]})},w))}),e.jsxs("div",{className:"text-center",children:[e.jsx("button",{onClick:X,disabled:m.length<Y[E].pairs,className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:I("startGame")}),m.length<Y[E].pairs&&e.jsxs("p",{className:"text-red-500 text-sm mt-2",children:[I("needMoreImages")," (",m.length,"/",Y[E].pairs,")"]})]})]})]})}):e.jsxs("div",{className:"min-h-screen p-2 sm:p-4 relative landscape-optimized",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/30 via-purple-500/30 to-pink-500/30"}),e.jsx("style",{jsx:!0,children:`
        @keyframes sparkle {
          0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
          }
        }

        @keyframes celebration {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        .celebration-card {
          animation: celebration 0.6s ease-in-out;
        }

        .sparkle-container {
          position: absolute;
          inset: 0;
          pointer-events: none;
          overflow: hidden;
          border-radius: 1rem;
        }
      `}),e.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-3 sm:mb-6 gap-3 sm:gap-0",children:[e.jsxs("button",{onClick:()=>{b(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:I("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl px-3 sm:px-6 py-2 sm:py-3",children:[e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(ge,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:J(u)})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(He,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsxs("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:[h," ",e.jsx("span",{className:"hidden sm:inline",children:I("moves")})]})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(Te,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:d})]})]}),e.jsxs("button",{onClick:se,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:I("restart")})]})]}),e.jsx("div",{className:"grid gap-2 sm:gap-4 lg:gap-6 mx-auto",style:{gridTemplateColumns:`repeat(${Y[E].gridCols}, 1fr)`,maxWidth:`${Y[E].gridCols*(window.innerWidth<640?80:window.innerWidth<1024?120:160)}px`},children:r.map((w,o)=>{const x=l.includes(o)||j.includes(o),p=T.includes(o);return e.jsx("div",{onClick:()=>te(o),className:`aspect-square cursor-pointer transition-all duration-300 transform hover:scale-105 ${p?"celebration-card":""}`,style:{perspective:"1000px"},children:e.jsxs("div",{className:`
                    relative w-full h-full transition-transform duration-700
                    ${x?"rotate-y-180":""}
                    ${j.includes(o)?"ring-4 ring-green-400 rounded-2xl":""}
                  `,style:{transformStyle:"preserve-3d",transform:x?"rotateY(180deg)":"rotateY(0deg)"},children:[p&&e.jsx("div",{className:"sparkle-container",children:[...Array(8)].map((f,C)=>e.jsx(Tt,{delay:C*.2},C))}),e.jsx("div",{className:"absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg",style:{backfaceVisibility:"hidden"},children:e.jsx("div",{className:"text-3xl sm:text-4xl lg:text-6xl drop-shadow-lg",children:"🎈"})}),e.jsx("div",{className:"absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-white p-1 sm:p-2 shadow-lg",style:{backfaceVisibility:"hidden",transform:"rotateY(180deg)"},children:e.jsx("img",{src:w.image.url||w.image.dataUrl,alt:w.image.name,className:"w-full h-full object-cover rounded-lg sm:rounded-xl"})})]})},w.id)})}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:I("congratulations")}),e.jsx("p",{className:"text-gray-600 mb-6",children:I("memoryGameCompleted")}),e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:J(u)}),e.jsx("div",{className:"text-sm text-gray-600",children:I("time")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:h}),e.jsx("div",{className:"text-sm text-gray-600",children:I("moves")})]})]}),L&&e.jsx("div",{className:"border-t pt-4",children:e.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Pasangan ditemukan:"}),e.jsxs("span",{className:"font-bold",children:["+",L.baseScore]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus waktu:"}),e.jsxs("span",{className:"font-bold text-blue-600",children:["+",L.timeBonus]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus efisiensi:"}),e.jsxs("span",{className:"font-bold text-green-600",children:["+",L.moveBonus]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus tingkat kesulitan:"}),e.jsxs("span",{className:"font-bold text-orange-600",children:["+",L.difficultyBonus]})]}),e.jsxs("div",{className:"border-t pt-2 flex justify-between text-lg font-bold",children:[e.jsx("span",{children:"Total Skor:"}),e.jsx("span",{className:"text-purple-600",children:L.finalScore})]})]})})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:X,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:I("playAgain")}),e.jsx("button",{onClick:()=>{b(),z("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:I("backToDashboard")})]})]})})]})]})},Ct=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState(null),[l,v]=n.useState(0),[j,P]=n.useState(null),[T,D]=n.useState("easy"),[h,M]=n.useState([]),[u,$]=n.useState({}),[g,V]=n.useState(0),[E,_]=n.useState([]),[m,A]=n.useState(null),[G,q]=n.useState(null),[d,k]=n.useState({x:0,y:0}),[L,H]=n.useState(window.innerWidth<640),I=n.useRef(null),{t:z}=Z(),Y=ce(),K={easy:{pieces:4,cols:2,rows:2,timeBonus:300},medium:{pieces:9,cols:3,rows:3,timeBonus:200},hard:{pieces:16,cols:4,rows:4,timeBonus:100}};n.useEffect(()=>{if(a){const y=U(a).themes?.reduce((O,S)=>[...O,...S.images],[])||[];M(y);const N=ne(a);$(N.puzzle||{completed:0,favoriteImage:null,bestTime:null})}},[a]),n.useEffect(()=>{let c=null;return t==="playing"&&j&&(c=setInterval(()=>{v(Math.floor((Date.now()-j)/1e3))},1e3)),()=>{c&&clearInterval(c)}},[t,j]),n.useEffect(()=>{const c=()=>{H(window.innerWidth<640)};return window.addEventListener("resize",c),()=>window.removeEventListener("resize",c)},[]);const X=c=>{console.log("Initializing simple puzzle with image:",c);const{cols:y,rows:N}=K[T],O=[];for(let B=0;B<N;B++)for(let R=0;R<y;R++){const W=B*y+R;O.push({id:W,row:B,col:R,currentRow:B,currentCol:R,correctPosition:{row:B,col:R},isPlaced:!1,image:c.url||c.dataUrl})}const S=[...O];for(let B=S.length-1;B>0;B--){const R=Math.floor(Math.random()*(B+1)),W=S[B].currentRow,De=S[B].currentCol;S[B].currentRow=S[R].currentRow,S[B].currentCol=S[R].currentCol,S[R].currentRow=W,S[R].currentCol=De}_(S),console.log("Puzzle pieces created:",S)},te=(c,y)=>{A(y);const N=c.target.getBoundingClientRect();k({x:c.clientX-N.left,y:c.clientY-N.top})},se=c=>{c.preventDefault()},J=(c,y,N)=>{if(c.preventDefault(),!m)return;const O=E.find(S=>S.currentRow===y&&S.currentCol===N);if(O&&O.id!==m.id){const S=E.map(B=>B.id===m.id?{...B,currentRow:y,currentCol:N}:B.id===O.id?{...B,currentRow:m.currentRow,currentCol:m.currentCol}:B);_(S),b(),w(S)}A(null)},w=c=>{c.every(N=>N.currentRow===N.correctPosition.row&&N.currentCol===N.correctPosition.col)&&setTimeout(()=>{s("completed"),ee();const N=Math.floor((Date.now()-j)/1e3);v(N);const O=Math.max(K[T].timeBonus-N,50);V(O);const S={...u,completed:(u.completed||0)+1,bestTime:u.bestTime?Math.min(u.bestTime,N):N,favoriteImage:r?.id};be(a,"puzzle",S),$(S),S.completed>=3&&me(a,"puzzleMaster")},500)},o=(c,y,N)=>{if(b(),G)if(G.id!==c.id){const O=E.map(S=>S.id===G.id?{...S,currentRow:y,currentCol:N}:S.id===c.id?{...S,currentRow:G.currentRow,currentCol:G.currentCol}:S);_(O),q(null),w(O)}else q(null);else q(c)},x=c=>{b(),i(c),v(0),V(0),P(Date.now()),s("playing"),setTimeout(()=>{X(c)},100)},p=()=>{if(b(),E.length>0){const c=[...E];for(let y=c.length-1;y>0;y--){const N=Math.floor(Math.random()*(y+1)),O=c[y].currentRow,S=c[y].currentCol;c[y].currentRow=c[N].currentRow,c[y].currentCol=c[N].currentCol,c[N].currentRow=O,c[N].currentCol=S}_(c)}},f=()=>{b(),r?X(r):s("menu")},C=c=>{const y=Math.floor(c/60),N=c%60;return`${y}:${N.toString().padStart(2,"0")}`};return t==="menu"?e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"}),e.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{b(),Y("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🧩 ",z("puzzleGame")]}),e.jsx("p",{className:"text-white/80",children:z("puzzleDesc")})]}),e.jsx("div",{className:"w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:u.completed||0}),e.jsx("div",{className:"text-sm opacity-80",children:z("puzzlesCompleted")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:u.bestTime?C(u.bestTime):"--:--"}),e.jsx("div",{className:"text-sm opacity-80",children:z("bestTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:h.length}),e.jsx("div",{className:"text-sm opacity-80",children:z("availableImages")})]})]})}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:z("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Object.entries(K).map(([c,y])=>e.jsx("button",{onClick:()=>{b(),D(c)},className:`p-6 rounded-2xl border-2 transition-all duration-200 ${T===c?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:c==="easy"?"😊":c==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-2",children:z(c)}),e.jsxs("div",{className:"text-sm text-gray-600",children:[y.cols,"x",y.rows," (",y.pieces," ",z("pieces"),")"]})]})},c))})]}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:z("chooseImage")}),h.length===0?e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📸"}),e.jsx("p",{children:z("noImagesAvailable")}),e.jsx("p",{className:"text-sm mt-2",children:z("generateSomeImages")})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:h.map(c=>e.jsxs("button",{onClick:()=>x(c),className:"group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[e.jsx("img",{src:c.url||c.dataUrl,alt:c.name,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("span",{className:"text-2xl",children:"🧩"})})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2",children:e.jsx("p",{className:"text-white text-xs font-bold truncate",children:c.name})})]},c.id))})]})]})]}):e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"}),e.jsxs("div",{className:"max-w-7xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("button",{onClick:()=>{b(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(ge,{className:"w-5 h-5"}),e.jsx("span",{className:"font-bold",children:C(l)})]}),e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(he,{className:"w-5 h-5"}),e.jsxs("span",{className:"font-bold",children:[K[T].cols,"x",K[T].rows," (",K[T].pieces," ",z("pieces"),")"]})]})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:p,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Se,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("shuffle")})]}),e.jsxs("button",{onClick:f,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("restart")})]})]})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-3xl p-6",children:[e.jsxs("div",{className:"mb-4 text-center",children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:z("puzzleGame")}),e.jsx("p",{className:"text-white/70 text-sm hidden sm:block",children:z("dragPiecesToSolve")}),e.jsx("p",{className:"text-white/70 text-sm sm:hidden",children:"Ketuk untuk memilih, ketuk lagi untuk menukar posisi"})]}),E.length>0&&e.jsx("div",{className:"flex justify-center px-4",children:e.jsx("div",{ref:I,className:"bg-white rounded-2xl p-3 sm:p-4 md:p-6 shadow-2xl max-w-full",style:{display:"grid",gridTemplateColumns:`repeat(${K[T].cols}, 1fr)`,gap:L?"2px":"6px",width:"fit-content"},children:Array.from({length:K[T].pieces}).map((c,y)=>{const{cols:N,rows:O}=K[T],S=Math.floor(y/N),B=y%N,R=E.find(W=>W.currentRow===S&&W.currentCol===B);return e.jsx("div",{className:"relative w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36 border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-100",onDragOver:se,onDrop:W=>J(W,S,B),children:R&&e.jsxs("div",{draggable:!0,onDragStart:W=>te(W,R),onClick:()=>o(R,S,B),className:`w-full h-full cursor-move relative group transition-all duration-200 ${G?.id===R.id?"ring-4 ring-blue-500 ring-opacity-70":""}`,style:{backgroundImage:`url(${R.image})`,backgroundSize:`${N*100}% ${O*100}%`,backgroundPosition:`${N>1?R.correctPosition.col*100/(N-1):0}% ${O>1?R.correctPosition.row*100/(O-1):0}%`},children:[e.jsx("div",{className:"absolute top-1 left-1 bg-black/50 text-white text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity",children:R.id+1}),G?.id===R.id&&e.jsx("div",{className:"absolute inset-0 border-2 border-blue-500 bg-blue-500/20 rounded",children:e.jsx("div",{className:"absolute top-1 right-1 text-blue-600 animate-pulse",children:"👆"})}),R.currentRow===R.correctPosition.row&&R.currentCol===R.correctPosition.col&&e.jsx("div",{className:"absolute inset-0 border-2 border-green-500 bg-green-500/20 rounded",children:e.jsx("div",{className:"absolute top-1 right-1 text-green-600",children:"✓"})})]})},y)})})})]}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:z("puzzleCompleted")}),e.jsx("p",{className:"text-gray-600 mb-6",children:z("greatJob")}),e.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C(l)}),e.jsx("div",{className:"text-sm text-gray-600",children:z("completionTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:g}),e.jsx("div",{className:"text-sm text-gray-600",children:z("score")})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>s("menu"),className:"flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:z("playAgain")}),e.jsx("button",{onClick:()=>{b(),Y("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:z("backToDashboard")})]})]})})]})]})},zt=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState([]),[l,v]=n.useState({row:0,col:0}),[j,P]=n.useState(0),[T,D]=n.useState(0),[h,M]=n.useState(null),[u,$]=n.useState("easy"),[g,V]=n.useState({}),[E,_]=n.useState(0),[m,A]=n.useState(null),[G,q]=n.useState([]),{t:d}=Z(),k=ce(),L={easy:{size:3,timeBonus:300},medium:{size:4,timeBonus:500},hard:{size:5,timeBonus:800}};n.useEffect(()=>{if(a){const x=U(a).themes?.reduce((f,C)=>[...f,...C.images],[])||[];q(x);const p=ne(a);V(p.slidingPuzzle||{bestTime:null,difficulty:"easy",solved:0})}},[a]),n.useEffect(()=>{let o=null;return t==="playing"&&h&&(o=setInterval(()=>{D(Math.floor((Date.now()-h)/1e3))},1e3)),()=>{o&&clearInterval(o)}},[t,h]),n.useEffect(()=>{if(t==="playing"&&r.length>0){const o=L[u].size;if(r.every((p,f)=>p.every((C,c)=>f===o-1&&c===o-1?C===0:C===f*o+c+1))){s("completed"),ee();const p=Math.max(L[u].timeBonus-T-j,50);_(p);const f={...g,solved:(g.solved||0)+1,bestTime:g.bestTime?Math.min(g.bestTime,T):T,difficulty:u};be(a,"slidingPuzzle",f),V(f),f.solved>=5&&me(a,"slidingMaster")}}},[r,t,u,T,j,g,a]);const H=n.useCallback(o=>{const x=[];for(let p=0;p<o;p++){const f=[];for(let C=0;C<o;C++)p===o-1&&C===o-1?f.push(0):f.push(p*o+C+1);x.push(f)}return x},[]),I=n.useCallback(o=>{const x=o.length;let p=o.map(c=>[...c]),f=x-1,C=x-1;for(let c=0;c<1e3;c++){const y=[];if(f>0&&y.push({row:f-1,col:C}),f<x-1&&y.push({row:f+1,col:C}),C>0&&y.push({row:f,col:C-1}),C<x-1&&y.push({row:f,col:C+1}),y.length>0){const N=y[Math.floor(Math.random()*y.length)];p[f][C]=p[N.row][N.col],p[N.row][N.col]=0,f=N.row,C=N.col}}return{board:p,emptyPosition:{row:f,col:C}}},[]),z=n.useCallback(o=>{b(),A(o);const x=L[u].size,p=H(x),{board:f,emptyPosition:C}=I(p);i(f),v(C),P(0),D(0),_(0),M(Date.now()),s("playing")},[u,H,I]),Y=()=>{b(),s("imageSelect")},K=(o,x)=>{const p=Math.abs(o-l.row),f=Math.abs(x-l.col);return p===1&&f===0||p===0&&f===1},X=(o,x)=>{if(!K(o,x)||t!=="playing")return;b();const p=r.map(f=>[...f]);p[l.row][l.col]=p[o][x],p[o][x]=0,i(p),v({row:o,col:x}),P(f=>f+1)},te=()=>{b(),s("imageSelect"),i([]),P(0),D(0),_(0),M(null),A(null)},se=()=>{if(t!=="playing")return;b();const{board:o,emptyPosition:x}=I(r);i(o),v(x),P(p=>p+10)},J=o=>{const x=Math.floor(o/60),p=o%60;return`${x}:${p.toString().padStart(2,"0")}`};if(t==="menu")return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{b(),k("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🔄 ",d("slidingPuzzle")]}),e.jsx("p",{className:"text-white/80",children:d("slidingDesc")})]}),e.jsx("div",{className:"w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:g.solved||0}),e.jsx("div",{className:"text-sm opacity-80",children:d("puzzlesSolved")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:g.bestTime?J(g.bestTime):"--:--"}),e.jsx("div",{className:"text-sm opacity-80",children:d("bestTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold capitalize",children:g.difficulty||"easy"}),e.jsx("div",{className:"text-sm opacity-80",children:d("lastDifficulty")})]})]})}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:d("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:Object.entries(L).map(([o,x])=>e.jsx("button",{onClick:()=>{b(),$(o)},className:`p-6 rounded-2xl border-2 transition-all duration-200 ${u===o?"border-orange-500 bg-orange-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:o==="easy"?"😊":o==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-2",children:d(o)}),e.jsxs("div",{className:"text-sm text-gray-600",children:[x.size,"×",x.size," ",d("grid")]})]})},o))}),e.jsx("div",{className:"text-center",children:e.jsx("button",{onClick:Y,className:"bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200",children:d("chooseImage")})}),e.jsxs("div",{className:"mt-8 bg-gray-50 rounded-2xl p-6",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-3",children:d("howToPlay")}),e.jsxs("ul",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("li",{children:["• ",d("slidingRule1")]}),e.jsxs("li",{children:["• ",d("slidingRule2")]}),e.jsxs("li",{children:["• ",d("slidingRule3")]})]})]})]})]})]});if(t==="imageSelect")return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{b(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToMenu")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🖼️ ",d("chooseImage")]}),e.jsx("p",{className:"text-white/80",children:d("selectImageForPuzzle")})]}),e.jsx("div",{className:"w-32"})]}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:d("selectYourImage")}),G.length===0?e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📸"}),e.jsx("p",{children:d("noImagesAvailable")}),e.jsx("p",{className:"text-sm mt-2",children:d("generateSomeImages")})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:G.map(o=>e.jsxs("button",{onClick:()=>z(o),className:"group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[e.jsx("img",{src:o.url||o.dataUrl,alt:o.name,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("span",{className:"text-2xl",children:"🔄"})})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2",children:e.jsx("p",{className:"text-white text-xs font-bold truncate",children:o.name})})]},o.id))})]})]})]});const w=L[u].size;return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-6 gap-4",children:[e.jsxs("button",{onClick:()=>{b(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-4 sm:px-6 py-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(ge,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:J(T)})]}),e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(he,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsxs("span",{className:"font-bold text-sm sm:text-base",children:[j," ",d("moves")]})]})]}),e.jsxs("div",{className:"flex gap-2 sm:gap-3",children:[e.jsxs("button",{onClick:se,className:"flex items-center gap-1 sm:gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Se,{className:"w-5 h-5 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base hidden sm:inline",children:d("shuffle")})]}),e.jsxs("button",{onClick:te,className:"flex items-center gap-1 sm:gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-5 h-5 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base hidden sm:inline",children:d("restart")})]})]})]}),e.jsx("div",{className:"text-center mb-4",children:e.jsxs("p",{className:"text-white/80 text-sm",children:[e.jsx("span",{className:"hidden sm:inline",children:d("clickAdjacentTiles")}),e.jsx("span",{className:"sm:hidden",children:"Ketuk kotak di sebelah area kosong untuk memindahkannya"})]})}),e.jsx("div",{className:"flex justify-center px-4",children:e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-3xl p-4 sm:p-6 md:p-8 max-w-full",children:e.jsx("div",{className:"grid gap-1 sm:gap-2 mx-auto",style:{gridTemplateColumns:`repeat(${w}, 1fr)`,width:"min(90vw, 400px)",aspectRatio:"1"},children:r.map((o,x)=>o.map((p,f)=>{if(p===0)return e.jsx("div",{className:"aspect-square bg-gray-200/50 border-2 border-dashed border-gray-400/50 rounded-lg sm:rounded-xl cursor-default flex items-center justify-center",children:e.jsx("div",{className:"text-gray-400 text-2xl",children:"⬜"})},`${x}-${f}`);const C=Math.floor((p-1)/w),c=(p-1)%w;return e.jsxs("button",{onClick:()=>X(x,f),className:`
                        aspect-square rounded-lg sm:rounded-xl transition-all duration-200 transform overflow-hidden relative
                        ${K(x,f)?"hover:scale-105 shadow-lg cursor-pointer hover:shadow-xl border-2 border-yellow-400 hover:border-yellow-500":"shadow-md cursor-default border-2 border-gray-300"}
                      `,style:{backgroundImage:m?`url(${m.url||m.dataUrl})`:"none",backgroundPosition:`${c*(100/(w-1))}% ${C*(100/(w-1))}%`,backgroundSize:`${w*100}% ${w*100}%`,backgroundRepeat:"no-repeat",backgroundColor:m?"transparent":"#f3f4f6"},children:[!m&&e.jsx("span",{className:"font-bold text-xl text-gray-800",children:p}),K(x,f)&&e.jsx("div",{className:"absolute inset-0 bg-yellow-400/20 flex items-center justify-center",children:e.jsx("div",{className:"bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold animate-pulse",children:"↕"})})]},`${x}-${f}`)}))})})}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:d("puzzleSolved")}),e.jsx("p",{className:"text-gray-600 mb-6",children:d("excellentWork")}),e.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:J(T)}),e.jsx("div",{className:"text-sm text-gray-600",children:d("time")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:j}),e.jsx("div",{className:"text-sm text-gray-600",children:d("moves")})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("div",{className:"text-3xl font-bold text-orange-600",children:E}),e.jsx("div",{className:"text-sm text-gray-600",children:d("finalScore")})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>z(m),className:"flex-1 bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:d("playAgain")}),e.jsx("button",{onClick:()=>{b(),k("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:d("backToDashboard")})]})]})})]})]})},Pt=({onComplete:a,onClose:t})=>{const[s,r]=n.useState(""),[i,l]=n.useState(!1),v=j=>{if(j.preventDefault(),s.trim().length<2){alert("Please enter at least 2 characters for your name!");return}l(!0),ee(),mt(s.trim()),setTimeout(()=>{a(s.trim())},500)};return e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl transform animate-bounce-slow",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"text-6xl mb-4",children:"👋"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Hi there, friend!"}),e.jsx("p",{className:"text-gray-600 text-lg",children:"What should we call you?"})]}),e.jsxs("form",{onSubmit:v,className:"space-y-6",children:[e.jsx("div",{children:e.jsx("input",{type:"text",value:s,onChange:j=>r(j.target.value),placeholder:"Type your name here...",className:"w-full px-4 py-3 text-xl text-center border-2 border-gray-300 rounded-2xl focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-200 transition-all duration-200",maxLength:20,autoFocus:!0,disabled:i})}),e.jsx("button",{type:"submit",disabled:!s.trim()||i,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-300 disabled:to-gray-400 text-white font-bold text-xl py-3 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none transition-all duration-200 disabled:cursor-not-allowed",children:i?e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Getting ready..."]}):"🎮 LET'S GO! 🎮"})]}),e.jsxs("div",{className:"mt-6 flex justify-center gap-4 text-2xl",children:[e.jsx("span",{className:"animate-bounce",children:"🌟"}),e.jsx("span",{className:"animate-pulse",children:"🎈"}),e.jsx("span",{className:"animate-bounce-slow",children:"🦄"}),e.jsx("span",{className:"animate-wiggle",children:"✨"})]})]})})})},At=({childName:a,onComplete:t,onClose:s})=>{const[r,i]=n.useState("unopened"),[l,v]=n.useState([]),j=Me(),P=()=>{ot(),i("opening"),setTimeout(()=>{i("revealing"),T()},2e3)},T=()=>{j.forEach((h,M)=>{setTimeout(()=>{Pe(),v(u=>[...u,h])},M*500)}),setTimeout(()=>{it(),i("complete"),jt(a)},j.length*500+1e3)},D=()=>{b(),t()};return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-4xl w-full shadow-2xl",children:[r==="unopened"&&e.jsxs("div",{className:"text-center",children:[e.jsxs("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:["🎁 ",a?`Welcome ${a}!`:"Welcome!"," 🎁"]}),e.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Here's your FREE starter pack with 6 amazing pictures!"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"w-48 h-64 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer flex flex-col items-center justify-center text-white",onClick:P,children:[e.jsx("div",{className:"text-6xl mb-4",children:"📦"}),e.jsx("div",{className:"text-2xl font-bold mb-2",children:"MYSTERY"}),e.jsx("div",{className:"text-xl font-bold mb-4",children:"PACK"}),e.jsx("div",{className:"bg-white/20 rounded-full px-4 py-2 text-sm font-bold",children:"CLICK ME!"})]})}),e.jsx("p",{className:"text-lg text-gray-500",children:"✨ 6 Amazing Pictures Inside! ✨"})]}),r==="opening"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-8",children:"🎉 Opening Your Pack! 🎉"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"w-48 h-64 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-2xl animate-pulse flex flex-col items-center justify-center text-white",children:[e.jsx("div",{className:"text-6xl mb-4 animate-bounce",children:"✨"}),e.jsx("div",{className:"text-xl font-bold",children:"OPENING..."})]})}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),r==="revealing"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-8",children:"🌟 Your New Pictures! 🌟"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-8",children:j.map((h,M)=>e.jsx("div",{className:"relative",children:l.includes(h)?e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg p-4 transform animate-bounce-slow",children:[e.jsx("img",{src:h.url||h.fallback||`https://picsum.photos/400/400?random=${h.id}`,alt:h.name,className:"w-full h-32 object-cover rounded-xl mb-2",onError:u=>{h.fallback&&u.target.src!==h.fallback?u.target.src=h.fallback:u.target.src.includes("picsum")||(u.target.src=`https://picsum.photos/400/400?random=${h.id}`)}}),e.jsx("p",{className:"text-sm font-bold text-gray-700",children:h.name})]}):e.jsx("div",{className:"bg-gray-200 rounded-2xl shadow-lg p-4 h-44 flex items-center justify-center",children:e.jsx("div",{className:"text-4xl text-gray-400",children:"?"})})},h.id))}),e.jsx("p",{className:"text-lg text-gray-600",children:"Revealing your amazing pictures..."})]}),r==="complete"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"🎉 AWESOME! 🎉"}),e.jsxs("p",{className:"text-xl text-gray-600 mb-8",children:["⭐ These pictures are now yours! ⭐",e.jsx("br",{}),"You can use them in all your games!"]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-8",children:j.map(h=>e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg p-4 transform hover:scale-105 transition-all duration-200",children:[e.jsx("img",{src:h.url||h.fallback||`https://picsum.photos/400/400?random=${h.id}`,alt:h.name,className:"w-full h-32 object-cover rounded-xl mb-2",onError:M=>{h.fallback&&M.target.src!==h.fallback?M.target.src=h.fallback:M.target.src.includes("picsum")||(M.target.src=`https://picsum.photos/400/400?random=${h.id}`)}}),e.jsx("p",{className:"text-sm font-bold text-gray-700",children:h.name})]},h.id))}),e.jsx("button",{onClick:D,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold text-2xl py-4 px-8 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",children:"🎮 AWESOME! LET'S PLAY! 🎮"})]})]})})},It=()=>{const[a,t]=n.useState(!1),s=()=>{const r=ct();t(r)};return e.jsx("div",{className:"fixed top-4 right-4 z-50",children:e.jsx("button",{onClick:s,className:"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110",title:a?"Unmute":"Mute",children:a?e.jsx(Je,{className:"w-6 h-6"}):e.jsx(Xe,{className:"w-6 h-6"})})})};function Mt(){const[a,t]=n.useState(""),[s,r]=n.useState(!1),[i,l]=n.useState(!1),[v,j]=n.useState(!1);n.useEffect(()=>{const h=ve();h&&(t(h),j(!0),ye(h)||l(!0)),(async()=>{we(),await je()})();const u=()=>{we(),je(),document.removeEventListener("click",u),document.removeEventListener("touchstart",u)};return document.addEventListener("click",u),document.addEventListener("touchstart",u),()=>{document.removeEventListener("click",u),document.removeEventListener("touchstart",u)}},[]);const P=()=>{const h=ve();h?(t(h),j(!0),ye(h)||l(!0)):r(!0)},T=h=>{t(h),r(!1),l(!0),j(!0)},D=()=>{l(!1)};return e.jsx(Ge,{children:e.jsx(wt,{children:e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500",children:[e.jsxs(Be,{children:[e.jsx(ae,{path:"/",element:v?e.jsx(re,{to:"/dashboard",replace:!0}):e.jsx(dt,{onStartAdventure:P})}),e.jsx(ae,{path:"/dashboard",element:v?e.jsx(kt,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/memory",element:v?e.jsx(St,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/puzzle",element:v?e.jsx(Ct,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/sliding",element:v?e.jsx(zt,{childName:a}):e.jsx(re,{to:"/",replace:!0})})]}),s&&e.jsx(Pt,{onComplete:T,onClose:()=>r(!1)}),i&&e.jsx(At,{childName:a,onComplete:D,onClose:()=>l(!1)}),e.jsx(It,{})]})})})}ue.createRoot(document.getElementById("root")).render(e.jsx(Oe.StrictMode,{children:e.jsx(Mt,{})}));
