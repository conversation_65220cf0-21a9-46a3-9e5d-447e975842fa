import{r as n,a as Re,u as ce,B as Ge,R as Be,b as ae,N as re,c as Oe}from"./vendor-bf17a190.js";import{G as Ve,P as xe,S as ke,C as $e,H as Le,T as he,a as _e,b as oe,c as Ue,d as Ke,I as Ye,W as qe,X as We,e as Fe,f as Te,A as Q,g as ge,h as Je,R as pe,i as Se,V as He,j as Xe}from"./ui-055c1b4d.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const w of l.addedNodes)w.tagName==="LINK"&&w.rel==="modulepreload"&&r(w)}).observe(document,{childList:!0,subtree:!0});function s(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=s(i);fetch(i.href,l)}})();var Ce={exports:{}},de={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qe=n,Ze=Symbol.for("react.element"),et=Symbol.for("react.fragment"),tt=Object.prototype.hasOwnProperty,st=Qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,at={key:!0,ref:!0,__self:!0,__source:!0};function ze(a,t,s){var r,i={},l=null,w=null;s!==void 0&&(l=""+s),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(w=t.ref);for(r in t)tt.call(t,r)&&!at.hasOwnProperty(r)&&(i[r]=t[r]);if(a&&a.defaultProps)for(r in t=a.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Ze,type:a,key:l,ref:w,props:i,_owner:st.current}}de.Fragment=et;de.jsx=ze;de.jsxs=ze;Ce.exports=de;var e=Ce.exports,ue={},fe=Re;ue.createRoot=fe.createRoot,ue.hydrateRoot=fe.hydrateRoot;class rt{constructor(){this.bgMusic=null,this.soundEffects={},this.isMuted=!1,this.bgMusicVolume=.3,this.sfxVolume=.5,this.initializeAudio()}initializeAudio(){this.bgMusic=new Audio("/music/music-bgm.mp3"),this.bgMusic.loop=!0,this.bgMusic.volume=this.bgMusicVolume,this.createSoundEffect("click",this.generateClickSound()),this.createSoundEffect("success",this.generateSuccessSound()),this.createSoundEffect("cardMatch",this.generateCardMatchSound()),this.createSoundEffect("sparkle",this.generateSparkleSound()),this.createSoundEffect("cardFlip",this.generateCardFlipSound()),this.createSoundEffect("achievement",this.generateAchievementSound()),this.createSoundEffect("packOpen",this.generatePackOpenSound())}generateClickSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(800,t.currentTime),s.frequency.exponentialRampToValueAtTime(400,t.currentTime+.1),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.3,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.1),s.start(t.currentTime),s.stop(t.currentTime+.1)}}generateSuccessSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(523,t.currentTime),s.frequency.setValueAtTime(659,t.currentTime+.1),s.frequency.setValueAtTime(784,t.currentTime+.2),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.4,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.3),s.start(t.currentTime),s.stop(t.currentTime+.3)}}generateCardMatchSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination);const i=t.createOscillator(),l=t.createGain();i.connect(l),l.connect(t.destination),[{main:523,harmony:659},{main:659,harmony:784},{main:784,harmony:1047}].forEach((f,P)=>{const T=t.currentTime+P*.15;s.frequency.setValueAtTime(f.main,T),i.frequency.setValueAtTime(f.harmony,T)}),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.3,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),l.gain.setValueAtTime(0,t.currentTime),l.gain.linearRampToValueAtTime(this.sfxVolume*.2,t.currentTime+.01),l.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),s.start(t.currentTime),s.stop(t.currentTime+.5),i.start(t.currentTime),i.stop(t.currentTime+.5)}}generateSparkleSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(1200,t.currentTime),s.frequency.exponentialRampToValueAtTime(2e3,t.currentTime+.1),s.frequency.exponentialRampToValueAtTime(800,t.currentTime+.2),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.15,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.2),s.start(t.currentTime),s.stop(t.currentTime+.2)}}generateCardFlipSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(300,t.currentTime),s.frequency.linearRampToValueAtTime(600,t.currentTime+.05),s.frequency.linearRampToValueAtTime(200,t.currentTime+.1),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.2,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.1),s.start(t.currentTime),s.stop(t.currentTime+.1)}}generateAchievementSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),[523,659,784,1047].forEach((l,w)=>{s.frequency.setValueAtTime(l,t.currentTime+w*.15)}),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.5,t.currentTime+.01),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.6),s.start(t.currentTime),s.stop(t.currentTime+.6)}}generatePackOpenSound(){const t=new(window.AudioContext||window.webkitAudioContext);return()=>{if(this.isMuted)return;const s=t.createOscillator(),r=t.createGain();s.connect(r),r.connect(t.destination),s.frequency.setValueAtTime(200,t.currentTime),s.frequency.exponentialRampToValueAtTime(800,t.currentTime+.3),s.frequency.exponentialRampToValueAtTime(1200,t.currentTime+.5),r.gain.setValueAtTime(0,t.currentTime),r.gain.linearRampToValueAtTime(this.sfxVolume*.4,t.currentTime+.1),r.gain.exponentialRampToValueAtTime(.001,t.currentTime+.5),s.start(t.currentTime),s.stop(t.currentTime+.5)}}createSoundEffect(t,s){this.soundEffects[t]=s}async playBackgroundMusic(){if(!(this.isMuted||!this.bgMusic))try{await this.bgMusic.play()}catch(t){console.log("Background music autoplay blocked by browser:",t)}}stopBackgroundMusic(){this.bgMusic&&(this.bgMusic.pause(),this.bgMusic.currentTime=0)}playSoundEffect(t){if(!(this.isMuted||!this.soundEffects[t]))try{this.soundEffects[t]()}catch(s){console.log("Sound effect error:",s)}}toggleMute(){return this.isMuted=!this.isMuted,this.isMuted?this.stopBackgroundMusic():this.playBackgroundMusic(),this.isMuted}setBgMusicVolume(t){this.bgMusicVolume=Math.max(0,Math.min(1,t)),this.bgMusic&&(this.bgMusic.volume=this.bgMusicVolume)}setSfxVolume(t){this.sfxVolume=Math.max(0,Math.min(1,t))}resumeAudioContext(){if(window.AudioContext||window.webkitAudioContext){const t=new(window.AudioContext||window.webkitAudioContext);t.state==="suspended"&&t.resume()}}}const F=new rt,je=()=>F.playBackgroundMusic(),p=()=>F.playSoundEffect("click"),ee=()=>F.playSoundEffect("success"),lt=()=>F.playSoundEffect("cardMatch"),nt=()=>F.playSoundEffect("sparkle"),Pe=()=>F.playSoundEffect("cardFlip"),it=()=>F.playSoundEffect("achievement"),ot=()=>F.playSoundEffect("packOpen"),ct=()=>F.toggleMute(),we=()=>F.resumeAudioContext(),dt=({onStartAdventure:a})=>{const t=[{icon:e.jsx(Ve,{className:"w-8 h-8"}),title:"Permainan Memori",description:"Permainan mencocokkan kartu yang seru untuk melatih ingatan!"},{icon:e.jsx(xe,{className:"w-8 h-8"}),title:"Puzzle Seru",description:"Seret & lepas potongan puzzle dengan gambar favoritmu!"},{icon:e.jsx(ke,{className:"w-8 h-8"}),title:"Puzzle Geser",description:"Tantang dirimu dengan permainan geser angka!"},{icon:e.jsx($e,{className:"w-8 h-8"}),title:"Pembuat Gambar AI",description:"Buat gambar menakjubkan dengan keajaiban AI!"},{icon:e.jsx(Le,{className:"w-8 h-8"}),title:"Galeri Pribadi",description:"Kumpulkan dan gunakan gambarmu di semua permainan!"},{icon:e.jsx(he,{className:"w-8 h-8"}),title:"Pelacakan Progres",description:"Lihat pencapaianmu dan terus berkembang!"}];return e.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-white",children:[e.jsxs("div",{className:"text-center mb-8 sm:mb-12 max-w-4xl",children:[e.jsx("div",{className:"mb-4 sm:mb-6",children:e.jsxs("h1",{className:"text-4xl sm:text-6xl md:text-8xl font-bold mb-4 animate-bounce-slow",children:["🎮 ",e.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent",children:"KidzPlay AI"})," 🎨"]})}),e.jsx("p",{className:"text-lg sm:text-2xl md:text-3xl font-semibold mb-6 sm:mb-8 text-yellow-100 px-4",children:'"Di Mana Imajinasi Bertemu Kesenangan Interaktif!"'}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8 text-sm sm:text-lg",children:[e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"👶 Ramah Anak"}),e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"🎯 Usia 3+"}),e.jsx("span",{className:"bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2",children:"🆓 Gratis Sepenuhnya"})]})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-6xl px-4",children:t.map((s,r)=>e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105",children:[e.jsx("div",{className:"text-yellow-300 mb-3 sm:mb-4 flex justify-center",children:s.icon}),e.jsx("h3",{className:"text-lg sm:text-xl font-bold mb-2",children:s.title}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base",children:s.description})]},r))}),e.jsxs("div",{className:"text-center px-4",children:[e.jsx("button",{onClick:()=>{p(),a()},className:"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold text-lg sm:text-2xl py-3 sm:py-4 px-8 sm:px-12 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 animate-pulse-slow",children:"🚀 MULAI PETUALANGANMU! 🚀"}),e.jsx("p",{className:"mt-4 text-white/70 text-base sm:text-lg",children:"Dapatkan 6 gambar menakjubkan gratis saat memulai!"})]}),e.jsx("div",{className:"fixed top-10 left-10 text-4xl animate-bounce",children:"🌟"}),e.jsx("div",{className:"fixed top-20 right-20 text-3xl animate-pulse",children:"🎈"}),e.jsx("div",{className:"fixed bottom-20 left-20 text-3xl animate-wiggle",children:"🦄"}),e.jsx("div",{className:"fixed bottom-10 right-10 text-4xl animate-bounce-slow",children:"✨"})]})},ve=()=>localStorage.getItem("gameApp_childName")||"",mt=a=>{localStorage.setItem("gameApp_childName",a)},ye=a=>localStorage.getItem(`gameApp_hasOpenedStarterPack_${a}`)==="true",ut=a=>{localStorage.setItem(`gameApp_hasOpenedStarterPack_${a}`,"true")},U=a=>{const t=`gameApp_imageCollection_${a}`,s=localStorage.getItem(t);if(s){const r=JSON.parse(s);return r.starter&&!r.themes?{themes:[{id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:r.starter||[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"},{id:"generated",name:"My Creations",description:"Pictures I created with AI",images:r.generated||[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-green-500 to-teal-600"}]}:r}return{themes:[{id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"}]}},le=(a,t)=>{const s=`gameApp_imageCollection_${a}`;localStorage.setItem(s,JSON.stringify(t))},Ae=(a,t,s="",r="")=>{const i=U(a),l={id:`theme_${Date.now()}`,name:t,description:s,prompt:r,images:[],createdAt:new Date().toISOString(),isDefault:!1,color:pt()};return i.themes.push(l),le(a,i),l.id},xt=(a,t,s)=>{const r=U(a),i=r.themes.find(l=>l.id===t);i&&!i.isDefault&&(i.name=s,le(a,r))},ht=(a,t)=>{const s=U(a),r=s.themes.findIndex(i=>i.id===t&&!i.isDefault);r!==-1&&(s.themes.splice(r,1),le(a,s))},Ne=(a,t,s)=>{const r=U(a),i=r.themes.find(l=>l.id===t);if(i){const l={id:`img_${Date.now()}`,name:s.name,dataUrl:s.dataUrl,url:s.url,prompt:s.prompt,createdAt:new Date().toISOString()};i.images.push(l),le(a,r)}},gt=(a,t,s=null)=>{const r=U(a);if(s){Ne(a,s,t);return}let i=r.themes.find(l=>l.id==="generated");i||(i={id:"generated",name:"My Creations",description:"Pictures I created with AI",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-green-500 to-teal-600"},r.themes.push(i)),Ne(a,"generated",t)},pt=()=>{const a=["from-pink-500 to-rose-600","from-purple-500 to-indigo-600","from-blue-500 to-cyan-600","from-green-500 to-emerald-600","from-yellow-500 to-orange-600","from-red-500 to-pink-600","from-indigo-500 to-purple-600","from-teal-500 to-blue-600"];return a[Math.floor(Math.random()*a.length)]},ne=a=>{const t=`gameApp_gameProgress_${a}`,s=localStorage.getItem(t);return s?JSON.parse(s):{memoryCard:{level:1,bestScore:null,gamesPlayed:0},puzzle:{completed:0,favoriteImage:null,bestTime:null},slidingPuzzle:{bestTime:null,difficulty:"easy",solved:0}}},bt=(a,t)=>{const s=`gameApp_gameProgress_${a}`;localStorage.setItem(s,JSON.stringify(t))},be=(a,t,s)=>{const r=ne(a);r[t]={...r[t],...s},bt(a,r)},Ie=a=>{const t=`gameApp_achievements_${a}`,s=localStorage.getItem(t);return s?JSON.parse(s):{starterPack:{unlocked:!1,date:null},artist:{unlocked:!1,progress:0,target:5},genius:{unlocked:!1,progress:0,target:10},memoryMaster:{unlocked:!1,progress:0,target:5},puzzleMaster:{unlocked:!1,progress:0,target:3},slidingMaster:{unlocked:!1,progress:0,target:5}}},ft=(a,t)=>{const s=`gameApp_achievements_${a}`;localStorage.setItem(s,JSON.stringify(t))},me=(a,t,s={})=>{const r=Ie(a);r[t]={...r[t],unlocked:!0,date:new Date().toISOString(),...s},ft(a,r)},Me=()=>[{id:"starter_1",name:"Picture 1",url:"/images/img1.jpg"},{id:"starter_2",name:"Picture 2",url:"/images/img2.jpg"},{id:"starter_3",name:"Picture 3",url:"/images/img3.jpg"},{id:"starter_4",name:"Picture 4",url:"/images/img4.jpg"},{id:"starter_5",name:"Picture 5",url:"/images/img5.jpg"},{id:"starter_6",name:"Picture 6",url:"/images/img6.jpg"}],jt=a=>{const t=U(a);let s=t.themes.find(i=>i.id==="starter");s||(s={id:"starter",name:"Starter Pack",description:"Your first collection of amazing pictures!",images:[],createdAt:new Date().toISOString(),isDefault:!0,color:"from-blue-500 to-purple-600"},t.themes.push(s));const r=Me();s.images=r.map(i=>({...i,createdAt:new Date().toISOString()})),le(a,t),ut(a),me(a,"starterPack")},Ee=n.createContext(),Z=()=>{const a=n.useContext(Ee);if(!a)throw new Error("useLanguage must be used within a LanguageProvider");return a},ie={id:{appTitle:"KidzPlay AI",welcomeBack:"Selamat datang kembali, {name}! 🌟",searchGames:"Cari permainan...",myPictureCollection:"📸 KOLEKSI GAMBARKU ({count})",myThemeCollections:"🎨 KOLEKSI TEMA GAMBARKU",organizeYourPictures:"Atur gambar-gambarmu berdasarkan tema",generateNewImage:"BUAT GAMBAR BARU",viewByThemes:"Lihat berdasarkan tema",seeAll:"Lihat semua →",createNewTheme:"Buat Tema Baru",themeName:"Nama Tema",themeDescription:"Deskripsi Tema",enterThemeName:"Masukkan nama tema...",enterThemeDescription:"Ceritakan tentang tema ini...",optional:"opsional",cancel:"Batal",create:"Buat",pictures:"gambar",generate:"Buat",noImagesYet:"Belum ada gambar",selectTheme:"Pilih Tema",createAmazingPictures:"Buat gambar menakjub dengan AI",describeYourPicture:"Ceritakan gambar yang kamu inginkan",promptPlaceholder:"Contoh: Seekor kucing lucu bermain di taman yang penuh bunga warna-warni...",generating:"Sedang membuat",generateImage:"Buat Gambar",generateAnother:"Buat Lagi",saveToCollection:"Simpan ke Koleksi",backToDashboard:"Kembali ke Dashboard",backToMenu:"Kembali ke Menu",chooseDifficulty:"Pilih Tingkat Kesulitan",easy:"Mudah",medium:"Sedang",hard:"Sulit",level:"Level",bestScore:"Skor Terbaik",gamesPlayed:"Permainan Dimainkan",startGame:"Mulai Permainan",playAgain:"Main Lagi",restart:"Mulai Ulang",moves:"Langkah",time:"Waktu",score:"Skor",finalScore:"Skor Akhir",congratulations:"Selamat!",needMoreImages:"Butuh lebih banyak gambar",pairs:"pasang",memoryGameCompleted:"Kamu berhasil menyelesaikan permainan memori!",puzzlesCompleted:"Puzzle Selesai",bestTime:"Waktu Terbaik",availableImages:"Gambar Tersedia",pieces:"potongan",chooseImage:"Pilih Gambar",noImagesAvailable:"Tidak ada gambar tersedia",generateSomeImages:"Buat beberapa gambar terlebih dahulu!",puzzleBoard:"Papan Puzzle",puzzlePieces:"Potongan Puzzle",workspace:"Area Kerja",shuffle:"Acak",puzzleCompleted:"Puzzle Selesai!",greatJob:"Kerja bagus! Kamu berhasil menyelesaikan puzzle!",completionTime:"Waktu Selesai",selectImageForPuzzle:"Pilih gambar untuk puzzle",selectYourImage:"Pilih Gambarmu",dragPiecesHere:"Seret potongan puzzle ke sini untuk menyusunnya",dragToWorkspace:"Seret ke area kerja untuk menyusun puzzle",puzzlesSolved:"Puzzle Terpecahkan",lastDifficulty:"Kesulitan Terakhir",grid:"kotak",howToPlay:"Cara Bermain",slidingRule1:"Klik angka yang bersebelahan dengan kotak kosong untuk memindahkannya",slidingRule2:"Susun angka dari 1 hingga terakhir secara berurutan",slidingRule3:"Kotak kosong harus berada di pojok kanan bawah",puzzleSolved:"Puzzle Terpecahkan!",excellentWork:"Kerja yang luar biasa! Kamu berhasil menyelesaikan sliding puzzle!",startPlaying:"🎮 MULAI BERMAIN",memoryCardGame:"Permainan Kartu Memori",memoryCardDesc:"Cocokkan pasangan kartu",puzzleGame:"Permainan Puzzle",puzzleDesc:"Seret & lepas potongan puzzle",slidingPuzzle:"Puzzle Geser",slidingDesc:"Geser angka untuk menyelesaikan",start:"MULAI",continue:"LANJUTKAN",new:"BARU!",levelShort:"LV",yourAchievements:"🏆 PENCAPAIANMU",starterTitle:"🎁 Pemula",starterDesc:"Membuka paket kartu pertama!",artistTitle:"🎨 Seniman",artistDesc:"Buat 5 gambar",geniusTitle:"🧠 Jenius",geniusDesc:"Selesaikan 10 permainan",unlocked:"✅ TERBUKA",locked:"⏳ TERKUNCI",progress:"Progres",language:"Bahasa",indonesian:"Indonesia",english:"English"},en:{appTitle:"KidzPlay AI",welcomeBack:"Welcome back, {name}! 🌟",searchGames:"Search games...",myPictureCollection:"📸 MY PICTURE COLLECTION ({count})",myThemeCollections:"🎨 MY THEME COLLECTIONS",organizeYourPictures:"Organize your pictures by themes",generateNewImage:"GENERATE NEW IMAGE",viewByThemes:"View by themes",seeAll:"See all →",createNewTheme:"Create New Theme",themeName:"Theme Name",themeDescription:"Theme Description",enterThemeName:"Enter theme name...",enterThemeDescription:"Tell us about this theme...",optional:"optional",cancel:"Cancel",create:"Create",pictures:"pictures",generate:"Generate",noImagesYet:"No images yet",selectTheme:"Select Theme",createAmazingPictures:"Create amazing pictures with AI",describeYourPicture:"Describe the picture you want",promptPlaceholder:"Example: A cute cat playing in a garden full of colorful flowers...",generating:"Generating",generateImage:"Generate Image",generateAnother:"Generate Another",saveToCollection:"Save to Collection",backToDashboard:"Back to Dashboard",backToMenu:"Back to Menu",chooseDifficulty:"Choose Difficulty",easy:"Easy",medium:"Medium",hard:"Hard",level:"Level",bestScore:"Best Score",gamesPlayed:"Games Played",startGame:"Start Game",playAgain:"Play Again",restart:"Restart",moves:"Moves",time:"Time",score:"Score",finalScore:"Final Score",congratulations:"Congratulations!",needMoreImages:"Need more images",pairs:"pairs",memoryGameCompleted:"You completed the memory game!",puzzlesCompleted:"Puzzles Completed",bestTime:"Best Time",availableImages:"Available Images",pieces:"pieces",chooseImage:"Choose Image",noImagesAvailable:"No images available",generateSomeImages:"Generate some images first!",puzzleBoard:"Puzzle Board",puzzlePieces:"Puzzle Pieces",workspace:"Workspace",shuffle:"Shuffle",puzzleCompleted:"Puzzle Completed!",greatJob:"Great job! You completed the puzzle!",completionTime:"Completion Time",selectImageForPuzzle:"Select image for puzzle",selectYourImage:"Select Your Image",dragPiecesHere:"Drag puzzle pieces here to assemble them",dragToWorkspace:"Drag to workspace to assemble puzzle",puzzlesSolved:"Puzzles Solved",lastDifficulty:"Last Difficulty",grid:"grid",howToPlay:"How to Play",slidingRule1:"Click numbers adjacent to the empty space to move them",slidingRule2:"Arrange numbers from 1 to last in order",slidingRule3:"Empty space should be at bottom right corner",puzzleSolved:"Puzzle Solved!",excellentWork:"Excellent work! You solved the sliding puzzle!",startPlaying:"🎮 START PLAYING",memoryCardGame:"Memory Card Game",memoryCardDesc:"Match pairs of cards",puzzleGame:"Puzzle Game",puzzleDesc:"Drag & drop puzzle pieces",slidingPuzzle:"Sliding Puzzle",slidingDesc:"Slide numbers to solve",start:"START",continue:"CONTINUE",new:"NEW!",levelShort:"LV",yourAchievements:"🏆 YOUR ACHIEVEMENTS",starterTitle:"🎁 Starter",starterDesc:"Opened first card pack!",artistTitle:"🎨 Artist",artistDesc:"Generate 5 images",geniusTitle:"🧠 Genius",geniusDesc:"Complete 10 games",unlocked:"✅ UNLOCKED",locked:"⏳ LOCKED",progress:"Progress",language:"Language",indonesian:"Indonesia",english:"English"}},wt=({children:a})=>{const[t,s]=n.useState("id");n.useEffect(()=>{const l=localStorage.getItem("kidzplay-language");l&&ie[l]&&s(l)},[]);const r=l=>{ie[l]&&(s(l),localStorage.setItem("kidzplay-language",l))},i=(l,w={})=>{let f=ie[t][l]||ie.id[l]||l;return Object.keys(w).forEach(P=>{f=f.replace(`{${P}}`,w[P])}),f};return e.jsx(Ee.Provider,{value:{language:t,changeLanguage:r,t:i},children:a})},vt=()=>{const{language:a,changeLanguage:t,t:s}=Z(),r=i=>{p(),t(i)};return e.jsxs("div",{className:"relative group",children:[e.jsxs("button",{className:"flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-white hover:bg-white/30 transition-colors",children:[e.jsx(_e,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm font-medium",children:a==="id"?"ID":"EN"})]}),e.jsxs("div",{className:"absolute right-0 top-full mt-2 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50",children:[e.jsxs("button",{onClick:()=>r("id"),className:`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${a==="id"?"bg-blue-100 text-blue-800":"text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:"🇮🇩"}),e.jsx("span",{className:"font-medium",children:s("indonesian")}),a==="id"&&e.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]}),e.jsxs("button",{onClick:()=>r("en"),className:`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${a==="en"?"bg-blue-100 text-blue-800":"text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:"🇺🇸"}),e.jsx("span",{className:"font-medium",children:s("english")}),a==="en"&&e.jsx("span",{className:"ml-auto text-blue-600",children:"✓"})]})]})]})},yt=({childName:a,onGenerateImage:t,onImageSelect:s})=>{const[r,i]=n.useState({themes:[]}),[l,w]=n.useState(null),[f,P]=n.useState(""),[T,E]=n.useState(""),[v,B]=n.useState(!1),{t:x}=Z();n.useEffect(()=>{a&&i(U(a))},[a]);const $=()=>{f.trim()&&(p(),Ae(a,f.trim(),T.trim()),i(U(a)),P(""),E(""),B(!1))},h=(m,A)=>{A.trim()&&(p(),xt(a,m,A.trim()),i(U(a)),w(null))},L=m=>{p(),ht(a,m),i(U(a))},M=(m,A)=>{p(),t&&t(m,A)},_=r.themes.reduce((m,A)=>m+A.images.length,0);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-3xl font-bold text-white flex items-center gap-2",children:[e.jsx(xe,{className:"w-8 h-8"}),x("myThemeCollections")," (",_,")"]}),e.jsx("p",{className:"text-white/80 mt-1",children:x("organizeYourPictures")})]}),e.jsxs("button",{onClick:()=>{p(),B(!0)},className:"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-6 py-3 rounded-2xl font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center gap-2",children:[e.jsx(oe,{className:"w-5 h-5"}),x("createNewTheme")]})]}),v&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:x("createNewTheme")}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-2",children:x("themeName")}),e.jsx("input",{type:"text",value:f,onChange:m=>P(m.target.value),placeholder:x("enterThemeName"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none",maxLength:30})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-bold text-gray-700 mb-2",children:[x("themeDescription")," (",x("optional"),")"]}),e.jsx("textarea",{value:T,onChange:m=>E(m.target.value),placeholder:x("enterThemeDescription"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none",rows:3,maxLength:100})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{onClick:()=>{p(),B(!1),P(""),E("")},className:"flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:x("cancel")}),e.jsx("button",{onClick:$,disabled:!f.trim(),className:"flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:x("create")})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.themes.map(m=>e.jsxs("div",{className:`bg-gradient-to-br ${m.color} rounded-3xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 overflow-hidden`,children:[e.jsxs("div",{className:"p-6 text-white",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[l===m.id?e.jsx("input",{type:"text",defaultValue:m.name,onBlur:A=>h(m.id,A.target.value),onKeyPress:A=>{A.key==="Enter"&&h(m.id,A.target.value)},className:"bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 rounded-lg px-3 py-1 text-xl font-bold focus:outline-none focus:ring-2 focus:ring-white/50",autoFocus:!0}):e.jsx("h3",{className:"text-xl font-bold truncate",children:m.name}),e.jsx("p",{className:"text-white/80 text-sm mt-1 line-clamp-2",children:m.description})]}),e.jsx("div",{className:"flex gap-2 ml-3",children:!m.isDefault&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>{p(),w(l===m.id?null:m.id)},className:"p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors",children:e.jsx(Ue,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>L(m.id),className:"p-2 rounded-lg bg-white/20 hover:bg-red-500/50 transition-colors",children:e.jsx(Ke,{className:"w-4 h-4"})})]})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-white/90 text-sm font-medium",children:[m.images.length," ",x("pictures")]}),e.jsxs("button",{onClick:()=>M(m.id,m.name),className:"bg-white/20 hover:bg-white/30 backdrop-blur-sm px-3 py-1 rounded-lg text-sm font-bold transition-colors flex items-center gap-1",children:[e.jsx(oe,{className:"w-4 h-4"}),x("generate")]})]})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm p-4",children:m.images.length>0?e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[m.images.slice(0,6).map((A,R)=>e.jsx("div",{className:"aspect-square rounded-lg overflow-hidden cursor-pointer hover:scale-105 transition-transform",onClick:()=>{p(),s&&s(A)},children:e.jsx("img",{src:A.url||A.dataUrl,alt:A.name,className:"w-full h-full object-cover"})},A.id)),m.images.length>6&&e.jsxs("div",{className:"aspect-square rounded-lg bg-white/20 flex items-center justify-center text-white font-bold text-sm",children:["+",m.images.length-6]})]}):e.jsxs("div",{className:"aspect-square rounded-lg border-2 border-dashed border-white/30 flex flex-col items-center justify-center text-white/60",children:[e.jsx(Ye,{className:"w-8 h-8 mb-2"}),e.jsx("span",{className:"text-sm font-medium",children:x("noImagesYet")})]})})]},m.id))})]})},Nt=({childName:a,onClose:t,onImageGenerated:s,selectedThemeId:r=null,selectedThemeName:i=null})=>{const[l,w]=n.useState(""),[f,P]=n.useState(!1),[T,E]=n.useState(null),[v,B]=n.useState(r||"generated"),[x,$]=n.useState(""),[h,L]=n.useState(!1),[M,_]=n.useState({themes:[]}),{t:m}=Z();n.useEffect(()=>{a&&_(U(a)),i&&i!=="My Creations"&&w(`${i} themed picture for kids`)},[a,i]);const A=async()=>{if(l.trim()){P(!0),p();try{const k=await fetch("/api/generate-image",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:`${l}, child-friendly, colorful, cartoon style, safe for kids aged 3-9`,model:"fal-ai/minimax/image-01"})});if(k.ok){const V=await k.json();E({dataUrl:V.imageUrl,prompt:l}),ee()}else setTimeout(()=>{E({dataUrl:`https://picsum.photos/512/512?random=${Date.now()}`,prompt:l}),ee()},2e3)}catch(k){console.error("Error generating image:",k),setTimeout(()=>{E({dataUrl:`https://picsum.photos/512/512?random=${Date.now()}`,prompt:l}),ee()},2e3)}finally{P(!1)}}},R=()=>{if(x.trim()){p();const k=Ae(a,x.trim(),`Pictures about ${x}`,l);_(U(a)),B(k),$(""),L(!1)}},q=()=>{if(!T)return;p();const k={name:`${l.slice(0,30)}...`,dataUrl:T.dataUrl,prompt:T.prompt};v!=="new"&&(gt(a,k,v),s&&s(),t())},d=M.themes||[];return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-2xl w-full shadow-2xl max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl",children:e.jsx(qe,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:m("generateNewImage")}),e.jsx("p",{className:"text-gray-600",children:m("createAmazingPictures")})]})]}),e.jsx("button",{onClick:()=>{p(),t()},className:"p-2 rounded-xl hover:bg-gray-100 transition-colors",children:e.jsx(We,{className:"w-6 h-6 text-gray-500"})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-3",children:m("selectTheme")}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[d.map(k=>e.jsx("button",{onClick:()=>{p(),B(k.id)},className:`p-4 rounded-xl border-2 transition-all duration-200 text-left ${v===k.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-4 h-4 rounded-full bg-gradient-to-r ${k.color}`}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-gray-800",children:k.name}),e.jsxs("div",{className:"text-xs text-gray-500",children:[k.images.length," ",m("pictures")]})]})]})},k.id)),e.jsxs("button",{onClick:()=>{p(),L(!0)},className:"p-4 rounded-xl border-2 border-dashed border-gray-300 hover:border-blue-500 transition-all duration-200 flex items-center justify-center gap-2 text-gray-600 hover:text-blue-600",children:[e.jsx(oe,{className:"w-5 h-5"}),e.jsx("span",{className:"font-bold",children:m("createNewTheme")})]})]})]}),h&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60",children:e.jsxs("div",{className:"bg-white rounded-2xl p-6 max-w-md w-full shadow-xl",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:m("createNewTheme")}),e.jsx("input",{type:"text",value:x,onChange:k=>$(k.target.value),placeholder:m("enterThemeName"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none mb-4",maxLength:30}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{p(),L(!1),$("")},className:"flex-1 px-4 py-2 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:m("cancel")}),e.jsx("button",{onClick:R,disabled:!x.trim(),className:"flex-1 px-4 py-2 rounded-xl bg-blue-500 text-white font-bold hover:bg-blue-600 transition-colors disabled:opacity-50",children:m("create")})]})]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-bold text-gray-700 mb-3",children:m("describeYourPicture")}),e.jsx("textarea",{value:l,onChange:k=>w(k.target.value),placeholder:m("promptPlaceholder"),className:"w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none",rows:4,maxLength:200}),e.jsxs("div",{className:"text-right text-xs text-gray-500 mt-1",children:[l.length,"/200"]})]}),e.jsx("button",{onClick:A,disabled:!l.trim()||f,className:"w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 mb-6",children:f?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-white"}),m("generating"),"..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ke,{className:"w-6 h-6"}),m("generateImage")]})}),T&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-6 text-center",children:[e.jsx("img",{src:T.dataUrl,alt:"Generated",className:"w-full max-w-md mx-auto rounded-xl shadow-lg"}),e.jsxs("p",{className:"text-sm text-gray-600 mt-3 italic",children:['"',T.prompt,'"']})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{p(),E(null)},className:"flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors",children:m("generateAnother")}),e.jsx("button",{onClick:q,className:"flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold hover:shadow-lg transition-all duration-200",children:m("saveToCollection")})]})]})]})})},kt=({childName:a})=>{const[t,s]=n.useState({themes:[]}),[r,i]=n.useState({}),[l,w]=n.useState({}),[f,P]=n.useState(!1),[T,E]=n.useState(!1),[v,B]=n.useState(null),[x,$]=n.useState(null),{t:h}=Z(),L=ce();n.useEffect(()=>{a&&(s(U(a)),i(ne(a)),w(Ie(a)))},[a]);const M=t.themes?.reduce((d,k)=>[...d,...k.images],[])||[],_=M.length,m=(d=null,k=null)=>{B(d),$(k),E(!0)},A=()=>{s(U(a))},R=[{id:"memory",title:h("memoryCardGame"),icon:"🃏",description:h("memoryCardDesc"),progress:r.memoryCard,color:"from-blue-500 to-purple-600",thumbnail:"/images/memory-card.jpg"},{id:"puzzle",title:h("puzzleGame"),icon:"🧩",description:h("puzzleDesc"),progress:r.puzzle,color:"from-green-500 to-teal-600",thumbnail:"/images/puzzle.jpg"},{id:"sliding",title:h("slidingPuzzle"),icon:"🔄",description:h("slidingDesc"),progress:r.slidingPuzzle,color:"from-orange-500 to-red-600",thumbnail:"/images/sliding-puzzle.jpg"}],q=[{key:"starterPack",title:h("starterTitle"),description:h("starterDesc"),unlocked:l.starterPack?.unlocked},{key:"artist",title:h("artistTitle"),description:h("artistDesc"),unlocked:l.artist?.unlocked,progress:l.artist?.progress,target:l.artist?.target},{key:"genius",title:h("geniusTitle"),description:h("geniusDesc"),unlocked:l.genius?.unlocked,progress:l.genius?.progress,target:l.genius?.target}];return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 landscape-optimized portrait-optimized",children:e.jsxs("div",{className:"dashboard-container",children:[e.jsxs("header",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-8 bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 landscape-header",children:[e.jsxs("div",{className:"mb-4 sm:mb-0",children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-white flex items-center gap-2 landscape-text-lg",children:["🏠 ",h("appTitle")]}),e.jsx("p",{className:"text-white/80 text-base sm:text-lg landscape-text-sm",children:h("welcomeBack",{name:a})})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4 w-full sm:w-auto",children:[e.jsx(vt,{}),e.jsxs("div",{className:"relative flex-1 sm:flex-none",children:[e.jsx(Fe,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("input",{type:"text",placeholder:h("searchGames"),className:"w-full sm:w-auto pl-8 sm:pl-10 pr-4 py-2 text-sm sm:text-base rounded-full bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"})]})]})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2 sm:gap-0",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:h("myPictureCollection",{count:_})}),e.jsxs("div",{className:"flex gap-2 sm:gap-3 text-sm sm:text-base",children:[e.jsxs("button",{onClick:()=>{p(),P(!0)},className:"text-white/80 hover:text-white transition-colors flex items-center gap-1 sm:gap-2",children:[e.jsx(xe,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"hidden sm:inline",children:h("viewByThemes")}),e.jsx("span",{className:"sm:hidden",children:"Tema"})]}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors",children:h("seeAll")})]})]}),e.jsxs("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-4",children:[M.slice(0,5).map(d=>e.jsxs("div",{className:"bg-white rounded-xl sm:rounded-2xl shadow-lg p-2 sm:p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer",onClick:()=>p(),children:[e.jsx("img",{src:d.url||d.dataUrl,alt:d.name,className:"w-full aspect-square object-cover rounded-lg sm:rounded-xl mb-1 sm:mb-2"}),e.jsx("p",{className:"text-xs font-bold text-gray-700 text-center truncate landscape-text-sm",children:d.name})]},d.id)),e.jsxs("div",{className:"bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl sm:rounded-2xl shadow-lg p-2 sm:p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer flex flex-col items-center justify-center text-white min-h-[80px] sm:min-h-[100px]",onClick:()=>m(),children:[e.jsx(oe,{className:"w-6 h-6 sm:w-8 sm:h-8 mb-1"}),e.jsx("p",{className:"text-xs font-bold text-center landscape-text-sm",children:h("generateNewImage")})]})]})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:h("startPlaying")}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors text-sm sm:text-base",children:h("seeAll")})]}),e.jsx("div",{className:"responsive-grid landscape-games-grid portrait-games-grid",children:R.map(d=>e.jsxs("div",{className:`relative aspect-square landscape-game-card portrait-game-card bg-gradient-to-br ${d.color} rounded-xl sm:rounded-2xl shadow-lg text-white hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer overflow-hidden`,onClick:k=>{k.stopPropagation(),p(),L(`/game/${d.id}`)},children:[e.jsx("div",{className:"absolute inset-0 opacity-20",children:e.jsx("img",{src:d.thumbnail,alt:d.title,className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"relative z-10 h-full p-3 sm:p-6 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-4",children:[e.jsx("div",{className:"text-2xl sm:text-3xl",children:d.icon}),e.jsx("div",{className:"text-xs bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full font-bold landscape-text-sm",children:d.progress?.gamesPlayed>0?`${h("level")} ${d.progress.level||1}`:h("new")})]}),e.jsxs("div",{className:"flex-1 flex flex-col justify-center text-center",children:[e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-1 sm:mb-2 landscape-text-sm",children:d.title}),e.jsx("p",{className:"text-white/80 text-xs sm:text-sm mb-2 sm:mb-4 landscape-text-sm hidden sm:block",children:d.description})]}),e.jsxs("div",{className:"flex flex-col items-center space-y-1 sm:space-y-2",children:[d.progress?.gamesPlayed>0&&e.jsx("div",{className:"flex justify-center",children:[...Array(3)].map((k,V)=>e.jsx(Te,{className:"w-3 h-3 sm:w-4 sm:h-4 text-yellow-300 fill-current"},V))}),e.jsx("button",{className:"bg-white/30 backdrop-blur-sm hover:bg-white/40 rounded-full px-4 sm:px-6 py-1 sm:py-2 text-xs sm:text-sm font-bold transition-colors landscape-text-sm",onClick:k=>{k.stopPropagation(),p(),L(`/game/${d.id}`)},children:h("start")})]})]})]},d.id))})]}),e.jsxs("section",{className:"mb-6 sm:mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-white flex items-center gap-2 landscape-text-base",children:h("yourAchievements")}),e.jsx("button",{className:"text-white/80 hover:text-white transition-colors text-sm sm:text-base",children:h("seeAll")})]}),e.jsx("div",{className:"responsive-grid",children:q.map(d=>e.jsx("div",{className:`bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 text-white border-2 ${d.unlocked?"border-yellow-400":"border-white/20"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl sm:text-3xl mb-2 sm:mb-3",children:d.title.split(" ")[0]}),e.jsx("h3",{className:"text-base sm:text-lg font-bold mb-1 sm:mb-2 landscape-text-sm",children:d.title.split(" ").slice(1).join(" ")}),e.jsx("p",{className:"text-white/80 mb-3 sm:mb-4 text-xs sm:text-sm landscape-text-sm",children:d.description}),d.unlocked?e.jsx("div",{className:"bg-yellow-400 text-yellow-900 rounded-full px-4 py-2 text-sm font-bold",children:h("unlocked")}):d.progress!==void 0?e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"text-sm",children:[h("progress"),": ",d.progress,"/",d.target]}),e.jsx("div",{className:"bg-white/20 rounded-full h-2",children:e.jsx("div",{className:"bg-yellow-400 rounded-full h-2 transition-all duration-300",style:{width:`${d.progress/d.target*100}%`}})})]}):e.jsx("div",{className:"bg-white/20 rounded-full px-4 py-2 text-sm font-bold",children:h("locked")})]})},d.key))})]}),f&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 rounded-3xl p-8 max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-3xl font-bold text-white",children:h("myThemeCollections")}),e.jsx("button",{onClick:()=>{p(),P(!1)},className:"text-white/80 hover:text-white text-2xl font-bold",children:"✕"})]}),e.jsx(yt,{childName:a,onGenerateImage:m,onImageSelect:()=>{}})]})}),T&&e.jsx(Nt,{childName:a,selectedThemeId:v,selectedThemeName:x,onClose:()=>{E(!1),B(null),$(null)},onImageGenerated:A}),e.jsx("div",{className:"landscape-hint",children:"📱 Putar layar untuk pengalaman bermain yang lebih baik!"})]})})},Tt=({delay:a=0})=>e.jsx("div",{className:"absolute pointer-events-none",style:{animation:`sparkle 2s ease-in-out ${a}s infinite`,left:`${Math.random()*100}%`,top:`${Math.random()*100}%`},children:e.jsx("div",{className:"text-yellow-400 text-2xl animate-pulse",children:"✨"})}),St=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState([]),[l,w]=n.useState([]),[f,P]=n.useState([]),[T,E]=n.useState([]),[v,B]=n.useState(0),[x,$]=n.useState(0),[h,L]=n.useState(null),[M,_]=n.useState("easy"),[m,A]=n.useState([]),[R,q]=n.useState({}),[d,k]=n.useState(0),[V,J]=n.useState(null),{t:I}=Z(),z=ce(),Y={easy:{pairs:6,gridCols:4,gridRows:3,baseScore:1e3,perfectTime:60},medium:{pairs:8,gridCols:4,gridRows:4,baseScore:1500,perfectTime:90},hard:{pairs:12,gridCols:6,gridRows:4,baseScore:2e3,perfectTime:120}};n.useEffect(()=>{if(a){const o=U(a).themes?.reduce((g,b)=>[...g,...b.images],[])||[];A(o);const u=ne(a);q(u.memoryCard||{level:1,bestScore:null,gamesPlayed:0})}},[a]),n.useEffect(()=>{let j=null;return t==="playing"&&h&&(j=setInterval(()=>{$(Math.floor((Date.now()-h)/1e3))},1e3)),()=>{j&&clearInterval(j)}},[t,h]),n.useEffect(()=>{if(l.length===2){const[j,o]=l;r[j].imageId===r[o].imageId?(lt(),E([j,o]),setTimeout(()=>{nt()},200),setTimeout(()=>{E([])},2e3),P(u=>[...u,j,o]),w([]),k(u=>u+100)):setTimeout(()=>{w([])},1e3),B(u=>u+1)}},[l,r,x]),n.useEffect(()=>{if(f.length===r.length&&r.length>0&&t==="playing"){const j=Y[M],o=d,u=Math.max(0,Math.floor((j.perfectTime-x)*10)),g=j.pairs,b=Math.max(0,(g*2-v)*20),C=j.baseScore;let c=o+u+b+C;c=Math.max(c,100),J({baseScore:o,timeBonus:u,moveBonus:b,difficultyBonus:C,finalScore:c}),s("completed"),k(c);const y={...R,gamesPlayed:(R.gamesPlayed||0)+1,bestScore:Math.max(R.bestScore||0,c),level:Math.min((R.level||1)+(c>(R.bestScore||0)?1:0),10)};be(a,"memoryCard",y),q(y),y.gamesPlayed>=5&&me(a,"memoryMaster")}},[f.length,r.length,t]);const K=j=>{const o=[...j];for(let u=o.length-1;u>0;u--){const g=Math.floor(Math.random()*(u+1));[o[u],o[g]]=[o[g],o[u]]}return o},X=n.useCallback(()=>{if(m.length<Y[M].pairs){alert(I("needMoreImages"));return}p();const o=K(m).slice(0,Y[M].pairs).flatMap((g,b)=>[{id:b*2,imageId:g.id,image:g},{id:b*2+1,imageId:g.id,image:g}]),u=K(o);i(u),w([]),P([]),E([]),B(0),$(0),k(0),J(null),L(Date.now()),s("playing")},[m,M,I]),te=j=>{t!=="playing"||l.length>=2||l.includes(j)||f.includes(j)||(Pe(),w(o=>[...o,j]))},se=()=>{p(),s("menu"),i([]),w([]),P([]),E([]),B(0),$(0),k(0),J(null),L(null)},H=j=>{const o=Math.floor(j/60),u=j%60;return`${o}:${u.toString().padStart(2,"0")}`};return t==="menu"?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-2 sm:p-4 landscape-optimized",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-4 sm:mb-8 gap-4 sm:gap-0",children:[e.jsxs("button",{onClick:()=>{p(),z("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:I("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-xl sm:text-3xl font-bold text-white mb-1 sm:mb-2 landscape-text-lg",children:["🃏 ",I("memoryCardGame")]}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base landscape-text-sm hidden sm:block",children:I("memoryCardDesc")})]}),e.jsx("div",{className:"w-8 sm:w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-4 sm:mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-3 sm:gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:R.level||1}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:I("level")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:R.bestScore||0}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:I("bestScore")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg sm:text-2xl font-bold landscape-text-base",children:R.gamesPlayed||0}),e.jsx("div",{className:"text-xs sm:text-sm opacity-80 landscape-text-sm",children:I("gamesPlayed")})]})]})}),e.jsxs("div",{className:"bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-lg sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6 text-center landscape-text-base",children:I("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mb-4 sm:mb-8",children:Object.entries(Y).map(([j,o])=>e.jsx("button",{onClick:()=>{p(),_(j)},className:`p-3 sm:p-6 rounded-xl sm:rounded-2xl border-2 transition-all duration-200 ${M===j?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl sm:text-2xl mb-1 sm:mb-2",children:j==="easy"?"😊":j==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-1 sm:mb-2 text-sm sm:text-base landscape-text-sm",children:I(j)}),e.jsxs("div",{className:"text-xs sm:text-sm text-gray-600 landscape-text-sm",children:[o.pairs," ",I("pairs")," • ",o.gridCols,"×",o.gridRows]})]})},j))}),e.jsxs("div",{className:"text-center",children:[e.jsx("button",{onClick:X,disabled:m.length<Y[M].pairs,className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:I("startGame")}),m.length<Y[M].pairs&&e.jsxs("p",{className:"text-red-500 text-sm mt-2",children:[I("needMoreImages")," (",m.length,"/",Y[M].pairs,")"]})]})]})]})}):e.jsxs("div",{className:"min-h-screen p-2 sm:p-4 relative landscape-optimized",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/30 via-purple-500/30 to-pink-500/30"}),e.jsx("style",{jsx:!0,children:`
        @keyframes sparkle {
          0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
          }
        }

        @keyframes celebration {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        .celebration-card {
          animation: celebration 0.6s ease-in-out;
        }

        .sparkle-container {
          position: absolute;
          inset: 0;
          pointer-events: none;
          overflow: hidden;
          border-radius: 1rem;
        }
      `}),e.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-3 sm:mb-6 gap-3 sm:gap-0",children:[e.jsxs("button",{onClick:()=>{p(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:I("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl px-3 sm:px-6 py-2 sm:py-3",children:[e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(ge,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:H(x)})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(Je,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsxs("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:[v," ",e.jsx("span",{className:"hidden sm:inline",children:I("moves")})]})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 text-white",children:[e.jsx(Te,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base landscape-text-sm",children:d})]})]}),e.jsxs("button",{onClick:se,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-4 h-4 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:I("restart")})]})]}),e.jsx("div",{className:"grid gap-2 sm:gap-4 lg:gap-6 mx-auto",style:{gridTemplateColumns:`repeat(${Y[M].gridCols}, 1fr)`,maxWidth:`${Y[M].gridCols*(window.innerWidth<640?80:window.innerWidth<1024?120:160)}px`},children:r.map((j,o)=>{const u=l.includes(o)||f.includes(o),g=T.includes(o);return e.jsx("div",{onClick:()=>te(o),className:`aspect-square cursor-pointer transition-all duration-300 transform hover:scale-105 ${g?"celebration-card":""}`,style:{perspective:"1000px"},children:e.jsxs("div",{className:`
                    relative w-full h-full transition-transform duration-700
                    ${u?"rotate-y-180":""}
                    ${f.includes(o)?"ring-4 ring-green-400 rounded-2xl":""}
                  `,style:{transformStyle:"preserve-3d",transform:u?"rotateY(180deg)":"rotateY(0deg)"},children:[g&&e.jsx("div",{className:"sparkle-container",children:[...Array(8)].map((b,C)=>e.jsx(Tt,{delay:C*.2},C))}),e.jsx("div",{className:"absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg",style:{backfaceVisibility:"hidden"},children:e.jsx("div",{className:"text-3xl sm:text-4xl lg:text-6xl drop-shadow-lg",children:"🎈"})}),e.jsx("div",{className:"absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-white p-1 sm:p-2 shadow-lg",style:{backfaceVisibility:"hidden",transform:"rotateY(180deg)"},children:e.jsx("img",{src:j.image.url||j.image.dataUrl,alt:j.image.name,className:"w-full h-full object-cover rounded-lg sm:rounded-xl"})})]})},j.id)})}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:I("congratulations")}),e.jsx("p",{className:"text-gray-600 mb-6",children:I("memoryGameCompleted")}),e.jsxs("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:H(x)}),e.jsx("div",{className:"text-sm text-gray-600",children:I("time")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:v}),e.jsx("div",{className:"text-sm text-gray-600",children:I("moves")})]})]}),V&&e.jsx("div",{className:"border-t pt-4",children:e.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Pasangan ditemukan:"}),e.jsxs("span",{className:"font-bold",children:["+",V.baseScore]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus waktu:"}),e.jsxs("span",{className:"font-bold text-blue-600",children:["+",V.timeBonus]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus efisiensi:"}),e.jsxs("span",{className:"font-bold text-green-600",children:["+",V.moveBonus]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Bonus tingkat kesulitan:"}),e.jsxs("span",{className:"font-bold text-orange-600",children:["+",V.difficultyBonus]})]}),e.jsxs("div",{className:"border-t pt-2 flex justify-between text-lg font-bold",children:[e.jsx("span",{children:"Total Skor:"}),e.jsx("span",{className:"text-purple-600",children:V.finalScore})]})]})})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:X,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:I("playAgain")}),e.jsx("button",{onClick:()=>{p(),z("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:I("backToDashboard")})]})]})})]})]})},Ct=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState(null),[l,w]=n.useState(0),[f,P]=n.useState(null),[T,E]=n.useState("easy"),[v,B]=n.useState([]),[x,$]=n.useState({}),[h,L]=n.useState(0),[M,_]=n.useState([]),[m,A]=n.useState(null),[R,q]=n.useState(null),[d,k]=n.useState({x:0,y:0}),[V,J]=n.useState(window.innerWidth<640),I=n.useRef(null),{t:z}=Z(),Y=ce(),K={easy:{pieces:4,cols:2,rows:2,timeBonus:300},medium:{pieces:9,cols:3,rows:3,timeBonus:200},hard:{pieces:16,cols:4,rows:4,timeBonus:100}};n.useEffect(()=>{if(a){const y=U(a).themes?.reduce((O,S)=>[...O,...S.images],[])||[];B(y);const N=ne(a);$(N.puzzle||{completed:0,favoriteImage:null,bestTime:null})}},[a]),n.useEffect(()=>{let c=null;return t==="playing"&&f&&(c=setInterval(()=>{w(Math.floor((Date.now()-f)/1e3))},1e3)),()=>{c&&clearInterval(c)}},[t,f]),n.useEffect(()=>{const c=()=>{J(window.innerWidth<640)};return window.addEventListener("resize",c),()=>window.removeEventListener("resize",c)},[]);const X=c=>{console.log("Initializing simple puzzle with image:",c);const{cols:y,rows:N}=K[T],O=[];for(let G=0;G<N;G++)for(let D=0;D<y;D++){const W=G*y+D;O.push({id:W,row:G,col:D,currentRow:G,currentCol:D,correctPosition:{row:G,col:D},isPlaced:!1,image:c.url||c.dataUrl})}const S=[...O];for(let G=S.length-1;G>0;G--){const D=Math.floor(Math.random()*(G+1)),W=S[G].currentRow,De=S[G].currentCol;S[G].currentRow=S[D].currentRow,S[G].currentCol=S[D].currentCol,S[D].currentRow=W,S[D].currentCol=De}_(S),console.log("Puzzle pieces created:",S)},te=(c,y)=>{A(y);const N=c.target.getBoundingClientRect();k({x:c.clientX-N.left,y:c.clientY-N.top})},se=c=>{c.preventDefault()},H=(c,y,N)=>{if(c.preventDefault(),!m)return;const O=M.find(S=>S.currentRow===y&&S.currentCol===N);if(O&&O.id!==m.id){const S=M.map(G=>G.id===m.id?{...G,currentRow:y,currentCol:N}:G.id===O.id?{...G,currentRow:m.currentRow,currentCol:m.currentCol}:G);_(S),p(),j(S)}A(null)},j=c=>{c.every(N=>N.currentRow===N.correctPosition.row&&N.currentCol===N.correctPosition.col)&&setTimeout(()=>{s("completed"),ee();const N=Math.floor((Date.now()-f)/1e3);w(N);const O=Math.max(K[T].timeBonus-N,50);L(O);const S={...x,completed:(x.completed||0)+1,bestTime:x.bestTime?Math.min(x.bestTime,N):N,favoriteImage:r?.id};be(a,"puzzle",S),$(S),S.completed>=3&&me(a,"puzzleMaster")},500)},o=(c,y,N)=>{if(p(),R)if(R.id!==c.id){const O=M.map(S=>S.id===R.id?{...S,currentRow:y,currentCol:N}:S.id===c.id?{...S,currentRow:R.currentRow,currentCol:R.currentCol}:S);_(O),q(null),j(O)}else q(null);else q(c)},u=c=>{p(),i(c),w(0),L(0),P(Date.now()),s("playing"),setTimeout(()=>{X(c)},100)},g=()=>{if(p(),M.length>0){const c=[...M];for(let y=c.length-1;y>0;y--){const N=Math.floor(Math.random()*(y+1)),O=c[y].currentRow,S=c[y].currentCol;c[y].currentRow=c[N].currentRow,c[y].currentCol=c[N].currentCol,c[N].currentRow=O,c[N].currentCol=S}_(c)}},b=()=>{p(),r?X(r):s("menu")},C=c=>{const y=Math.floor(c/60),N=c%60;return`${y}:${N.toString().padStart(2,"0")}`};return t==="menu"?e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"}),e.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{p(),Y("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🧩 ",z("puzzleGame")]}),e.jsx("p",{className:"text-white/80",children:z("puzzleDesc")})]}),e.jsx("div",{className:"w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:x.completed||0}),e.jsx("div",{className:"text-sm opacity-80",children:z("puzzlesCompleted")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:x.bestTime?C(x.bestTime):"--:--"}),e.jsx("div",{className:"text-sm opacity-80",children:z("bestTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:v.length}),e.jsx("div",{className:"text-sm opacity-80",children:z("availableImages")})]})]})}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:z("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Object.entries(K).map(([c,y])=>e.jsx("button",{onClick:()=>{p(),E(c)},className:`p-6 rounded-2xl border-2 transition-all duration-200 ${T===c?"border-green-500 bg-green-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:c==="easy"?"😊":c==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-2",children:z(c)}),e.jsxs("div",{className:"text-sm text-gray-600",children:[y.cols,"x",y.rows," (",y.pieces," ",z("pieces"),")"]})]})},c))})]}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:z("chooseImage")}),v.length===0?e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📸"}),e.jsx("p",{children:z("noImagesAvailable")}),e.jsx("p",{className:"text-sm mt-2",children:z("generateSomeImages")})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:v.map(c=>e.jsxs("button",{onClick:()=>u(c),className:"group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[e.jsx("img",{src:c.url||c.dataUrl,alt:c.name,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("span",{className:"text-2xl",children:"🧩"})})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2",children:e.jsx("p",{className:"text-white text-xs font-bold truncate",children:c.name})})]},c.id))})]})]})]}):e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"}),e.jsxs("div",{className:"max-w-7xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("button",{onClick:()=>{p(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(ge,{className:"w-5 h-5"}),e.jsx("span",{className:"font-bold",children:C(l)})]}),e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(he,{className:"w-5 h-5"}),e.jsxs("span",{className:"font-bold",children:[K[T].cols,"x",K[T].rows," (",K[T].pieces," ",z("pieces"),")"]})]})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:g,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Se,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("shuffle")})]}),e.jsxs("button",{onClick:b,className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:z("restart")})]})]})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-3xl p-6",children:[e.jsxs("div",{className:"mb-4 text-center",children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:z("puzzleGame")}),e.jsx("p",{className:"text-white/70 text-sm hidden sm:block",children:z("dragPiecesToSolve")}),e.jsx("p",{className:"text-white/70 text-sm sm:hidden",children:"Ketuk untuk memilih, ketuk lagi untuk menukar posisi"})]}),M.length>0&&e.jsx("div",{className:"flex justify-center px-4",children:e.jsx("div",{ref:I,className:"bg-white rounded-2xl p-3 sm:p-4 md:p-6 shadow-2xl max-w-full",style:{display:"grid",gridTemplateColumns:`repeat(${K[T].cols}, 1fr)`,gap:V?"2px":"6px",width:"fit-content"},children:Array.from({length:K[T].pieces}).map((c,y)=>{const{cols:N,rows:O}=K[T],S=Math.floor(y/N),G=y%N,D=M.find(W=>W.currentRow===S&&W.currentCol===G);return e.jsx("div",{className:"relative w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36 border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-100",onDragOver:se,onDrop:W=>H(W,S,G),children:D&&e.jsxs("div",{draggable:!0,onDragStart:W=>te(W,D),onClick:()=>o(D,S,G),className:`w-full h-full cursor-move relative group transition-all duration-200 ${R?.id===D.id?"ring-4 ring-blue-500 ring-opacity-70":""}`,style:{backgroundImage:`url(${D.image})`,backgroundSize:`${N*100}% ${O*100}%`,backgroundPosition:`${N>1?D.correctPosition.col*100/(N-1):0}% ${O>1?D.correctPosition.row*100/(O-1):0}%`},children:[e.jsx("div",{className:"absolute top-1 left-1 bg-black/50 text-white text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity",children:D.id+1}),R?.id===D.id&&e.jsx("div",{className:"absolute inset-0 border-2 border-blue-500 bg-blue-500/20 rounded",children:e.jsx("div",{className:"absolute top-1 right-1 text-blue-600 animate-pulse",children:"👆"})}),D.currentRow===D.correctPosition.row&&D.currentCol===D.correctPosition.col&&e.jsx("div",{className:"absolute inset-0 border-2 border-green-500 bg-green-500/20 rounded",children:e.jsx("div",{className:"absolute top-1 right-1 text-green-600",children:"✓"})})]})},y)})})})]}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:z("puzzleCompleted")}),e.jsx("p",{className:"text-gray-600 mb-6",children:z("greatJob")}),e.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:C(l)}),e.jsx("div",{className:"text-sm text-gray-600",children:z("completionTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:h}),e.jsx("div",{className:"text-sm text-gray-600",children:z("score")})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>s("menu"),className:"flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:z("playAgain")}),e.jsx("button",{onClick:()=>{p(),Y("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:z("backToDashboard")})]})]})})]})]})},zt=({childName:a})=>{const[t,s]=n.useState("menu"),[r,i]=n.useState([]),[l,w]=n.useState({row:0,col:0}),[f,P]=n.useState(0),[T,E]=n.useState(0),[v,B]=n.useState(null),[x,$]=n.useState("easy"),[h,L]=n.useState({}),[M,_]=n.useState(0),[m,A]=n.useState(null),[R,q]=n.useState([]),{t:d}=Z(),k=ce(),V={easy:{size:3,timeBonus:300},medium:{size:4,timeBonus:500},hard:{size:5,timeBonus:800}};n.useEffect(()=>{if(a){const u=U(a).themes?.reduce((b,C)=>[...b,...C.images],[])||[];q(u);const g=ne(a);L(g.slidingPuzzle||{bestTime:null,difficulty:"easy",solved:0})}},[a]),n.useEffect(()=>{let o=null;return t==="playing"&&v&&(o=setInterval(()=>{E(Math.floor((Date.now()-v)/1e3))},1e3)),()=>{o&&clearInterval(o)}},[t,v]),n.useEffect(()=>{if(t==="playing"&&r.length>0){const o=V[x].size;if(r.every((g,b)=>g.every((C,c)=>b===o-1&&c===o-1?C===0:C===b*o+c+1))){s("completed"),ee();const g=Math.max(V[x].timeBonus-T-f,50);_(g);const b={...h,solved:(h.solved||0)+1,bestTime:h.bestTime?Math.min(h.bestTime,T):T,difficulty:x};be(a,"slidingPuzzle",b),L(b),b.solved>=5&&me(a,"slidingMaster")}}},[r,t,x,T,f,h,a]);const J=n.useCallback(o=>{const u=[];for(let g=0;g<o;g++){const b=[];for(let C=0;C<o;C++)g===o-1&&C===o-1?b.push(0):b.push(g*o+C+1);u.push(b)}return u},[]),I=n.useCallback(o=>{const u=o.length;let g=o.map(c=>[...c]),b=u-1,C=u-1;for(let c=0;c<1e3;c++){const y=[];if(b>0&&y.push({row:b-1,col:C}),b<u-1&&y.push({row:b+1,col:C}),C>0&&y.push({row:b,col:C-1}),C<u-1&&y.push({row:b,col:C+1}),y.length>0){const N=y[Math.floor(Math.random()*y.length)];g[b][C]=g[N.row][N.col],g[N.row][N.col]=0,b=N.row,C=N.col}}return{board:g,emptyPosition:{row:b,col:C}}},[]),z=n.useCallback(o=>{p(),A(o);const u=V[x].size,g=J(u),{board:b,emptyPosition:C}=I(g);i(b),w(C),P(0),E(0),_(0),B(Date.now()),s("playing")},[x,J,I]),Y=()=>{p(),s("imageSelect")},K=(o,u)=>{const g=Math.abs(o-l.row),b=Math.abs(u-l.col);return g===1&&b===0||g===0&&b===1},X=(o,u)=>{if(!K(o,u)||t!=="playing")return;p();const g=r.map(b=>[...b]);g[l.row][l.col]=g[o][u],g[o][u]=0,i(g),w({row:o,col:u}),P(b=>b+1)},te=()=>{p(),s("imageSelect"),i([]),P(0),E(0),_(0),B(null),A(null)},se=()=>{if(t!=="playing")return;p();const{board:o,emptyPosition:u}=I(r);i(o),w(u),P(g=>g+10)},H=o=>{const u=Math.floor(o/60),g=o%60;return`${u}:${g.toString().padStart(2,"0")}`};if(t==="menu")return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{p(),k("/dashboard")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToDashboard")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🔄 ",d("slidingPuzzle")]}),e.jsx("p",{className:"text-white/80",children:d("slidingDesc")})]}),e.jsx("div",{className:"w-32"})]}),e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8",children:e.jsxs("div",{className:"grid grid-cols-3 gap-6 text-center text-white",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:h.solved||0}),e.jsx("div",{className:"text-sm opacity-80",children:d("puzzlesSolved")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold",children:h.bestTime?H(h.bestTime):"--:--"}),e.jsx("div",{className:"text-sm opacity-80",children:d("bestTime")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold capitalize",children:h.difficulty||"easy"}),e.jsx("div",{className:"text-sm opacity-80",children:d("lastDifficulty")})]})]})}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:d("chooseDifficulty")}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:Object.entries(V).map(([o,u])=>e.jsx("button",{onClick:()=>{p(),$(o)},className:`p-6 rounded-2xl border-2 transition-all duration-200 ${x===o?"border-orange-500 bg-orange-50":"border-gray-200 hover:border-gray-300"}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl mb-2",children:o==="easy"?"😊":o==="medium"?"🤔":"😤"}),e.jsx("div",{className:"font-bold text-gray-800 capitalize mb-2",children:d(o)}),e.jsxs("div",{className:"text-sm text-gray-600",children:[u.size,"×",u.size," ",d("grid")]})]})},o))}),e.jsx("div",{className:"text-center",children:e.jsx("button",{onClick:Y,className:"bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200",children:d("chooseImage")})}),e.jsxs("div",{className:"mt-8 bg-gray-50 rounded-2xl p-6",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-3",children:d("howToPlay")}),e.jsxs("ul",{className:"text-sm text-gray-600 space-y-2",children:[e.jsxs("li",{children:["• ",d("slidingRule1")]}),e.jsxs("li",{children:["• ",d("slidingRule2")]}),e.jsxs("li",{children:["• ",d("slidingRule3")]})]})]})]})]})]});if(t==="imageSelect")return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8",children:[e.jsxs("button",{onClick:()=>{p(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToMenu")})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-2",children:["🖼️ ",d("chooseImage")]}),e.jsx("p",{className:"text-white/80",children:d("selectImageForPuzzle")})]}),e.jsx("div",{className:"w-32"})]}),e.jsxs("div",{className:"bg-white rounded-3xl p-8 shadow-2xl",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center",children:d("selectYourImage")}),R.length===0?e.jsxs("div",{className:"text-center text-gray-500 py-8",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📸"}),e.jsx("p",{children:d("noImagesAvailable")}),e.jsx("p",{className:"text-sm mt-2",children:d("generateSomeImages")})]}):e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:R.map(o=>e.jsxs("button",{onClick:()=>z(o),className:"group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:[e.jsx("img",{src:o.url||o.dataUrl,alt:o.name,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center",children:e.jsx("div",{className:"bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsx("span",{className:"text-2xl",children:"🔄"})})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2",children:e.jsx("p",{className:"text-white text-xs font-bold truncate",children:o.name})})]},o.id))})]})]})]});const j=V[x].size;return e.jsxs("div",{className:"min-h-screen p-4 relative",style:{backgroundImage:"url(/images/background-game.jpg)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/30 to-pink-500/30"}),e.jsxs("div",{className:"max-w-4xl mx-auto relative z-10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between mb-6 gap-4",children:[e.jsxs("button",{onClick:()=>{p(),s("menu")},className:"flex items-center gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Q,{className:"w-6 h-6"}),e.jsx("span",{className:"font-bold",children:d("backToMenu")})]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-4 sm:px-6 py-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(ge,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsx("span",{className:"font-bold text-sm sm:text-base",children:H(T)})]}),e.jsxs("div",{className:"flex items-center gap-2 text-white",children:[e.jsx(he,{className:"w-4 h-4 sm:w-5 sm:h-5"}),e.jsxs("span",{className:"font-bold text-sm sm:text-base",children:[f," ",d("moves")]})]})]}),e.jsxs("div",{className:"flex gap-2 sm:gap-3",children:[e.jsxs("button",{onClick:se,className:"flex items-center gap-1 sm:gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(Se,{className:"w-5 h-5 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base hidden sm:inline",children:d("shuffle")})]}),e.jsxs("button",{onClick:te,className:"flex items-center gap-1 sm:gap-2 text-white hover:text-white/80 transition-colors",children:[e.jsx(pe,{className:"w-5 h-5 sm:w-6 sm:h-6"}),e.jsx("span",{className:"font-bold text-sm sm:text-base hidden sm:inline",children:d("restart")})]})]})]}),e.jsx("div",{className:"text-center mb-4",children:e.jsxs("p",{className:"text-white/80 text-sm",children:[e.jsx("span",{className:"hidden sm:inline",children:d("clickAdjacentTiles")}),e.jsx("span",{className:"sm:hidden",children:"Ketuk kotak di sebelah area kosong untuk memindahkannya"})]})}),e.jsx("div",{className:"flex justify-center px-4",children:e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-3xl p-4 sm:p-6 md:p-8 max-w-full",children:e.jsx("div",{className:"grid gap-1 sm:gap-2 mx-auto",style:{gridTemplateColumns:`repeat(${j}, 1fr)`,width:"min(90vw, 400px)",aspectRatio:"1"},children:r.map((o,u)=>o.map((g,b)=>{if(g===0)return e.jsx("div",{className:"aspect-square bg-gray-200/50 border-2 border-dashed border-gray-400/50 rounded-lg sm:rounded-xl cursor-default flex items-center justify-center",children:e.jsx("div",{className:"text-gray-400 text-2xl",children:"⬜"})},`${u}-${b}`);const C=Math.floor((g-1)/j),c=(g-1)%j;return e.jsxs("button",{onClick:()=>X(u,b),className:`
                        aspect-square rounded-lg sm:rounded-xl transition-all duration-200 transform overflow-hidden relative
                        ${K(u,b)?"hover:scale-105 shadow-lg cursor-pointer hover:shadow-xl border-2 border-yellow-400 hover:border-yellow-500":"shadow-md cursor-default border-2 border-gray-300"}
                      `,style:{backgroundImage:m?`url(${m.url||m.dataUrl})`:"none",backgroundPosition:`${c*(100/(j-1))}% ${C*(100/(j-1))}%`,backgroundSize:`${j*100}% ${j*100}%`,backgroundRepeat:"no-repeat",backgroundColor:m?"transparent":"#f3f4f6"},children:[!m&&e.jsx("span",{className:"font-bold text-xl text-gray-800",children:g}),K(u,b)&&e.jsx("div",{className:"absolute inset-0 bg-yellow-400/20 flex items-center justify-center",children:e.jsx("div",{className:"bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold animate-pulse",children:"↕"})})]},`${u}-${b}`)}))})})}),t==="completed"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🎉"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:d("puzzleSolved")}),e.jsx("p",{className:"text-gray-600 mb-6",children:d("excellentWork")}),e.jsx("div",{className:"bg-gray-50 rounded-2xl p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:H(T)}),e.jsx("div",{className:"text-sm text-gray-600",children:d("time")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:f}),e.jsx("div",{className:"text-sm text-gray-600",children:d("moves")})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("div",{className:"text-3xl font-bold text-orange-600",children:M}),e.jsx("div",{className:"text-sm text-gray-600",children:d("finalScore")})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>z(m),className:"flex-1 bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200",children:d("playAgain")}),e.jsx("button",{onClick:()=>{p(),k("/dashboard")},className:"flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors",children:d("backToDashboard")})]})]})})]})]})},Pt=({onComplete:a,onClose:t})=>{const[s,r]=n.useState(""),[i,l]=n.useState(!1),w=f=>{if(f.preventDefault(),s.trim().length<2){alert("Please enter at least 2 characters for your name!");return}l(!0),ee(),mt(s.trim()),setTimeout(()=>{a(s.trim())},500)};return e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl transform animate-bounce-slow",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"text-6xl mb-4",children:"👋"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Hi there, friend!"}),e.jsx("p",{className:"text-gray-600 text-lg",children:"What should we call you?"})]}),e.jsxs("form",{onSubmit:w,className:"space-y-6",children:[e.jsx("div",{children:e.jsx("input",{type:"text",value:s,onChange:f=>r(f.target.value),placeholder:"Type your name here...",className:"w-full px-4 py-3 text-xl text-center border-2 border-gray-300 rounded-2xl focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-200 transition-all duration-200",maxLength:20,autoFocus:!0,disabled:i})}),e.jsx("button",{type:"submit",disabled:!s.trim()||i,className:"w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-300 disabled:to-gray-400 text-white font-bold text-xl py-3 px-6 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none transition-all duration-200 disabled:cursor-not-allowed",children:i?e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Getting ready..."]}):"🎮 LET'S GO! 🎮"})]}),e.jsxs("div",{className:"mt-6 flex justify-center gap-4 text-2xl",children:[e.jsx("span",{className:"animate-bounce",children:"🌟"}),e.jsx("span",{className:"animate-pulse",children:"🎈"}),e.jsx("span",{className:"animate-bounce-slow",children:"🦄"}),e.jsx("span",{className:"animate-wiggle",children:"✨"})]})]})})})},At=({childName:a,onComplete:t,onClose:s})=>{const[r,i]=n.useState("unopened"),[l,w]=n.useState([]),f=Me(),P=()=>{ot(),i("opening"),setTimeout(()=>{i("revealing"),T()},2e3)},T=()=>{f.forEach((v,B)=>{setTimeout(()=>{Pe(),w(x=>[...x,v])},B*500)}),setTimeout(()=>{it(),i("complete"),jt(a)},f.length*500+1e3)},E=()=>{p(),t()};return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-3xl p-8 max-w-4xl w-full shadow-2xl",children:[r==="unopened"&&e.jsxs("div",{className:"text-center",children:[e.jsxs("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:["🎁 ",a?`Welcome ${a}!`:"Welcome!"," 🎁"]}),e.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Here's your FREE starter pack with 6 amazing pictures!"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"w-48 h-64 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer flex flex-col items-center justify-center text-white",onClick:P,children:[e.jsx("div",{className:"text-6xl mb-4",children:"📦"}),e.jsx("div",{className:"text-2xl font-bold mb-2",children:"MYSTERY"}),e.jsx("div",{className:"text-xl font-bold mb-4",children:"PACK"}),e.jsx("div",{className:"bg-white/20 rounded-full px-4 py-2 text-sm font-bold",children:"CLICK ME!"})]})}),e.jsx("p",{className:"text-lg text-gray-500",children:"✨ 6 Amazing Pictures Inside! ✨"})]}),r==="opening"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-8",children:"🎉 Opening Your Pack! 🎉"}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"w-48 h-64 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-2xl animate-pulse flex flex-col items-center justify-center text-white",children:[e.jsx("div",{className:"text-6xl mb-4 animate-bounce",children:"✨"}),e.jsx("div",{className:"text-xl font-bold",children:"OPENING..."})]})}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce"}),e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),e.jsx("div",{className:"w-3 h-3 bg-primary-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]}),r==="revealing"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-8",children:"🌟 Your New Pictures! 🌟"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-8",children:f.map((v,B)=>e.jsx("div",{className:"relative",children:l.includes(v)?e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg p-4 transform animate-bounce-slow",children:[e.jsx("img",{src:v.url,alt:v.name,className:"w-full h-32 object-cover rounded-xl mb-2"}),e.jsx("p",{className:"text-sm font-bold text-gray-700",children:v.name})]}):e.jsx("div",{className:"bg-gray-200 rounded-2xl shadow-lg p-4 h-44 flex items-center justify-center",children:e.jsx("div",{className:"text-4xl text-gray-400",children:"?"})})},v.id))}),e.jsx("p",{className:"text-lg text-gray-600",children:"Revealing your amazing pictures..."})]}),r==="complete"&&e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-800 mb-4",children:"🎉 AWESOME! 🎉"}),e.jsxs("p",{className:"text-xl text-gray-600 mb-8",children:["⭐ These pictures are now yours! ⭐",e.jsx("br",{}),"You can use them in all your games!"]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-8",children:f.map(v=>e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg p-4 transform hover:scale-105 transition-all duration-200",children:[e.jsx("img",{src:v.url,alt:v.name,className:"w-full h-32 object-cover rounded-xl mb-2"}),e.jsx("p",{className:"text-sm font-bold text-gray-700",children:v.name})]},v.id))}),e.jsx("button",{onClick:E,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold text-2xl py-4 px-8 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200",children:"🎮 AWESOME! LET'S PLAY! 🎮"})]})]})})},It=()=>{const[a,t]=n.useState(!1),s=()=>{const r=ct();t(r)};return e.jsx("div",{className:"fixed top-4 right-4 z-50",children:e.jsx("button",{onClick:s,className:"bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full shadow-lg transition-all duration-200 transform hover:scale-110",title:a?"Unmute":"Mute",children:a?e.jsx(He,{className:"w-6 h-6"}):e.jsx(Xe,{className:"w-6 h-6"})})})};function Mt(){const[a,t]=n.useState(""),[s,r]=n.useState(!1),[i,l]=n.useState(!1),[w,f]=n.useState(!1);n.useEffect(()=>{const v=ve();v&&(t(v),f(!0),ye(v)||l(!0)),(async()=>{we(),await je()})();const x=()=>{we(),je(),document.removeEventListener("click",x),document.removeEventListener("touchstart",x)};return document.addEventListener("click",x),document.addEventListener("touchstart",x),()=>{document.removeEventListener("click",x),document.removeEventListener("touchstart",x)}},[]);const P=()=>{const v=ve();v?(t(v),f(!0),ye(v)||l(!0)):r(!0)},T=v=>{t(v),r(!1),l(!0),f(!0)},E=()=>{l(!1)};return e.jsx(Ge,{children:e.jsx(wt,{children:e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500",children:[e.jsxs(Be,{children:[e.jsx(ae,{path:"/",element:w?e.jsx(re,{to:"/dashboard",replace:!0}):e.jsx(dt,{onStartAdventure:P})}),e.jsx(ae,{path:"/dashboard",element:w?e.jsx(kt,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/memory",element:w?e.jsx(St,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/puzzle",element:w?e.jsx(Ct,{childName:a}):e.jsx(re,{to:"/",replace:!0})}),e.jsx(ae,{path:"/game/sliding",element:w?e.jsx(zt,{childName:a}):e.jsx(re,{to:"/",replace:!0})})]}),s&&e.jsx(Pt,{onComplete:T,onClose:()=>r(!1)}),i&&e.jsx(At,{childName:a,onComplete:E,onClose:()=>l(!1)}),e.jsx(It,{})]})})})}ue.createRoot(document.getElementById("root")).render(e.jsx(Oe.StrictMode,{children:e.jsx(Mt,{})}));
