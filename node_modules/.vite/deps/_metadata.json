{"hash": "3580f8aa", "browserHash": "9a7c99e6", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1eab4acd", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "d51750ff", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "e14772c4", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "bd283b15", "needsInterop": true}, "headbreaker": {"src": "../../headbreaker/src/index.js", "file": "headbreaker.js", "fileHash": "bc1344e7", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "820b19ff", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8b872f7d", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "53e5b89e", "needsInterop": false}}, "chunks": {"chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}