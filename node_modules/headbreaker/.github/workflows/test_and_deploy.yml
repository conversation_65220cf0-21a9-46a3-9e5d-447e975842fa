name: Test and deploy

on:
- push
- workflow_dispatch

jobs:
  test_and_deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v1
      with:
          node-version: 12
          registry-url: https://registry.npmjs.org/
    - name: Install dependencies
      run: npm install
    - name: Run tests
      run: npm test
    - name: Build package
      run: npm run build
    - name: Create Github release
      id: create_release
      if: ${{ startsWith(github.ref, 'refs/tags/v') }}
      uses: actions/create-release@v1
      with:
        tag_name: ${{ github.ref }}
        release_name: ${{ github.ref }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - name: Upload package to Github releases
      if: ${{ startsWith(github.ref, 'refs/tags/v') }}
      uses: actions/upload-release-asset@v1
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ${{ github.workspace }}/dist/headbreaker.js
        asset_name: heabreaker.js
        asset_content_type: application/javascript
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - name: Deploy to NPM
      if: ${{ startsWith(github.ref, 'refs/tags/v') }}
      run: npm publish --access public
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
