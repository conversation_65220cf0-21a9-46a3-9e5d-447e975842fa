# Changelog

## [v2.0.0](https://github.com/flbulgarelli/headbreaker/tree/v2.0.0) (2023-02-14)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v1.3.0...v2.0.0)

**Closed issues:**

- Moving pieces offstage if preventOffstageDrag AND fixed are true [\#25](https://github.com/flbulgarelli/headbreaker/issues/25)

**Merged pull requests:**

- Feature drag mode [\#47](https://github.com/flbulgarelli/headbreaker/pull/47) ([flbulgarelli](https://github.com/flbulgarelli))
- Place figures on top during movement [\#46](https://github.com/flbulgarelli/headbreaker/pull/46) ([flbulgarelli](https://github.com/flbulgarelli))
- Bump json5 from 1.0.1 to 1.0.2 [\#44](https://github.com/flbulgarelli/headbreaker/pull/44) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump decode-uri-component from 0.2.0 to 0.2.2 [\#32](https://github.com/flbulgarelli/headbreaker/pull/32) ([dependabot[bot]](https://github.com/apps/dependabot))
- Feature reframe [\#31](https://github.com/flbulgarelli/headbreaker/pull/31) ([flbulgarelli](https://github.com/flbulgarelli))
- Bump ansi-regex from 3.0.0 to 3.0.1 [\#30](https://github.com/flbulgarelli/headbreaker/pull/30) ([dependabot[bot]](https://github.com/apps/dependabot))
- Bump loader-utils and html-webpack-plugin [\#28](https://github.com/flbulgarelli/headbreaker/pull/28) ([dependabot[bot]](https://github.com/apps/dependabot))

## [v1.3.0](https://github.com/flbulgarelli/headbreaker/tree/v1.3.0) (2022-11-27)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v1.2.0...v1.3.0)

**Closed issues:**

- onConnect piece, target bug [\#23](https://github.com/flbulgarelli/headbreaker/issues/23)
- Keep the solved pieces intact when dragging [\#21](https://github.com/flbulgarelli/headbreaker/issues/21)
- Issue Tracker [\#19](https://github.com/flbulgarelli/headbreaker/issues/19)
- Hi - Pieces "Bank" [\#17](https://github.com/flbulgarelli/headbreaker/issues/17)
- How can I replace the image of a single piece? [\#16](https://github.com/flbulgarelli/headbreaker/issues/16)
- Standalone Javascript [\#15](https://github.com/flbulgarelli/headbreaker/issues/15)
- How to find the effective width and height of a piece? [\#14](https://github.com/flbulgarelli/headbreaker/issues/14)
- Headbreaker is not defined [\#13](https://github.com/flbulgarelli/headbreaker/issues/13)
- open source / license? [\#12](https://github.com/flbulgarelli/headbreaker/issues/12)
- Sorry, maybe I'm too low to use it at all [\#11](https://github.com/flbulgarelli/headbreaker/issues/11)
- get the whole image rendered  [\#10](https://github.com/flbulgarelli/headbreaker/issues/10)

## [v1.2.0](https://github.com/flbulgarelli/headbreaker/tree/v1.2.0) (2020-11-22)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v1.1.1...v1.2.0)

**Closed issues:**

- Drawing our own shapes [\#8](https://github.com/flbulgarelli/headbreaker/issues/8)
- Limit the drag   [\#7](https://github.com/flbulgarelli/headbreaker/issues/7)
- Positioning Puzzle on Canvas in autogenerate [\#5](https://github.com/flbulgarelli/headbreaker/issues/5)
- Unsure how to implement drag boundary so pieces can't be dragged offstage. [\#2](https://github.com/flbulgarelli/headbreaker/issues/2)

**Merged pull requests:**

- Feature prevent pieces going out of boundaries [\#9](https://github.com/flbulgarelli/headbreaker/pull/9) ([flbulgarelli](https://github.com/flbulgarelli))

## [v1.1.1](https://github.com/flbulgarelli/headbreaker/tree/v1.1.1) (2020-10-11)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v1.1.0...v1.1.1)

**Closed issues:**

- Responsive Demo doesn't work in Chrome Version 84.0.4147.105 [\#3](https://github.com/flbulgarelli/headbreaker/issues/3)

**Merged pull requests:**

- Feature rounded outline [\#6](https://github.com/flbulgarelli/headbreaker/pull/6) ([flbulgarelli](https://github.com/flbulgarelli))

## [v1.1.0](https://github.com/flbulgarelli/headbreaker/tree/v1.1.0) (2020-08-31)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v1.0.0...v1.1.0)

**Merged pull requests:**

- Feature mixed piece size puzzle [\#4](https://github.com/flbulgarelli/headbreaker/pull/4) ([flbulgarelli](https://github.com/flbulgarelli))

## [v1.0.0](https://github.com/flbulgarelli/headbreaker/tree/v1.0.0) (2020-08-15)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v0.2.0...v1.0.0)

**Merged pull requests:**

- Feature non squared pieces [\#1](https://github.com/flbulgarelli/headbreaker/pull/1) ([flbulgarelli](https://github.com/flbulgarelli))

## [v0.2.0](https://github.com/flbulgarelli/headbreaker/tree/v0.2.0) (2020-07-05)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v0.1.0...v0.2.0)

## [v0.1.0](https://github.com/flbulgarelli/headbreaker/tree/v0.1.0) (2020-07-02)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/v0.0.1...v0.1.0)

## [v0.0.1](https://github.com/flbulgarelli/headbreaker/tree/v0.0.1) (2020-06-19)

[Full Changelog](https://github.com/flbulgarelli/headbreaker/compare/a0d14952305f7baec93b6e821432ad721d8687b9...v0.0.1)



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
