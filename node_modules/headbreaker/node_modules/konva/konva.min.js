!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Konva=e()}(this,function(){"use strict";
/*
   * Konva JavaScript Framework v6.0.0
   * http://konvajs.org/
   * Licensed under the MIT
   * Date: Fri May 08 2020
   *
   * Original work Copyright (C) 2011 - 2013 by <PERSON> (KineticJS)
   * Modified work Copyright (C) 2014 - present by <PERSON> (Konva)
   *
   * @license
   */var e=Math.PI/180;function t(t){var e=t.toLowerCase(),i=/(chrome)[ /]([\w.]+)/.exec(e)||/(webkit)[ /]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ /]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[],n=!!t.match(/Android|BlackBerry|iPhone|iPad|iPod|Opera Mini|IEMobile/i),r=!!t.match(/IEMobile/i);return{browser:i[1]||"",version:i[2]||"0",isIE:function(t){var e=t.indexOf("msie ");if(0<e)return parseInt(t.substring(e+5,t.indexOf(".",e)),10);if(0<t.indexOf("trident/")){var i=t.indexOf("rv:");return parseInt(t.substring(i+3,t.indexOf(".",i)),10)}var n=t.indexOf("edge/");return 0<n&&parseInt(t.substring(n+5,t.indexOf(".",n)),10)}(e),mobile:n,ieMobile:r}}function i(t){s[t.prototype.getClassName()]=t,G[t.prototype.getClassName()]=t}var n="undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope?self:{},G={_global:n,version:"6.0.0",isBrowser:"undefined"!=typeof window&&("[object Window]"==={}.toString.call(window)||"[object global]"==={}.toString.call(window)),isUnminified:/param/.test(function(t){}.toString()),dblClickWindow:400,getAngle:function(t){return G.angleDeg?t*e:t},enableTrace:!1,_pointerEventsEnabled:!1,hitOnDragEnabled:!1,captureTouchEventsEnabled:!1,listenClickTap:!1,inDblClickWindow:!1,pixelRatio:void 0,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging:function(){return G.DD.isDragging},isDragReady:function(){return!!G.DD.node},UA:t(n.navigator&&n.navigator.userAgent||""),document:n.document,_injectGlobal:function(t){n.Konva=t},_parseUA:t},s={},o=(r.toCollection=function(t){for(var e=new r,i=t.length,n=0;n<i;n++)e.push(t[n]);return e},r._mapMethod=function(n){r.prototype[n]=function(){for(var t=this.length,e=[].slice.call(arguments),i=0;i<t;i++)this[i][n].apply(this[i],e);return this}},r.mapMethods=function(t){var e=t.prototype;for(var i in e)r._mapMethod(i)},r);function r(){}o.prototype=[],o.prototype.each=function(t){for(var e=0;e<this.length;e++)t(this[e],e)},o.prototype.toArray=function(){for(var t=[],e=this.length,i=0;i<e;i++)t.push(this[i]);return t};var l=(a.prototype.copy=function(){return new a(this.m)},a.prototype.point=function(t){var e=this.m;return{x:e[0]*t.x+e[2]*t.y+e[4],y:e[1]*t.x+e[3]*t.y+e[5]}},a.prototype.translate=function(t,e){return this.m[4]+=this.m[0]*t+this.m[2]*e,this.m[5]+=this.m[1]*t+this.m[3]*e,this},a.prototype.scale=function(t,e){return this.m[0]*=t,this.m[1]*=t,this.m[2]*=e,this.m[3]*=e,this},a.prototype.rotate=function(t){var e=Math.cos(t),i=Math.sin(t),n=this.m[0]*e+this.m[2]*i,r=this.m[1]*e+this.m[3]*i,o=this.m[0]*-i+this.m[2]*e,a=this.m[1]*-i+this.m[3]*e;return this.m[0]=n,this.m[1]=r,this.m[2]=o,this.m[3]=a,this},a.prototype.getTranslation=function(){return{x:this.m[4],y:this.m[5]}},a.prototype.skew=function(t,e){var i=this.m[0]+this.m[2]*e,n=this.m[1]+this.m[3]*e,r=this.m[2]+this.m[0]*t,o=this.m[3]+this.m[1]*t;return this.m[0]=i,this.m[1]=n,this.m[2]=r,this.m[3]=o,this},a.prototype.multiply=function(t){var e=this.m[0]*t.m[0]+this.m[2]*t.m[1],i=this.m[1]*t.m[0]+this.m[3]*t.m[1],n=this.m[0]*t.m[2]+this.m[2]*t.m[3],r=this.m[1]*t.m[2]+this.m[3]*t.m[3],o=this.m[0]*t.m[4]+this.m[2]*t.m[5]+this.m[4],a=this.m[1]*t.m[4]+this.m[3]*t.m[5]+this.m[5];return this.m[0]=e,this.m[1]=i,this.m[2]=n,this.m[3]=r,this.m[4]=o,this.m[5]=a,this},a.prototype.invert=function(){var t=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),e=this.m[3]*t,i=-this.m[1]*t,n=-this.m[2]*t,r=this.m[0]*t,o=t*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),a=t*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=e,this.m[1]=i,this.m[2]=n,this.m[3]=r,this.m[4]=o,this.m[5]=a,this},a.prototype.getMatrix=function(){return this.m},a.prototype.setAbsolutePosition=function(t,e){var i=this.m[0],n=this.m[1],r=this.m[2],o=this.m[3],a=this.m[4],s=(i*(e-this.m[5])-n*(t-a))/(i*o-n*r),h=(t-a-r*s)/i;return this.translate(h,s)},a.prototype.decompose=function(){var t,e,i=this.m[0],n=this.m[1],r=this.m[2],o=this.m[3],a=i*o-n*r,s={x:this.m[4],y:this.m[5],rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};return 0!=i||0!=n?(t=Math.sqrt(i*i+n*n),s.rotation=0<n?Math.acos(i/t):-Math.acos(i/t),s.scaleX=t,s.scaleY=a/t,s.skewX=(i*r+n*o)/a,s.skewY=0):0==r&&0==o||(e=Math.sqrt(r*r+o*o),s.rotation=Math.PI/2-(0<o?Math.acos(-r/e):-Math.acos(r/e)),s.scaleX=a/e,s.scaleY=e,s.skewX=0,s.skewY=(i*r+n*o)/a),s.rotation=A._getRotation(s.rotation),s},a);function a(t){void 0===t&&(t=[1,0,0,1,0,0]),this.m=t&&t.slice()||[1,0,0,1,0,0]}var h=Math.PI/180,d=180/Math.PI,c="Konva error: ",p={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},u=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/,f=[],A={_isElement:function(t){return!(!t||1!=t.nodeType)},_isFunction:function(t){return!!(t&&t.constructor&&t.call&&t.apply)},_isPlainObject:function(t){return!!t&&t.constructor===Object},_isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},_isNumber:function(t){return"[object Number]"===Object.prototype.toString.call(t)&&!isNaN(t)&&isFinite(t)},_isString:function(t){return"[object String]"===Object.prototype.toString.call(t)},_isBoolean:function(t){return"[object Boolean]"===Object.prototype.toString.call(t)},isObject:function(t){return t instanceof Object},isValidSelector:function(t){if("string"!=typeof t)return!1;var e=t[0];return"#"===e||"."===e||e===e.toUpperCase()},_sign:function(t){return 0===t?0:0<t?1:-1},requestAnimFrame:function(t){f.push(t),1===f.length&&requestAnimationFrame(function(){var t=f;f=[],t.forEach(function(t){t()})})},createCanvasElement:function(){var t=document.createElement("canvas");try{t.style=t.style||{}}catch(t){}return t},createImageElement:function(){return document.createElement("img")},_isInDocument:function(t){for(;t=t.parentNode;)if(t==document)return!0;return!1},_simplifyArray:function(t){for(var e,i=[],n=t.length,r=A,o=0;o<n;o++)e=t[o],r._isNumber(e)?e=Math.round(1e3*e)/1e3:r._isString(e)||(e=e.toString()),i.push(e);return i},_urlToImage:function(t,e){var i=new n.Image;i.onload=function(){e(i)},i.src=t},_rgbToHex:function(t,e,i){return((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1)},_hexToRgb:function(t){t=t.replace("#","");var e=parseInt(t,16);return{r:e>>16&255,g:e>>8&255,b:255&e}},getRandomColor:function(){for(var t=(16777215*Math.random()<<0).toString(16);t.length<6;)t="0"+t;return"#"+t},get:function(t,e){return void 0===t?e:t},getRGB:function(t){var e;return t in p?{r:(e=p[t])[0],g:e[1],b:e[2]}:"#"===t[0]?this._hexToRgb(t.substring(1)):"rgb("===t.substr(0,4)?(e=u.exec(t.replace(/ /g,"")),{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}):{r:0,g:0,b:0}},colorToRGBA:function(t){return t=t||"black",A._namedColorToRBA(t)||A._hex3ColorToRGBA(t)||A._hex6ColorToRGBA(t)||A._rgbColorToRGBA(t)||A._rgbaColorToRGBA(t)||A._hslColorToRGBA(t)},_namedColorToRBA:function(t){var e=p[t.toLowerCase()];return e?{r:e[0],g:e[1],b:e[2],a:1}:null},_rgbColorToRGBA:function(t){if(0===t.indexOf("rgb(")){var e=(t=t.match(/rgb\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:1}}},_rgbaColorToRGBA:function(t){if(0===t.indexOf("rgba(")){var e=(t=t.match(/rgba\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:e[3]}}},_hex6ColorToRGBA:function(t){if("#"===t[0]&&7===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:1}},_hex3ColorToRGBA:function(t){if("#"===t[0]&&4===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:1}},_hslColorToRGBA:function(t){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(t)){var e=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(t),i=(e[0],e.slice(1)),n=Number(i[0])/360,r=Number(i[1])/100,o=Number(i[2])/100,a=void 0,s=void 0,h=void 0;if(0==r)return h=255*o,{r:Math.round(h),g:Math.round(h),b:Math.round(h),a:1};for(var d=2*o-(a=o<.5?o*(1+r):o+r-o*r),l=[0,0,0],c=0;c<3;c++)(s=n+1/3*-(c-1))<0&&s++,1<s&&s--,h=6*s<1?d+6*(a-d)*s:2*s<1?a:3*s<2?d+(a-d)*(2/3-s)*6:d,l[c]=255*h;return{r:Math.round(l[0]),g:Math.round(l[1]),b:Math.round(l[2]),a:1}}},haveIntersection:function(t,e){return!(e.x>t.x+t.width||e.x+e.width<t.x||e.y>t.y+t.height||e.y+e.height<t.y)},cloneObject:function(t){var e={};for(var i in t)this._isPlainObject(t[i])?e[i]=this.cloneObject(t[i]):this._isArray(t[i])?e[i]=this.cloneArray(t[i]):e[i]=t[i];return e},cloneArray:function(t){return t.slice(0)},_degToRad:function(t){return t*h},_radToDeg:function(t){return t*d},_getRotation:function(t){return G.angleDeg?A._radToDeg(t):t},_capitalize:function(t){return t.charAt(0).toUpperCase()+t.slice(1)},throw:function(t){throw new Error(c+t)},error:function(t){console.error(c+t)},warn:function(t){G.showWarnings&&console.warn("Konva warning: "+t)},extend:function(t,e){function i(){this.constructor=t}i.prototype=e.prototype;var n=t.prototype;for(var r in t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.__super__=e.prototype,t.super=e},_getControlPoints:function(t,e,i,n,r,o,a){var s=Math.sqrt(Math.pow(i-t,2)+Math.pow(n-e,2)),h=Math.sqrt(Math.pow(r-i,2)+Math.pow(o-n,2)),d=a*s/(s+h),l=a*h/(s+h);return[i-d*(r-t),n-d*(o-e),i+l*(r-t),n+l*(o-e)]},_expandPoints:function(t,e){for(var i,n=t.length,r=[],o=2;o<n-2;o+=2)i=A._getControlPoints(t[o-2],t[o-1],t[o],t[o+1],t[o+2],t[o+3],e),r.push(i[0]),r.push(i[1]),r.push(t[o]),r.push(t[o+1]),r.push(i[2]),r.push(i[3]);return r},each:function(t,e){for(var i in t)e(i,t[i])},_inRange:function(t,e,i){return e<=t&&t<i},_getProjectionToSegment:function(t,e,i,n,r,o){var a,s,h,d,l=(t-i)*(t-i)+(e-n)*(e-n);return d=0==l?(a=t,s=e,(r-i)*(r-i)+(o-n)*(o-n)):(h=((r-t)*(i-t)+(o-e)*(n-e))/l)<0?((a=t)-r)*(t-r)+((s=e)-o)*(e-o):1<h?((a=i)-r)*(i-r)+((s=n)-o)*(n-o):((a=t+h*(i-t))-r)*(a-r)+((s=e+h*(n-e))-o)*(s-o),[a,s,d]},_getProjectionToLine:function(s,h,d){var l=A.cloneObject(s),c=Number.MAX_VALUE;return h.forEach(function(t,e){var i,n,r,o,a;!d&&e===h.length-1||(i=h[(e+1)%h.length],r=(n=A._getProjectionToSegment(t.x,t.y,i.x,i.y,s.x,s.y))[0],o=n[1],(a=n[2])<c&&(l.x=r,l.y=o,c=a))}),l},_prepareArrayForTween:function(t,e,i){var n,r,o=[],a=[];for(t.length>e.length&&(r=e,e=t,t=r),n=0;n<t.length;n+=2)o.push({x:t[n],y:t[n+1]});for(n=0;n<e.length;n+=2)a.push({x:e[n],y:e[n+1]});var s=[];return a.forEach(function(t){var e=A._getProjectionToLine(t,o,i);s.push(e.x),s.push(e.y)}),s},_prepareToStringify:function(t){var e;for(var i in t.visitedByCircularReferenceRemoval=!0,t)if(t.hasOwnProperty(i)&&t[i]&&"object"==typeof t[i])if(e=Object.getOwnPropertyDescriptor(t,i),t[i].visitedByCircularReferenceRemoval||A._isElement(t[i])){if(!e.configurable)return null;delete t[i]}else if(null===A._prepareToStringify(t[i])){if(!e.configurable)return null;delete t[i]}return delete t.visitedByCircularReferenceRemoval,t},_assign:function(t,e){for(var i in e)t[i]=e[i];return t},_getFirstPointerId:function(t){return t.touches?t.changedTouches[0].identifier:999}};function g(t){return A._isString(t)?'"'+t+'"':"[object Number]"===Object.prototype.toString.call(t)||A._isBoolean(t)?t:Object.prototype.toString.call(t)}function y(t){return 255<t?255:t<0?0:Math.round(t)}function v(){if(G.isUnminified)return function(t,e){return A._isNumber(t)||A.warn(g(t)+' is a not valid value for "'+e+'" attribute. The value should be a number.'),t}}function m(){if(G.isUnminified)return function(t,e){return A._isNumber(t)||"auto"===t||A.warn(g(t)+' is a not valid value for "'+e+'" attribute. The value should be a number or "auto".'),t}}function _(){if(G.isUnminified)return function(t,e){return A._isString(t)||A.warn(g(t)+' is a not valid value for "'+e+'" attribute. The value should be a string.'),t}}function b(){if(G.isUnminified)return function(t,e){return!0===t||!1===t||A.warn(g(t)+' is a not valid value for "'+e+'" attribute. The value should be a boolean.'),t}}var x="get",S="set",w={addGetterSetter:function(t,e,i,n,r){this.addGetter(t,e,i),this.addSetter(t,e,n,r),this.addOverloadedGetterSetter(t,e)},addGetter:function(t,e,i){var n=x+A._capitalize(e);t.prototype[n]=t.prototype[n]||function(){var t=this.attrs[e];return void 0===t?i:t}},addSetter:function(t,e,i,n){var r=S+A._capitalize(e);t.prototype[r]||w.overWriteSetter(t,e,i,n)},overWriteSetter:function(t,e,i,n){var r=S+A._capitalize(e);t.prototype[r]=function(t){return i&&null!=t&&(t=i.call(this,t,e)),this._setAttr(e,t),n&&n.call(this),this}},addComponentsGetterSetter:function(t,n,e,r,o){var i,a,s=e.length,h=A._capitalize,d=x+h(n),l=S+h(n);t.prototype[d]=function(){var t={};for(i=0;i<s;i++)t[a=e[i]]=this.getAttr(n+h(a));return t};var c=function(i){if(G.isUnminified)return function(t,e){return A.isObject(t)||A.warn(g(t)+' is a not valid value for "'+e+'" attribute. The value should be an object with properties '+i),t}}(e);t.prototype[l]=function(t){var e,i=this.attrs[n];for(e in r&&(t=r.call(this,t)),c&&c.call(this,t,n),t)t.hasOwnProperty(e)&&this._setAttr(n+h(e),t[e]);return this._fireChangeEvent(n,i,t),o&&o.call(this),this},this.addOverloadedGetterSetter(t,n)},addOverloadedGetterSetter:function(t,e){var i=A._capitalize(e),n=S+i,r=x+i;t.prototype[e]=function(){return arguments.length?(this[n](arguments[0]),this):this[r]()}},addDeprecatedGetterSetter:function(t,e,i,n){A.error("Adding deprecated "+e);var r=x+A._capitalize(e),o=e+" property is deprecated and will be removed soon. Look at Konva change log for more information.";t.prototype[r]=function(){A.error(o);var t=this.attrs[e];return void 0===t?i:t},this.addSetter(t,e,n,function(){A.error(o)}),this.addOverloadedGetterSetter(t,e)},backCompat:function(a,t){A.each(t,function(t,e){var i=a.prototype[e],n=x+A._capitalize(t),r=S+A._capitalize(t);function o(){i.apply(this,arguments),A.error('"'+t+'" method is deprecated and will be removed soon. Use ""'+e+'" instead.')}a.prototype[t]=o,a.prototype[n]=o,a.prototype[r]=o})},afterSetFilter:function(){this._filterUpToDate=!1}},C=function(t,e){return(C=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)};function P(t,e){function i(){this.constructor=t}C(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var k=function(){return(k=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};var T=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"],M=(R.prototype.fillShape=function(t){t.fillEnabled()&&this._fill(t)},R.prototype._fill=function(t){},R.prototype.strokeShape=function(t){t.hasStroke()&&this._stroke(t)},R.prototype._stroke=function(t){},R.prototype.fillStrokeShape=function(t){this.fillShape(t),this.strokeShape(t)},R.prototype.getTrace=function(t){for(var e,i,n,r=this.traceArr,o=r.length,a="",s=0;s<o;s++)(i=(e=r[s]).method)?(n=e.args,a+=i,t?a+="()":A._isArray(n[0])?a+="(["+n.join(",")+"])":a+="("+n.join(",")+")"):(a+=e.property,t||(a+="="+e.val)),a+=";";return a},R.prototype.clearTrace=function(){this.traceArr=[]},R.prototype._trace=function(t){var e=this.traceArr;e.push(t),100<=e.length&&e.shift()},R.prototype.reset=function(){var t=this.getCanvas().getPixelRatio();this.setTransform(+t,0,0,+t,0,0)},R.prototype.getCanvas=function(){return this.canvas},R.prototype.clear=function(t){var e=this.getCanvas();t?this.clearRect(t.x||0,t.y||0,t.width||0,t.height||0):this.clearRect(0,0,e.getWidth()/e.pixelRatio,e.getHeight()/e.pixelRatio)},R.prototype._applyLineCap=function(t){var e=t.getLineCap();e&&this.setAttr("lineCap",e)},R.prototype._applyOpacity=function(t){var e=t.getAbsoluteOpacity();1!==e&&this.setAttr("globalAlpha",e)},R.prototype._applyLineJoin=function(t){var e=t.getLineJoin();e&&this.setAttr("lineJoin",e)},R.prototype.setAttr=function(t,e){this._context[t]=e},R.prototype.arc=function(t,e,i,n,r,o){this._context.arc(t,e,i,n,r,o)},R.prototype.arcTo=function(t,e,i,n,r){this._context.arcTo(t,e,i,n,r)},R.prototype.beginPath=function(){this._context.beginPath()},R.prototype.bezierCurveTo=function(t,e,i,n,r,o){this._context.bezierCurveTo(t,e,i,n,r,o)},R.prototype.clearRect=function(t,e,i,n){this._context.clearRect(t,e,i,n)},R.prototype.clip=function(){this._context.clip()},R.prototype.closePath=function(){this._context.closePath()},R.prototype.createImageData=function(t,e){var i=arguments;return 2===i.length?this._context.createImageData(t,e):1===i.length?this._context.createImageData(t):void 0},R.prototype.createLinearGradient=function(t,e,i,n){return this._context.createLinearGradient(t,e,i,n)},R.prototype.createPattern=function(t,e){return this._context.createPattern(t,e)},R.prototype.createRadialGradient=function(t,e,i,n,r,o){return this._context.createRadialGradient(t,e,i,n,r,o)},R.prototype.drawImage=function(t,e,i,n,r,o,a,s,h){var d=arguments,l=this._context;3===d.length?l.drawImage(t,e,i):5===d.length?l.drawImage(t,e,i,n,r):9===d.length&&l.drawImage(t,e,i,n,r,o,a,s,h)},R.prototype.ellipse=function(t,e,i,n,r,o,a,s){this._context.ellipse(t,e,i,n,r,o,a,s)},R.prototype.isPointInPath=function(t,e){return this._context.isPointInPath(t,e)},R.prototype.fill=function(){this._context.fill()},R.prototype.fillRect=function(t,e,i,n){this._context.fillRect(t,e,i,n)},R.prototype.strokeRect=function(t,e,i,n){this._context.strokeRect(t,e,i,n)},R.prototype.fillText=function(t,e,i){this._context.fillText(t,e,i)},R.prototype.measureText=function(t){return this._context.measureText(t)},R.prototype.getImageData=function(t,e,i,n){return this._context.getImageData(t,e,i,n)},R.prototype.lineTo=function(t,e){this._context.lineTo(t,e)},R.prototype.moveTo=function(t,e){this._context.moveTo(t,e)},R.prototype.rect=function(t,e,i,n){this._context.rect(t,e,i,n)},R.prototype.putImageData=function(t,e,i){this._context.putImageData(t,e,i)},R.prototype.quadraticCurveTo=function(t,e,i,n){this._context.quadraticCurveTo(t,e,i,n)},R.prototype.restore=function(){this._context.restore()},R.prototype.rotate=function(t){this._context.rotate(t)},R.prototype.save=function(){this._context.save()},R.prototype.scale=function(t,e){this._context.scale(t,e)},R.prototype.setLineDash=function(t){this._context.setLineDash?this._context.setLineDash(t):"mozDash"in this._context?this._context.mozDash=t:"webkitLineDash"in this._context&&(this._context.webkitLineDash=t)},R.prototype.getLineDash=function(){return this._context.getLineDash()},R.prototype.setTransform=function(t,e,i,n,r,o){this._context.setTransform(t,e,i,n,r,o)},R.prototype.stroke=function(){this._context.stroke()},R.prototype.strokeText=function(t,e,i,n){this._context.strokeText(t,e,i,n)},R.prototype.transform=function(t,e,i,n,r,o){this._context.transform(t,e,i,n,r,o)},R.prototype.translate=function(t,e){this._context.translate(t,e)},R.prototype._enableTrace=function(){for(var n,r=this,t=T.length,o=A._simplifyArray,i=this.setAttr,e=function(t){var e,i=r[t];r[t]=function(){return n=o(Array.prototype.slice.call(arguments,0)),e=i.apply(r,arguments),r._trace({method:t,args:n}),e}},a=0;a<t;a++)e(T[a]);r.setAttr=function(){i.apply(r,arguments);var t=arguments[0],e=arguments[1];"shadowOffsetX"!==t&&"shadowOffsetY"!==t&&"shadowBlur"!==t||(e/=this.canvas.getPixelRatio()),r._trace({property:t,val:e})}},R.prototype._applyGlobalCompositeOperation=function(t){var e=t.getGlobalCompositeOperation();"source-over"!==e&&this.setAttr("globalCompositeOperation",e)},R);function R(t){this.canvas=t,this._context=t._canvas.getContext("2d"),G.enableTrace&&(this.traceArr=[],this._enableTrace())}["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"].forEach(function(e){Object.defineProperty(M.prototype,e,{get:function(){return this._context[e]},set:function(t){this._context[e]=t}})});var I,O=(P(L,I=M),L.prototype._fillColor=function(t){var e=t.fill();this.setAttr("fillStyle",e),t._fillFunc(this)},L.prototype._fillPattern=function(t){var e=t.getFillPatternX(),i=t.getFillPatternY(),n=G.getAngle(t.getFillPatternRotation()),r=t.getFillPatternOffsetX(),o=t.getFillPatternOffsetY(),a=t.getFillPatternScaleX(),s=t.getFillPatternScaleY();(e||i)&&this.translate(e||0,i||0),n&&this.rotate(n),(a||s)&&this.scale(a,s),(r||o)&&this.translate(-1*r,-1*o),this.setAttr("fillStyle",t._getFillPattern()),t._fillFunc(this)},L.prototype._fillLinearGradient=function(t){var e=t._getLinearGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))},L.prototype._fillRadialGradient=function(t){var e=t._getRadialGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))},L.prototype._fill=function(t){var e,i,n,r=t.fill(),o=t.getFillPriority();r&&"color"===o?this._fillColor(t):(e=t.getFillPatternImage())&&"pattern"===o?this._fillPattern(t):(i=t.getFillLinearGradientColorStops())&&"linear-gradient"===o?this._fillLinearGradient(t):(n=t.getFillRadialGradientColorStops())&&"radial-gradient"===o?this._fillRadialGradient(t):r?this._fillColor(t):e?this._fillPattern(t):i?this._fillLinearGradient(t):n&&this._fillRadialGradient(t)},L.prototype._strokeLinearGradient=function(t){var e=t.getStrokeLinearGradientStartPoint(),i=t.getStrokeLinearGradientEndPoint(),n=t.getStrokeLinearGradientColorStops(),r=this.createLinearGradient(e.x,e.y,i.x,i.y);if(n){for(var o=0;o<n.length;o+=2)r.addColorStop(n[o],n[o+1]);this.setAttr("strokeStyle",r)}},L.prototype._stroke=function(t){var e,i=t.dash(),n=t.getStrokeScaleEnabled();t.hasStroke()&&(n||(this.save(),e=this.getCanvas().getPixelRatio(),this.setTransform(e,0,0,e,0,0)),this._applyLineCap(t),i&&t.dashEnabled()&&(this.setLineDash(i),this.setAttr("lineDashOffset",t.dashOffset())),this.setAttr("lineWidth",t.strokeWidth()),t.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)"),t.getStrokeLinearGradientColorStops()?this._strokeLinearGradient(t):this.setAttr("strokeStyle",t.stroke()),t._strokeFunc(this),n||this.restore())},L.prototype._applyShadow=function(t){var e=A,i=e.get(t.getShadowRGBA(),"black"),n=e.get(t.getShadowBlur(),5),r=e.get(t.getShadowOffset(),{x:0,y:0}),o=t.getAbsoluteScale(),a=this.canvas.getPixelRatio(),s=o.x*a,h=o.y*a;this.setAttr("shadowColor",i),this.setAttr("shadowBlur",n*Math.min(Math.abs(s),Math.abs(h))),this.setAttr("shadowOffsetX",r.x*s),this.setAttr("shadowOffsetY",r.y*h)},L);function L(){return null!==I&&I.apply(this,arguments)||this}var E,D,F=(P(B,E=M),B.prototype._fill=function(t){this.save(),this.setAttr("fillStyle",t.colorKey),t._fillFuncHit(this),this.restore()},B.prototype.strokeShape=function(t){t.hasHitStroke()&&this._stroke(t)},B.prototype._stroke=function(t){var e,i,n,r;t.hasHitStroke()&&((e=t.getStrokeScaleEnabled())||(this.save(),i=this.getCanvas().getPixelRatio(),this.setTransform(i,0,0,i,0,0)),this._applyLineCap(t),r="auto"===(n=t.hitStrokeWidth())?t.strokeWidth():n,this.setAttr("lineWidth",r),this.setAttr("strokeStyle",t.colorKey),t._strokeFuncHit(this),e||this.restore())},B);function B(){return null!==E&&E.apply(this,arguments)||this}var N=(z.prototype.getContext=function(){return this.context},z.prototype.getPixelRatio=function(){return this.pixelRatio},z.prototype.setPixelRatio=function(t){var e=this.pixelRatio;this.pixelRatio=t,this.setSize(this.getWidth()/e,this.getHeight()/e)},z.prototype.setWidth=function(t){this.width=this._canvas.width=t*this.pixelRatio,this._canvas.style.width=t+"px";var e=this.pixelRatio;this.getContext()._context.scale(e,e)},z.prototype.setHeight=function(t){this.height=this._canvas.height=t*this.pixelRatio,this._canvas.style.height=t+"px";var e=this.pixelRatio;this.getContext()._context.scale(e,e)},z.prototype.getWidth=function(){return this.width},z.prototype.getHeight=function(){return this.height},z.prototype.setSize=function(t,e){this.setWidth(t||0),this.setHeight(e||0)},z.prototype.toDataURL=function(t,e){try{return this._canvas.toDataURL(t,e)}catch(t){try{return this._canvas.toDataURL()}catch(t){return A.error("Unable to get data URL. "+t.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}},z);function z(t){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;var e=(t||{}).pixelRatio||G.pixelRatio||function(){if(D)return D;var t=A.createCanvasElement().getContext("2d");return D=(G._global.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)}();this.pixelRatio=e,this._canvas=A.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}w.addGetterSetter(N,"pixelRatio",void 0,v());var W,H=(P(Y,W=N),Y);function Y(t){void 0===t&&(t={width:0,height:0});var e=W.call(this,t)||this;return e.context=new O(e),e.setSize(t.width,t.height),e}var X,j=(P(U,X=N),U);function U(t){void 0===t&&(t={width:0,height:0});var e=X.call(this,t)||this;return e.hitCanvas=!0,e.context=new F(e),e.setSize(t.width,t.height),e}var q={get isDragging(){var e=!1;return q._dragElements.forEach(function(t){"dragging"===t.dragStatus&&(e=!0)}),e},justDragged:!1,get node(){var e;return q._dragElements.forEach(function(t){e=t.node}),e},_dragElements:new Map,_drag:function(a){q._dragElements.forEach(function(e,t){var i=e.node,n=i.getStage();n.setPointersPositions(a),void 0===e.pointerId&&(e.pointerId=A._getFirstPointerId(a));var r=n._changedPointerPositions.find(function(t){return t.id===e.pointerId});if(r){if("dragging"!==e.dragStatus){var o=i.dragDistance();if(Math.max(Math.abs(r.x-e.startPointerPos.x),Math.abs(r.y-e.startPointerPos.y))<o)return;if(i.startDrag({evt:a}),!i.isDragging())return}i._setDragPosition(a,e),i.fire("dragmove",{type:"dragmove",target:i,evt:a},!0)}})},_endDragBefore:function(r){q._dragElements.forEach(function(e,t){var i,n=e.node.getStage();r&&n.setPointersPositions(r),n._changedPointerPositions.find(function(t){return t.id===e.pointerId})&&("dragging"!==e.dragStatus&&"stopped"!==e.dragStatus||(q.justDragged=!0,G.listenClickTap=!1,e.dragStatus="stopped"),(i=e.node.getLayer()||e.node instanceof G.Stage&&e.node)&&i.draw())})},_endDragAfter:function(i){q._dragElements.forEach(function(t,e){"stopped"===t.dragStatus&&t.node.fire("dragend",{type:"dragend",target:t.node,evt:i},!0),"dragging"!==t.dragStatus&&q._dragElements.delete(e)})}};G.isBrowser&&(window.addEventListener("mouseup",q._endDragBefore,!0),window.addEventListener("touchend",q._endDragBefore,!0),window.addEventListener("mousemove",q._drag),window.addEventListener("touchmove",q._drag),window.addEventListener("mouseup",q._endDragAfter,!1),window.addEventListener("touchend",q._endDragAfter,!1));function V(t,e){t&&Q[t]===e&&delete Q[t]}function K(t,e){if(t){var i=J[t];if(i){for(var n=0;n<i.length;n++){i[n]._id===e&&i.splice(n,1)}0===i.length&&delete J[t]}}}var Q={},J={},Z="absoluteOpacity",$="absoluteTransform",tt="absoluteScale",et="canvas",it="listening",nt="mouseenter",rt="mouseleave",ot="transform",at="visible",st=["xChange.konva","yChange.konva","scaleXChange.konva","scaleYChange.konva","skewXChange.konva","skewYChange.konva","rotationChange.konva","offsetXChange.konva","offsetYChange.konva","transformsEnabledChange.konva"].join(" "),ht=new o,dt=1,lt=(ct.prototype.hasChildren=function(){return!1},ct.prototype.getChildren=function(){return ht},ct.prototype._clearCache=function(t){t?this._cache.delete(t):this._cache.clear()},ct.prototype._getCache=function(t,e){var i=this._cache.get(t);return void 0===i&&(i=e.call(this),this._cache.set(t,i)),i},ct.prototype._getCanvasCache=function(){return this._cache.get(et)},ct.prototype._clearSelfAndDescendantCache=function(e,t){this._clearCache(e),t&&e===$&&this.fire("_clearTransformCache"),this.isCached()||this.children&&this.children.each(function(t){t._clearSelfAndDescendantCache(e,!0)})},ct.prototype.clearCache=function(){return this._cache.delete(et),this._clearSelfAndDescendantCache(),this},ct.prototype.cache=function(t){var e=t||{},i={};void 0!==e.x&&void 0!==e.y&&void 0!==e.width&&void 0!==e.height||(i=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()}));var n=Math.ceil(e.width||i.width),r=Math.ceil(e.height||i.height),o=e.pixelRatio,a=void 0===e.x?i.x:e.x,s=void 0===e.y?i.y:e.y,h=e.offset||0,d=e.drawBorder||!1;if(n&&r){a-=h,s-=h;var l=new H({pixelRatio:o,width:n+=2*h,height:r+=2*h}),c=new H({pixelRatio:o,width:0,height:0}),p=new j({pixelRatio:1,width:n,height:r}),u=l.getContext(),f=p.getContext();return p.isCache=!0,this._cache.delete("canvas"),(this._filterUpToDate=!1)===e.imageSmoothingEnabled&&(l.getContext()._context.imageSmoothingEnabled=!1,c.getContext()._context.imageSmoothingEnabled=!1),u.save(),f.save(),u.translate(-a,-s),f.translate(-a,-s),this._isUnderCache=!0,this._clearSelfAndDescendantCache(Z),this._clearSelfAndDescendantCache(tt),this.drawScene(l,this,!0),this.drawHit(p,this,!0),this._isUnderCache=!1,u.restore(),f.restore(),d&&(u.save(),u.beginPath(),u.rect(0,0,n,r),u.closePath(),u.setAttr("strokeStyle","red"),u.setAttr("lineWidth",5),u.stroke(),u.restore()),this._cache.set(et,{scene:l,filter:c,hit:p,x:a,y:s}),this}A.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.")},ct.prototype.isCached=function(){return this._cache.has("canvas")},ct.prototype.getClientRect=function(t){throw new Error('abstract "getClientRect" method call')},ct.prototype._transformedRect=function(t,e){var i,n,r,o,a=[{x:t.x,y:t.y},{x:t.x+t.width,y:t.y},{x:t.x+t.width,y:t.y+t.height},{x:t.x,y:t.y+t.height}],s=this.getAbsoluteTransform(e);return a.forEach(function(t){var e=s.point(t);void 0===i&&(i=r=e.x,n=o=e.y),i=Math.min(i,e.x),n=Math.min(n,e.y),r=Math.max(r,e.x),o=Math.max(o,e.y)}),{x:i,y:n,width:r-i,height:o-n}},ct.prototype._drawCachedSceneCanvas=function(t){t.save(),t._applyOpacity(this),t._applyGlobalCompositeOperation(this);var e=this._getCanvasCache();t.translate(e.x,e.y);var i=this._getCachedSceneCanvas(),n=i.pixelRatio;t.drawImage(i._canvas,0,0,i.width/n,i.height/n),t.restore()},ct.prototype._drawCachedHitCanvas=function(t){var e=this._getCanvasCache(),i=e.hit;t.save(),t.translate(e.x,e.y),t.drawImage(i._canvas,0,0),t.restore()},ct.prototype._getCachedSceneCanvas=function(){var t,e,i,n,r=this.filters(),o=this._getCanvasCache(),a=o.scene,s=o.filter,h=s.getContext();if(r){if(!this._filterUpToDate){var d=a.pixelRatio;s.setSize(a.width/a.pixelRatio,a.height/a.pixelRatio);try{for(t=r.length,h.clear(),h.drawImage(a._canvas,0,0,a.getWidth()/d,a.getHeight()/d),e=h.getImageData(0,0,s.getWidth(),s.getHeight()),i=0;i<t;i++)"function"==typeof(n=r[i])?(n.call(this,e),h.putImageData(e,0,0)):A.error("Filter should be type of function, but got "+typeof n+" instead. Please check correct filters")}catch(t){A.error("Unable to apply filter. "+t.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return s}return a},ct.prototype.on=function(t,e){if(3===arguments.length)return this._delegate.apply(this,arguments);for(var i,n,r,o=t.split(" "),a=o.length,s=0;s<a;s++)n=(i=o[s].split("."))[0],r=i[1]||"",this.eventListeners[n]||(this.eventListeners[n]=[]),this.eventListeners[n].push({name:r,handler:e});return this},ct.prototype.off=function(t,e){var i,n,r,o,a,s=(t||"").split(" "),h=s.length;if(!t)for(n in this.eventListeners)this._off(n);for(i=0;i<h;i++)if(o=(r=s[i].split("."))[0],a=r[1],o)this.eventListeners[o]&&this._off(o,a,e);else for(n in this.eventListeners)this._off(n,a,e);return this},ct.prototype.dispatchEvent=function(t){var e={target:this,type:t.type,evt:t};return this.fire(t.type,e),this},ct.prototype.addEventListener=function(t,e){return this.on(t,function(t){e.call(this,t.evt)}),this},ct.prototype.removeEventListener=function(t){return this.off(t),this},ct.prototype._delegate=function(t,n,r){var o=this;this.on(t,function(t){for(var e=t.target.findAncestors(n,!0,o),i=0;i<e.length;i++)(t=A.cloneObject(t)).currentTarget=e[i],r.call(e[i],t)})},ct.prototype.remove=function(){return this.isDragging()&&this.stopDrag(),q._dragElements.delete(this._id),this._remove(),this},ct.prototype._clearCaches=function(){this._clearSelfAndDescendantCache($),this._clearSelfAndDescendantCache(Z),this._clearSelfAndDescendantCache(tt),this._clearSelfAndDescendantCache("stage"),this._clearSelfAndDescendantCache(at),this._clearSelfAndDescendantCache(it)},ct.prototype._remove=function(){this._clearCaches();var t=this.getParent();t&&t.children&&(t.children.splice(this.index,1),t._setChildrenIndices(),this.parent=null)},ct.prototype.destroy=function(){V(this.id(),this);for(var t=(this.name()||"").split(/\s/g),e=0;e<t.length;e++){var i=t[e];K(i,this._id)}return this.remove(),this},ct.prototype.getAttr=function(t){var e="get"+A._capitalize(t);return A._isFunction(this[e])?this[e]():this.attrs[t]},ct.prototype.getAncestors=function(){for(var t=this.getParent(),e=new o;t;)e.push(t),t=t.getParent();return e},ct.prototype.getAttrs=function(){return this.attrs||{}},ct.prototype.setAttrs=function(t){var e,i;if(!t)return this;for(e in t)"children"!==e&&(i="set"+A._capitalize(e),A._isFunction(this[i])?this[i](t[e]):this._setAttr(e,t[e]));return this},ct.prototype.isListening=function(){return this._getCache(it,this._isListening)},ct.prototype._isListening=function(){var t=this.listening(),e=this.getParent();return"inherit"===t?!e||e.isListening():t},ct.prototype.isVisible=function(){return this._getCache(at,this._isVisible)},ct.prototype._isVisible=function(t){var e=this.visible(),i=this.getParent();return"inherit"===e?!i||i===t||i._isVisible(t):t&&t!==i?e&&i._isVisible(t):e},ct.prototype.shouldDrawHit=function(){var t=this.getLayer();return!t&&this.isListening()&&this.isVisible()||t&&t.hitGraphEnabled()&&this.isListening()&&this.isVisible()},ct.prototype.show=function(){return this.visible(!0),this},ct.prototype.hide=function(){return this.visible(!1),this},ct.prototype.getZIndex=function(){return this.index||0},ct.prototype.getAbsoluteZIndex=function(){var i,n,r,o,a=this.getDepth(),s=this,h=0;return"Stage"!==s.nodeType&&function t(e){for(i=[],n=e.length,r=0;r<n;r++)o=e[r],h++,"Shape"!==o.nodeType&&(i=i.concat(o.getChildren().toArray())),o._id===s._id&&(r=n);0<i.length&&i[0].getDepth()<=a&&t(i)}(s.getStage().getChildren()),h},ct.prototype.getDepth=function(){for(var t=0,e=this.parent;e;)t++,e=e.parent;return t},ct.prototype._batchTransformChanges=function(t){this._batchingTransformChange=!0,t(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(ot),this._clearSelfAndDescendantCache($,!0)),this._needClearTransformCache=!1},ct.prototype.setPosition=function(t){var e=this;return this._batchTransformChanges(function(){e.x(t.x),e.y(t.y)}),this},ct.prototype.getPosition=function(){return{x:this.x(),y:this.y()}},ct.prototype.getAbsolutePosition=function(t){for(var e=!1,i=this.parent;i;){if(i.isCached()){e=!0;break}i=i.parent}e&&!t&&(t=!0);var n=this.getAbsoluteTransform(t).getMatrix(),r=new l,o=this.offset();return r.m=n.slice(),r.translate(o.x,o.y),r.getTranslation()},ct.prototype.setAbsolutePosition=function(t){var e,i=this._clearTransform();return this.attrs.x=i.x,this.attrs.y=i.y,delete i.x,delete i.y,this._clearCache(ot),(e=this._getAbsoluteTransform()).invert(),e.translate(t.x,t.y),t={x:this.attrs.x+e.getTranslation().x,y:this.attrs.y+e.getTranslation().y},this._setTransform(i),this.setPosition({x:t.x,y:t.y}),this},ct.prototype._setTransform=function(t){var e;for(e in t)this.attrs[e]=t[e]},ct.prototype._clearTransform=function(){var t={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,t},ct.prototype.move=function(t){var e=t.x,i=t.y,n=this.x(),r=this.y();return void 0!==e&&(n+=e),void 0!==i&&(r+=i),this.setPosition({x:n,y:r}),this},ct.prototype._eachAncestorReverse=function(t,e){var i,n,r=[],o=this.getParent();if(e&&e._id===this._id)t(this);else{for(r.unshift(this);o&&(!e||o._id!==e._id);)r.unshift(o),o=o.parent;for(i=r.length,n=0;n<i;n++)t(r[n])}},ct.prototype.rotate=function(t){return this.rotation(this.rotation()+t),this},ct.prototype.moveToTop=function(){if(!this.parent)return A.warn("Node has no parent. moveToTop function is ignored."),!1;var t=this.index;return this.parent.children.splice(t,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0},ct.prototype.moveUp=function(){if(!this.parent)return A.warn("Node has no parent. moveUp function is ignored."),!1;var t=this.index;return t<this.parent.getChildren().length-1&&(this.parent.children.splice(t,1),this.parent.children.splice(t+1,0,this),this.parent._setChildrenIndices(),!0)},ct.prototype.moveDown=function(){if(!this.parent)return A.warn("Node has no parent. moveDown function is ignored."),!1;var t=this.index;return 0<t&&(this.parent.children.splice(t,1),this.parent.children.splice(t-1,0,this),this.parent._setChildrenIndices(),!0)},ct.prototype.moveToBottom=function(){if(!this.parent)return A.warn("Node has no parent. moveToBottom function is ignored."),!1;var t=this.index;return 0<t&&(this.parent.children.splice(t,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0)},ct.prototype.setZIndex=function(t){if(!this.parent)return A.warn("Node has no parent. zIndex parameter is ignored."),this;(t<0||t>=this.parent.children.length)&&A.warn("Unexpected value "+t+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");var e=this.index;return this.parent.children.splice(e,1),this.parent.children.splice(t,0,this),this.parent._setChildrenIndices(),this},ct.prototype.getAbsoluteOpacity=function(){return this._getCache(Z,this._getAbsoluteOpacity)},ct.prototype._getAbsoluteOpacity=function(){var t=this.opacity(),e=this.getParent();return e&&!e._isUnderCache&&(t*=e.getAbsoluteOpacity()),t},ct.prototype.moveTo=function(t){return this.getParent()!==t&&(this._remove(),t.add(this)),this},ct.prototype.toObject=function(){var t,e,i,n={},r=this.getAttrs();for(t in n.attrs={},r)e=r[t],A.isObject(e)&&!A._isPlainObject(e)&&!A._isArray(e)||(i="function"==typeof this[t]&&this[t],delete r[t],(i?i.call(this):null)!==(r[t]=e)&&(n.attrs[t]=e));return n.className=this.getClassName(),A._prepareToStringify(n)},ct.prototype.toJSON=function(){return JSON.stringify(this.toObject())},ct.prototype.getParent=function(){return this.parent},ct.prototype.findAncestors=function(t,e,i){var n=[];e&&this._isMatch(t)&&n.push(this);for(var r=this.parent;r;){if(r===i)return n;r._isMatch(t)&&n.push(r),r=r.parent}return n},ct.prototype.isAncestorOf=function(t){return!1},ct.prototype.findAncestor=function(t,e,i){return this.findAncestors(t,e,i)[0]},ct.prototype._isMatch=function(t){if(!t)return!1;if("function"==typeof t)return t(this);for(var e,i=t.replace(/ /g,"").split(","),n=i.length,r=0;r<n;r++)if(e=i[r],A.isValidSelector(e)||(A.warn('Selector "'+e+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),A.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),A.warn("Konva is awesome, right?")),"#"===e.charAt(0)){if(this.id()===e.slice(1))return!0}else if("."===e.charAt(0)){if(this.hasName(e.slice(1)))return!0}else if(this.className===e||this.nodeType===e)return!0;return!1},ct.prototype.getLayer=function(){var t=this.getParent();return t?t.getLayer():null},ct.prototype.getStage=function(){return this._getCache("stage",this._getStage)},ct.prototype._getStage=function(){var t=this.getParent();return t?t.getStage():void 0},ct.prototype.fire=function(t,e,i){return void 0===e&&(e={}),e.target=e.target||this,i?this._fireAndBubble(t,e):this._fire(t,e),this},ct.prototype.getAbsoluteTransform=function(t){return t?this._getAbsoluteTransform(t):this._getCache($,this._getAbsoluteTransform)},ct.prototype._getAbsoluteTransform=function(t){var i;if(t)return i=new l,this._eachAncestorReverse(function(t){var e=t.transformsEnabled();"all"===e?i.multiply(t.getTransform()):"position"===e&&i.translate(t.x()-t.offsetX(),t.y()-t.offsetY())},t),i;i=this.parent?this.parent.getAbsoluteTransform().copy():new l;var e=this.transformsEnabled();return"all"===e?i.multiply(this.getTransform()):"position"===e&&i.translate(this.x()-this.offsetX(),this.y()-this.offsetY()),i},ct.prototype.getAbsoluteScale=function(t){for(var e=this;e;)e._isUnderCache&&(t=e),e=e.getParent();var i=this.getAbsoluteTransform(t).decompose();return{x:i.scaleX,y:i.scaleY}},ct.prototype.getAbsoluteRotation=function(){return this.getAbsoluteTransform().decompose().rotation},ct.prototype.getTransform=function(){return this._getCache(ot,this._getTransform)},ct.prototype._getTransform=function(){var t=new l,e=this.x(),i=this.y(),n=G.getAngle(this.rotation()),r=this.scaleX(),o=this.scaleY(),a=this.skewX(),s=this.skewY(),h=this.offsetX(),d=this.offsetY();return 0===e&&0===i||t.translate(e,i),0!==n&&t.rotate(n),0===a&&0===s||t.skew(a,s),1===r&&1===o||t.scale(r,o),0===h&&0===d||t.translate(-1*h,-1*d),t},ct.prototype.clone=function(t){var e,i,n,r,o,a=A.cloneObject(this.attrs);for(e in t)a[e]=t[e];var s=new this.constructor(a);for(e in this.eventListeners)for(n=(i=this.eventListeners[e]).length,r=0;r<n;r++)(o=i[r]).name.indexOf("konva")<0&&(s.eventListeners[e]||(s.eventListeners[e]=[]),s.eventListeners[e].push(o));return s},ct.prototype._toKonvaCanvas=function(t){t=t||{};var e=this.getClientRect(),i=this.getStage(),n=void 0!==t.x?t.x:e.x,r=void 0!==t.y?t.y:e.y,o=t.pixelRatio||1,a=new H({width:t.width||e.width||(i?i.width():0),height:t.height||e.height||(i?i.height():0),pixelRatio:o}),s=a.getContext();return s.save(),(n||r)&&s.translate(-1*n,-1*r),this.drawScene(a),s.restore(),a},ct.prototype.toCanvas=function(t){return this._toKonvaCanvas(t)._canvas},ct.prototype.toDataURL=function(t){var e=(t=t||{}).mimeType||null,i=t.quality||null,n=this._toKonvaCanvas(t).toDataURL(e,i);return t.callback&&t.callback(n),n},ct.prototype.toImage=function(t){if(!t||!t.callback)throw"callback required for toImage method config argument";var e=t.callback;delete t.callback,A._urlToImage(this.toDataURL(t),function(t){e(t)})},ct.prototype.setSize=function(t){return this.width(t.width),this.height(t.height),this},ct.prototype.getSize=function(){return{width:this.width(),height:this.height()}},ct.prototype.getClassName=function(){return this.className||this.nodeType},ct.prototype.getType=function(){return this.nodeType},ct.prototype.getDragDistance=function(){return void 0!==this.attrs.dragDistance?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():G.dragDistance},ct.prototype._off=function(t,e,i){for(var n,r,o=this.eventListeners[t],a=0;a<o.length;a++)if(n=o[a].name,r=o[a].handler,!("konva"===n&&"konva"!==e||e&&n!==e||i&&i!==r)){if(o.splice(a,1),0===o.length){delete this.eventListeners[t];break}a--}},ct.prototype._fireChangeEvent=function(t,e,i){this._fire(t+"Change",{oldVal:e,newVal:i})},ct.prototype.setId=function(t){var e,i,n=this.id();return V(n,this),e=this,(i=t)&&(Q[i]=e),this._setAttr("id",t),this},ct.prototype.setName=function(t){for(var e,i,n,r=(this.name()||"").split(/\s/g),o=(t||"").split(/\s/g),a=0;a<r.length;a++)e=r[a],-1===o.indexOf(e)&&e&&K(e,this._id);for(a=0;a<o.length;a++)e=o[a],-1===r.indexOf(e)&&e&&(i=this,(n=e)&&(J[n]||(J[n]=[]),J[n].push(i)));return this._setAttr("name",t),this},ct.prototype.addName=function(t){var e,i;return this.hasName(t)||(i=(e=this.name())?e+" "+t:t,this.setName(i)),this},ct.prototype.hasName=function(t){if(!t)return!1;var e=this.name();return!!e&&-1!==(e||"").split(/\s/g).indexOf(t)},ct.prototype.removeName=function(t){var e=(this.name()||"").split(/\s/g),i=e.indexOf(t);return-1!==i&&(e.splice(i,1),this.setName(e.join(" "))),this},ct.prototype.setAttr=function(t,e){var i=this["set"+A._capitalize(t)];return A._isFunction(i)?i.call(this,e):this._setAttr(t,e),this},ct.prototype._setAttr=function(t,e){var i=this.attrs[t];i===e&&!A.isObject(e)||(null==e?delete this.attrs[t]:this.attrs[t]=e,this._fireChangeEvent(t,i,e))},ct.prototype._setComponentAttr=function(t,e,i){var n;void 0!==i&&((n=this.attrs[t])||(this.attrs[t]=this.getAttr(t)),this.attrs[t][e]=i,this._fireChangeEvent(t,n,i))},ct.prototype._fireAndBubble=function(t,e,i){var n;e&&"Shape"===this.nodeType&&(e.target=this),(t===nt||t===rt)&&(i&&(this===i||this.isAncestorOf&&this.isAncestorOf(i))||"Stage"===this.nodeType&&!i)||(this._fire(t,e),n=(t===nt||t===rt)&&i&&i.isAncestorOf&&i.isAncestorOf(this)&&!i.isAncestorOf(this.parent),(e&&!e.cancelBubble||!e)&&this.parent&&this.parent.isListening()&&!n&&(i&&i.parent?this._fireAndBubble.call(this.parent,t,e,i):this._fireAndBubble.call(this.parent,t,e)))},ct.prototype._fire=function(t,e){var i,n=this.eventListeners[t];if(n)for((e=e||{}).currentTarget=this,e.type=t,i=0;i<n.length;i++)n[i].handler.call(this,e)},ct.prototype.draw=function(){return this.drawScene(),this.drawHit(),this},ct.prototype._createDragElement=function(t){var e=t?t.pointerId:void 0,i=this.getStage(),n=this.getAbsolutePosition(),r=i._getPointerById(e)||i._changedPointerPositions[0]||n;q._dragElements.set(this._id,{node:this,startPointerPos:r,offset:{x:r.x-n.x,y:r.y-n.y},dragStatus:"ready",pointerId:e})},ct.prototype.startDrag=function(t){q._dragElements.has(this._id)||this._createDragElement(t),q._dragElements.get(this._id).dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:t&&t.evt},!0)},ct.prototype._setDragPosition=function(t,e){var i,n,r,o=this.getStage()._getPointerById(e.pointerId);o&&(i={x:o.x-e.offset.x,y:o.y-e.offset.y},void 0!==(n=this.dragBoundFunc())&&((r=n.call(this,i,t))?i=r:A.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")),this._lastPos&&this._lastPos.x===i.x&&this._lastPos.y===i.y||(this.setAbsolutePosition(i),this.getLayer()?this.getLayer().batchDraw():this.getStage()&&this.getStage().batchDraw()),this._lastPos=i)},ct.prototype.stopDrag=function(t){var e=q._dragElements.get(this._id);e&&(e.dragStatus="stopped"),q._endDragBefore(t),q._endDragAfter(t)},ct.prototype.setDraggable=function(t){this._setAttr("draggable",t),this._dragChange()},ct.prototype.isDragging=function(){var t=q._dragElements.get(this._id);return!!t&&"dragging"===t.dragStatus},ct.prototype._listenDrag=function(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",function(t){var e,i=this;void 0!==t.evt.button&&!(0<=G.dragButtons.indexOf(t.evt.button))||this.isDragging()||(e=!1,q._dragElements.forEach(function(t){i.isAncestorOf(t.node)&&(e=!0)}),e||this._createDragElement(t))})},ct.prototype._dragChange=function(){this.attrs.draggable?this._listenDrag():(this._dragCleanup(),this.getStage()&&q._dragElements.has(this._id)&&this.stopDrag())},ct.prototype._dragCleanup=function(){this.off("mousedown.konva"),this.off("touchstart.konva")},ct.create=function(t,e){return A._isString(t)&&(t=JSON.parse(t)),this._createNode(t,e)},ct._createNode=function(t,e){var i,n,r=ct.prototype.getClassName.call(t),o=t.children;e&&(t.attrs.container=e),s[r]||(A.warn('Can not find a node with class name "'+r+'". Fallback to "Shape".'),r="Shape");var a=new s[r](t.attrs);if(o)for(i=o.length,n=0;n<i;n++)a.add(ct._createNode(o[n]));return a},ct);function ct(t){var e=this;this._id=dt++,this.eventListeners={},this.attrs={},this.index=0,this.parent=null,this._cache=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this.children=ht,this._dragEventId=null,this.setAttrs(t),this.on(st,function(){e._batchingTransformChange?e._needClearTransformCache=!0:(e._clearCache(ot),e._clearSelfAndDescendantCache($))}),this.on("visibleChange.konva",function(){e._clearSelfAndDescendantCache(at)}),this.on("listeningChange.konva",function(){e._clearSelfAndDescendantCache(it)}),this.on("opacityChange.konva",function(){e._clearSelfAndDescendantCache(Z)})}lt.prototype.nodeType="Node",lt.prototype._attrsAffectingSize=[],w.addGetterSetter(lt,"zIndex"),w.addGetterSetter(lt,"absolutePosition"),w.addGetterSetter(lt,"position"),w.addGetterSetter(lt,"x",0,v()),w.addGetterSetter(lt,"y",0,v()),w.addGetterSetter(lt,"globalCompositeOperation","source-over",_()),w.addGetterSetter(lt,"opacity",1,v()),w.addGetterSetter(lt,"name","",_()),w.addGetterSetter(lt,"id","",_()),w.addGetterSetter(lt,"rotation",0,v()),w.addComponentsGetterSetter(lt,"scale",["x","y"]),w.addGetterSetter(lt,"scaleX",1,v()),w.addGetterSetter(lt,"scaleY",1,v()),w.addComponentsGetterSetter(lt,"skew",["x","y"]),w.addGetterSetter(lt,"skewX",0,v()),w.addGetterSetter(lt,"skewY",0,v()),w.addComponentsGetterSetter(lt,"offset",["x","y"]),w.addGetterSetter(lt,"offsetX",0,v()),w.addGetterSetter(lt,"offsetY",0,v()),w.addGetterSetter(lt,"dragDistance",null,v()),w.addGetterSetter(lt,"width",0,v()),w.addGetterSetter(lt,"height",0,v()),w.addGetterSetter(lt,"listening","inherit",function(t){return!0===t||!1===t||"inherit"===t||A.warn(t+' is a not valid value for "listening" attribute. The value may be true, false or "inherit".'),t}),w.addGetterSetter(lt,"preventDefault",!0,b()),w.addGetterSetter(lt,"filters",null,function(t){return this._filterUpToDate=!1,t}),w.addGetterSetter(lt,"visible","inherit",function(t){return!0===t||!1===t||"inherit"===t||A.warn(t+' is a not valid value for "visible" attribute. The value may be true, false or "inherit".'),t}),w.addGetterSetter(lt,"transformsEnabled","all",_()),w.addGetterSetter(lt,"size"),w.addGetterSetter(lt,"dragBoundFunc"),w.addGetterSetter(lt,"draggable",!1,b()),w.backCompat(lt,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"}),o.mapMethods(lt);var pt,ut=(P(ft,pt=lt),ft.prototype.getChildren=function(e){if(!e)return this.children;var i=new o;return this.children.each(function(t){e(t)&&i.push(t)}),i},ft.prototype.hasChildren=function(){return 0<this.getChildren().length},ft.prototype.removeChildren=function(){for(var t,e=0;e<this.children.length;e++)(t=this.children[e]).parent=null,t.index=0,t.remove();return this.children=new o,this},ft.prototype.destroyChildren=function(){for(var t,e=0;e<this.children.length;e++)(t=this.children[e]).parent=null,t.index=0,t.destroy();return this.children=new o,this},ft.prototype.add=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(1<arguments.length){for(var i=0;i<arguments.length;i++)this.add(arguments[i]);return this}var n=t[0];if(n.getParent())return n.moveTo(this),this;var r=this.children;return this._validateAdd(n),n._clearCaches(),n.index=r.length,n.parent=this,r.push(n),this._fire("add",{child:n}),this},ft.prototype.destroy=function(){return this.hasChildren()&&this.destroyChildren(),pt.prototype.destroy.call(this),this},ft.prototype.find=function(t){return this._generalFind(t,!1)},ft.prototype.get=function(t){return A.warn("collection.get() method is deprecated. Please use collection.find() instead."),this.find(t)},ft.prototype.findOne=function(t){var e=this._generalFind(t,!0);return 0<e.length?e[0]:void 0},ft.prototype._generalFind=function(i,n){var r=[];return this._descendants(function(t){var e=t._isMatch(i);return e&&r.push(t),!(!e||!n)}),o.toCollection(r)},ft.prototype._descendants=function(t){for(var e=0;e<this.children.length;e++){var i=this.children[e];if(t(i))return!0;if(i.hasChildren()&&i._descendants(t))return!0}return!1},ft.prototype.toObject=function(){var t=lt.prototype.toObject.call(this);t.children=[];for(var e=this.getChildren(),i=e.length,n=0;n<i;n++){var r=e[n];t.children.push(r.toObject())}return t},ft.prototype.isAncestorOf=function(t){for(var e=t.getParent();e;){if(e._id===this._id)return!0;e=e.getParent()}return!1},ft.prototype.clone=function(t){var e=lt.prototype.clone.call(this,t);return this.getChildren().each(function(t){e.add(t.clone())}),e},ft.prototype.getAllIntersections=function(e){var i=[];return this.find("Shape").each(function(t){t.isVisible()&&t.intersects(e)&&i.push(t)}),i},ft.prototype._setChildrenIndices=function(){this.children.each(function(t,e){t.index=e})},ft.prototype.drawScene=function(t,e,i){var n=this.getLayer(),r=t||n&&n.getCanvas(),o=r&&r.getContext(),a=this._getCanvasCache(),s=a&&a.scene;return(this.isVisible()||i)&&(!i&&s?(o.save(),n._applyTransform(this,o,e),this._drawCachedSceneCanvas(o),o.restore()):this._drawChildren(r,"drawScene",e,!1,i,i)),this},ft.prototype.drawHit=function(t,e,i){var n=this.getLayer(),r=t||n&&n.hitCanvas,o=r&&r.getContext(),a=this._getCanvasCache(),s=a&&a.hit;return(this.shouldDrawHit(r)||i)&&(!i&&s?(o.save(),n._applyTransform(this,o,e),this._drawCachedHitCanvas(o),o.restore()):this._drawChildren(r,"drawHit",e,!1,i,i)),this},ft.prototype._drawChildren=function(e,i,n,r,o,t){var a,s,h,d,l=this.getLayer(),c=e&&e.getContext(),p=this.clipWidth(),u=this.clipHeight(),f=this.clipFunc(),g=p&&u||f;g&&l&&(c.save(),d=(h=this.getAbsoluteTransform(n)).getMatrix(),c.transform(d[0],d[1],d[2],d[3],d[4],d[5]),c.beginPath(),f?f.call(this,c,this):(a=this.clipX(),s=this.clipY(),c.rect(a,s,p,u)),c.clip(),d=h.copy().invert().getMatrix(),c.transform(d[0],d[1],d[2],d[3],d[4],d[5]));var y="source-over"!==this.globalCompositeOperation()&&!t&&"drawScene"===i;y&&l&&(c.save(),c._applyGlobalCompositeOperation(this)),this.children.each(function(t){t[i](e,n,r,o)}),y&&l&&c.restore(),g&&l&&c.restore()},ft.prototype.shouldDrawHit=function(t){if(t&&t.isCache)return!0;var e=this.getLayer(),i=!1;q._dragElements.forEach(function(t){"dragging"===t.dragStatus&&t.node.getLayer()===e&&(i=!0)});var n=!G.hitOnDragEnabled&&i;return e&&e.hitGraphEnabled()&&this.isVisible()&&!n},ft.prototype.getClientRect=function(i){var n,r,o,a,t=(i=i||{}).skipTransform,e=i.relativeTo,s={x:1/0,y:1/0,width:0,height:0},h=this;this.children.each(function(t){var e;t.visible()&&(0===(e=t.getClientRect({relativeTo:h,skipShadow:i.skipShadow,skipStroke:i.skipStroke})).width&&0===e.height||(a=void 0===n?(n=e.x,r=e.y,o=e.x+e.width,e.y+e.height):(n=Math.min(n,e.x),r=Math.min(r,e.y),o=Math.max(o,e.x+e.width),Math.max(a,e.y+e.height))))});for(var d=this.find("Shape"),l=!1,c=0;c<d.length;c++)if(d[c]._isVisible(this)){l=!0;break}return s=l&&void 0!==n?{x:n,y:r,width:o-n,height:a-r}:{x:0,y:0,width:0,height:0},t?s:this._transformedRect(s,e)},ft);function ft(){var t=null!==pt&&pt.apply(this,arguments)||this;return t.children=new o,t}w.addComponentsGetterSetter(ut,"clip",["x","y","width","height"]),w.addGetterSetter(ut,"clipX",void 0,v()),w.addGetterSetter(ut,"clipY",void 0,v()),w.addGetterSetter(ut,"clipWidth",void 0,v()),w.addGetterSetter(ut,"clipHeight",void 0,v()),w.addGetterSetter(ut,"clipFunc"),o.mapMethods(ut);var gt=new Map,yt=void 0!==G._global.PointerEvent;function vt(t){return gt.get(t)}function mt(t){return{evt:t,pointerId:t.pointerId}}function _t(t,e){return gt.get(t)===e}function bt(t,e){xt(t),e.getStage()&&(gt.set(t,e),yt&&e._fire("gotpointercapture",mt(new PointerEvent("gotpointercapture"))))}function xt(t){var e,i=gt.get(t);i&&((e=i.getStage())&&e.content,gt.delete(t),yt&&i._fire("lostpointercapture",mt(new PointerEvent("lostpointercapture"))))}var St="mouseout",wt="mouseleave",Ct="mouseover",Pt="mouseenter",kt="mousemove",Tt="mousedown",At="mouseup",Mt="pointermove",Gt="pointerdown",Rt="pointerup",It="contextmenu",Ot="dblclick",Lt="touchstart",Et="touchend",Dt="touchmove",Ft="wheel",Bt="_",Nt=[Pt,Tt,kt,At,St,Lt,Dt,Et,Ct,Ft,It,Gt,Mt,Rt,"pointercancel","lostpointercapture"],zt=Nt.length;function Wt(e,i){e.content.addEventListener(i,function(t){e[Bt+i](t)},!1)}var Ht=[];function Yt(t){return void 0===t&&(t={}),(t.clipFunc||t.clipWidth||t.clipHeight)&&A.warn("Stage does not support clipping. Please use clip for Layers or Groups."),t}var Xt,jt=(P(Ut,Xt=ut),Ut.prototype._validateAdd=function(t){var e="Layer"===t.getType(),i="FastLayer"===t.getType();e||i||A.throw("You may only add layers to the stage.")},Ut.prototype._checkVisibility=function(){var t;this.content&&(t=this.visible()?"":"none",this.content.style.display=t)},Ut.prototype.setContainer=function(t){var e,i;if("string"==typeof t&&!(t="."===t.charAt(0)?(e=t.slice(1),document.getElementsByClassName(e)[0]):(i="#"!==t.charAt(0)?t:t.slice(1),document.getElementById(i))))throw"Can not find container in document with id "+i;return this._setAttr("container",t),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),t.appendChild(this.content)),this},Ut.prototype.shouldDrawHit=function(){return!0},Ut.prototype.clear=function(){for(var t=this.children,e=t.length,i=0;i<e;i++)t[i].clear();return this},Ut.prototype.clone=function(t){return(t=t||{}).container=document.createElement("div"),ut.prototype.clone.call(this,t)},Ut.prototype.destroy=function(){Xt.prototype.destroy.call(this);var t=this.content;t&&A._isInDocument(t)&&this.container().removeChild(t);var e=Ht.indexOf(this);return-1<e&&Ht.splice(e,1),this},Ut.prototype.getPointerPosition=function(){var t=this._pointerPositions[0]||this._changedPointerPositions[0];return t?{x:t.x,y:t.y}:(A.warn("Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);"),null)},Ut.prototype._getPointerById=function(e){return this._pointerPositions.find(function(t){return t.id===e})},Ut.prototype.getPointersPositions=function(){return this._pointerPositions},Ut.prototype.getStage=function(){return this},Ut.prototype.getContent=function(){return this.content},Ut.prototype._toKonvaCanvas=function(i){var n=(i=i||{}).x||0,r=i.y||0,t=new H({width:i.width||this.width(),height:i.height||this.height(),pixelRatio:i.pixelRatio||1}),o=t.getContext()._context,e=this.children;return(n||r)&&o.translate(-1*n,-1*r),e.each(function(t){var e;t.isVisible()&&(e=t._toKonvaCanvas(i),o.drawImage(e._canvas,n,r,e.getWidth()/e.getPixelRatio(),e.getHeight()/e.getPixelRatio()))}),t},Ut.prototype.getIntersection=function(t,e){if(!t)return null;for(var i,n=this.children,r=n.length-1;0<=r;r--)if(i=n[r].getIntersection(t,e))return i;return null},Ut.prototype._resizeDOM=function(){var e=this.width(),i=this.height();this.content&&(this.content.style.width=e+"px",this.content.style.height=i+"px"),this.bufferCanvas.setSize(e,i),this.bufferHitCanvas.setSize(e,i),this.children.each(function(t){t.setSize({width:e,height:i}),t.draw()})},Ut.prototype.add=function(t){if(1<arguments.length){for(var e=0;e<arguments.length;e++)this.add(arguments[e]);return this}Xt.prototype.add.call(this,t);var i=this.children.length;return 5<i&&A.warn("The stage has "+i+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),t.setSize({width:this.width(),height:this.height()}),t.draw(),G.isBrowser&&this.content.appendChild(t.canvas._canvas),this},Ut.prototype.getParent=function(){return null},Ut.prototype.getLayer=function(){return null},Ut.prototype.hasPointerCapture=function(t){return _t(t,this)},Ut.prototype.setPointerCapture=function(t){bt(t,this)},Ut.prototype.releaseCapture=function(t){xt(t)},Ut.prototype.getLayers=function(){return this.getChildren()},Ut.prototype._bindContentEvents=function(){if(G.isBrowser)for(var t=0;t<zt;t++)Wt(this,Nt[t])},Ut.prototype._mouseenter=function(t){this.setPointersPositions(t),this._fire(Pt,{evt:t,target:this,currentTarget:this})},Ut.prototype._mouseover=function(t){this.setPointersPositions(t),this._fire("contentMouseover",{evt:t}),this._fire(Ct,{evt:t,target:this,currentTarget:this})},Ut.prototype._mouseout=function(t){var e;this.setPointersPositions(t);var i=null!==(e=this.targetShape)&&void 0!==e&&e.getStage()?this.targetShape:null,n=!q.isDragging||G.hitOnDragEnabled;i&&n?(i._fireAndBubble(St,{evt:t}),i._fireAndBubble(wt,{evt:t}),this._fire(wt,{evt:t,target:this,currentTarget:this}),this.targetShape=null):n&&(this._fire(wt,{evt:t,target:this,currentTarget:this}),this._fire(St,{evt:t,target:this,currentTarget:this})),this.pointerPos=void 0,this._pointerPositions=[],this._fire("contentMouseout",{evt:t})},Ut.prototype._mousemove=function(t){var e;if(G.UA.ieMobile)return this._touchmove(t);this.setPointersPositions(t);var i,n=A._getFirstPointerId(t),r=null!==(e=this.targetShape)&&void 0!==e&&e.getStage()?this.targetShape:null,o=!q.isDragging||G.hitOnDragEnabled;o&&((i=this.getIntersection(this.getPointerPosition()))&&i.isListening()?o&&r!==i?(r&&(r._fireAndBubble(St,{evt:t,pointerId:n},i),r._fireAndBubble(wt,{evt:t,pointerId:n},i)),i._fireAndBubble(Ct,{evt:t,pointerId:n},r),i._fireAndBubble(Pt,{evt:t,pointerId:n},r),i._fireAndBubble(kt,{evt:t,pointerId:n}),this.targetShape=i):i._fireAndBubble(kt,{evt:t,pointerId:n}):(r&&o&&(r._fireAndBubble(St,{evt:t,pointerId:n}),r._fireAndBubble(wt,{evt:t,pointerId:n}),this._fire(Ct,{evt:t,target:this,currentTarget:this,pointerId:n}),this.targetShape=null),this._fire(kt,{evt:t,target:this,currentTarget:this,pointerId:n})),this._fire("contentMousemove",{evt:t})),t.cancelable&&t.preventDefault()},Ut.prototype._mousedown=function(t){if(G.UA.ieMobile)return this._touchstart(t);this.setPointersPositions(t);var e=A._getFirstPointerId(t),i=this.getIntersection(this.getPointerPosition());q.justDragged=!1,G.listenClickTap=!0,i&&i.isListening()?(this.clickStartShape=i)._fireAndBubble(Tt,{evt:t,pointerId:e}):this._fire(Tt,{evt:t,target:this,currentTarget:this,pointerId:e}),this._fire("contentMousedown",{evt:t})},Ut.prototype._mouseup=function(t){if(G.UA.ieMobile)return this._touchend(t);this.setPointersPositions(t);var e=A._getFirstPointerId(t),i=this.getIntersection(this.getPointerPosition()),n=this.clickStartShape,r=this.clickEndShape,o=!1;G.inDblClickWindow?(o=!0,clearTimeout(this.dblTimeout)):q.justDragged||(G.inDblClickWindow=!0,clearTimeout(this.dblTimeout)),this.dblTimeout=setTimeout(function(){G.inDblClickWindow=!1},G.dblClickWindow),i&&i.isListening()?((this.clickEndShape=i)._fireAndBubble(At,{evt:t,pointerId:e}),G.listenClickTap&&n&&n._id===i._id&&(i._fireAndBubble("click",{evt:t,pointerId:e}),o&&r&&r===i&&i._fireAndBubble(Ot,{evt:t,pointerId:e}))):(this._fire(At,{evt:t,target:this,currentTarget:this,pointerId:e}),G.listenClickTap&&this._fire("click",{evt:t,target:this,currentTarget:this,pointerId:e}),o&&this._fire(Ot,{evt:t,target:this,currentTarget:this,pointerId:e})),this._fire("contentMouseup",{evt:t}),G.listenClickTap&&(this._fire("contentClick",{evt:t}),o&&this._fire("contentDblclick",{evt:t})),G.listenClickTap=!1,t.cancelable&&t.preventDefault()},Ut.prototype._contextmenu=function(t){this.setPointersPositions(t);var e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(It,{evt:t}):this._fire(It,{evt:t,target:this,currentTarget:this}),this._fire("contentContextmenu",{evt:t})},Ut.prototype._touchstart=function(i){var n=this;this.setPointersPositions(i);var r=!1;this._changedPointerPositions.forEach(function(t){var e=n.getIntersection(t);G.listenClickTap=!0,q.justDragged=!1,e&&e.isListening()&&(G.captureTouchEventsEnabled&&e.setPointerCapture(t.id),(n.tapStartShape=e)._fireAndBubble(Lt,{evt:i,pointerId:t.id},n),r=!0,e.isListening()&&e.preventDefault()&&i.cancelable&&i.preventDefault())}),r||this._fire(Lt,{evt:i,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),this._fire("contentTouchstart",{evt:i})},Ut.prototype._touchmove=function(i){var n,r,o=this;this.setPointersPositions(i),q.isDragging&&!G.hitOnDragEnabled||(n=!1,r={},this._changedPointerPositions.forEach(function(t){var e=vt(t.id)||o.getIntersection(t);e&&e.isListening()&&(r[e._id]||(r[e._id]=!0,e._fireAndBubble(Dt,{evt:i,pointerId:t.id}),n=!0,e.isListening()&&e.preventDefault()&&i.cancelable&&i.preventDefault()))}),n||this._fire(Dt,{evt:i,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),this._fire("contentTouchmove",{evt:i})),q.isDragging&&q.node.preventDefault()&&i.cancelable&&i.preventDefault()},Ut.prototype._touchend=function(i){var n=this;this.setPointersPositions(i);var r=this.clickEndShape,o=!1;G.inDblClickWindow?(o=!0,clearTimeout(this.dblTimeout)):q.justDragged||(G.inDblClickWindow=!0,clearTimeout(this.dblTimeout)),this.dblTimeout=setTimeout(function(){G.inDblClickWindow=!1},G.dblClickWindow);var a=!1,s={},h=!1,d=!1;this._changedPointerPositions.forEach(function(t){var e=vt(t.id)||n.getIntersection(t);e&&e.releaseCapture(t.id),e&&e.isListening()&&(s[e._id]||(s[e._id]=!0,(n.clickEndShape=e)._fireAndBubble(Et,{evt:i,pointerId:t.id}),a=!0,G.listenClickTap&&e===n.tapStartShape&&(h=!0,e._fireAndBubble("tap",{evt:i,pointerId:t.id}),o&&r&&r===e&&(d=!0,e._fireAndBubble("dbltap",{evt:i,pointerId:t.id}))),e.isListening()&&e.preventDefault()&&i.cancelable&&i.preventDefault()))}),a||this._fire(Et,{evt:i,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),G.listenClickTap&&!h&&this._fire("tap",{evt:i,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),o&&!d&&this._fire("dbltap",{evt:i,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),this._fire("contentTouchend",{evt:i}),G.listenClickTap&&(this._fire("contentTap",{evt:i}),o&&this._fire("contentDbltap",{evt:i})),G.listenClickTap=!1},Ut.prototype._wheel=function(t){this.setPointersPositions(t);var e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(Ft,{evt:t}):this._fire(Ft,{evt:t,target:this,currentTarget:this}),this._fire("contentWheel",{evt:t})},Ut.prototype._pointerdown=function(t){var e;G._pointerEventsEnabled&&(this.setPointersPositions(t),(e=vt(t.pointerId)||this.getIntersection(this.getPointerPosition()))&&e._fireAndBubble(Gt,mt(t)))},Ut.prototype._pointermove=function(t){var e;G._pointerEventsEnabled&&(this.setPointersPositions(t),(e=vt(t.pointerId)||this.getIntersection(this.getPointerPosition()))&&e._fireAndBubble(Mt,mt(t)))},Ut.prototype._pointerup=function(t){var e;G._pointerEventsEnabled&&(this.setPointersPositions(t),(e=vt(t.pointerId)||this.getIntersection(this.getPointerPosition()))&&e._fireAndBubble(Rt,mt(t)),xt(t.pointerId))},Ut.prototype._pointercancel=function(t){var e;G._pointerEventsEnabled&&(this.setPointersPositions(t),(e=vt(t.pointerId)||this.getIntersection(this.getPointerPosition()))&&e._fireAndBubble(Rt,mt(t)),xt(t.pointerId))},Ut.prototype._lostpointercapture=function(t){xt(t.pointerId)},Ut.prototype.setPointersPositions=function(t){var e=this,i=this._getContentPosition(),n=null,r=null;void 0!==(t=t||window.event).touches?(this._pointerPositions=[],this._changedPointerPositions=[],o.prototype.each.call(t.touches,function(t){e._pointerPositions.push({id:t.identifier,x:(t.clientX-i.left)/i.scaleX,y:(t.clientY-i.top)/i.scaleY})}),o.prototype.each.call(t.changedTouches||t.touches,function(t){e._changedPointerPositions.push({id:t.identifier,x:(t.clientX-i.left)/i.scaleX,y:(t.clientY-i.top)/i.scaleY})})):(n=(t.clientX-i.left)/i.scaleX,r=(t.clientY-i.top)/i.scaleY,this.pointerPos={x:n,y:r},this._pointerPositions=[{x:n,y:r,id:A._getFirstPointerId(t)}],this._changedPointerPositions=[{x:n,y:r,id:A._getFirstPointerId(t)}])},Ut.prototype._setPointerPosition=function(t){A.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(t)},Ut.prototype._getContentPosition=function(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};var t=this.content.getBoundingClientRect();return{top:t.top,left:t.left,scaleX:t.width/this.content.clientWidth||1,scaleY:t.height/this.content.clientHeight||1}},Ut.prototype._buildDOM=function(){if(this.bufferCanvas=new H({width:this.width(),height:this.height()}),this.bufferHitCanvas=new j({pixelRatio:1,width:this.width(),height:this.height()}),G.isBrowser){var t=this.container();if(!t)throw"Stage has no container. A container is required.";t.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),t.appendChild(this.content),this._resizeDOM()}},Ut.prototype.cache=function(){return A.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this},Ut.prototype.clearCache=function(){return this},Ut.prototype.batchDraw=function(){return this.children.each(function(t){t.batchDraw()}),this},Ut);function Ut(t){var e=Xt.call(this,Yt(t))||this;return e._pointerPositions=[],e._changedPointerPositions=[],e._buildDOM(),e._bindContentEvents(),Ht.push(e),e.on("widthChange.konva heightChange.konva",e._resizeDOM),e.on("visibleChange.konva",e._checkVisibility),e.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",function(){Yt(e.attrs)}),e._checkVisibility(),e}jt.prototype.nodeType="Stage",i(jt),w.addGetterSetter(jt,"container");var qt,Vt=(P(Kt,qt=ut),Kt.prototype.createPNGStream=function(){return this.canvas._canvas.createPNGStream()},Kt.prototype.getCanvas=function(){return this.canvas},Kt.prototype.getHitCanvas=function(){return this.hitCanvas},Kt.prototype.getContext=function(){return this.getCanvas().getContext()},Kt.prototype.clear=function(t){return this.getContext().clear(t),this},Kt.prototype.setZIndex=function(t){qt.prototype.setZIndex.call(this,t);var e=this.getStage();return e&&(e.content.removeChild(this.getCanvas()._canvas),t<e.children.length-1?e.content.insertBefore(this.getCanvas()._canvas,e.children[t+1].getCanvas()._canvas):e.content.appendChild(this.getCanvas()._canvas)),this},Kt.prototype.moveToTop=function(){lt.prototype.moveToTop.call(this);var t=this.getStage();return t&&(t.content.removeChild(this.getCanvas()._canvas),t.content.appendChild(this.getCanvas()._canvas)),!0},Kt.prototype.moveUp=function(){if(!lt.prototype.moveUp.call(this))return!1;var t=this.getStage();return!!t&&(t.content.removeChild(this.getCanvas()._canvas),this.index<t.children.length-1?t.content.insertBefore(this.getCanvas()._canvas,t.children[this.index+1].getCanvas()._canvas):t.content.appendChild(this.getCanvas()._canvas),!0)},Kt.prototype.moveDown=function(){if(lt.prototype.moveDown.call(this)){var t,e=this.getStage();return e&&(t=e.children,e.content.removeChild(this.getCanvas()._canvas),e.content.insertBefore(this.getCanvas()._canvas,t[this.index+1].getCanvas()._canvas)),!0}return!1},Kt.prototype.moveToBottom=function(){if(lt.prototype.moveToBottom.call(this)){var t,e=this.getStage();return e&&(t=e.children,e.content.removeChild(this.getCanvas()._canvas),e.content.insertBefore(this.getCanvas()._canvas,t[1].getCanvas()._canvas)),!0}return!1},Kt.prototype.getLayer=function(){return this},Kt.prototype.hitGraphEnabled=function(){return!0},Kt.prototype.remove=function(){var t=this.getCanvas()._canvas;return lt.prototype.remove.call(this),t&&t.parentNode&&A._isInDocument(t)&&t.parentNode.removeChild(t),this},Kt.prototype.getStage=function(){return this.parent},Kt.prototype.setSize=function(t){var e=t.width,i=t.height;return this.canvas.setSize(e,i),this._setSmoothEnabled(),this},Kt.prototype._toKonvaCanvas=function(t){return(t=t||{}).width=t.width||this.getWidth(),t.height=t.height||this.getHeight(),t.x=void 0!==t.x?t.x:this.x(),t.y=void 0!==t.y?t.y:this.y(),lt.prototype._toKonvaCanvas.call(this,t)},Kt.prototype._checkVisibility=function(){var t=this.visible();this.canvas._canvas.style.display=t?"block":"none"},Kt.prototype._setSmoothEnabled=function(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()},Kt.prototype.getWidth=function(){if(this.parent)return this.parent.width()},Kt.prototype.setWidth=function(){A.warn('Can not change width of layer. Use "stage.width(value)" function instead.')},Kt.prototype.getHeight=function(){if(this.parent)return this.parent.height()},Kt.prototype.setHeight=function(){A.warn('Can not change height of layer. Use "stage.height(value)" function instead.')},Kt.prototype.getIntersection=function(t,e){return null},Kt.prototype.batchDraw=function(){var t=this;return this._waitingForDraw||(this._waitingForDraw=!0,A.requestAnimFrame(function(){t.draw(),t._waitingForDraw=!1})),this},Kt.prototype._applyTransform=function(t,e,i){var n=t.getAbsoluteTransform(i).getMatrix();e.transform(n[0],n[1],n[2],n[3],n[4],n[5])},Kt);function Kt(t){var e=qt.call(this,t)||this;return e.canvas=new H,e._waitingForDraw=!1,e.on("visibleChange",e._checkVisibility),e._checkVisibility(),e.on("imageSmoothingEnabledChange",e._setSmoothEnabled),e._setSmoothEnabled(),e}Vt.prototype.nodeType="BaseLayer",w.addGetterSetter(Vt,"imageSmoothingEnabled",!0),w.addGetterSetter(Vt,"clearBeforeDraw",!0),o.mapMethods(Vt);var Qt,Jt="hasShadow",Zt="shadowRGBA",$t="patternImage",te="linearGradient",ee="radialGradient";function ie(){return Qt||(Qt=A.createCanvasElement().getContext("2d"))}var ne={};function re(){this._clearCache(Jt)}function oe(){this._clearCache(Zt)}function ae(){this._clearCache($t)}function se(){this._clearCache(te)}function he(){this._clearCache(ee)}var de,le=(P(ce,de=lt),ce.prototype.getContext=function(){return this.getLayer().getContext()},ce.prototype.getCanvas=function(){return this.getLayer().getCanvas()},ce.prototype.getSceneFunc=function(){return this.attrs.sceneFunc||this._sceneFunc},ce.prototype.getHitFunc=function(){return this.attrs.hitFunc||this._hitFunc},ce.prototype.hasShadow=function(){return this._getCache(Jt,this._hasShadow)},ce.prototype._hasShadow=function(){return this.shadowEnabled()&&0!==this.shadowOpacity()&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())},ce.prototype._getFillPattern=function(){return this._getCache($t,this.__getFillPattern)},ce.prototype.__getFillPattern=function(){if(this.fillPatternImage())return ie().createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat")},ce.prototype._getLinearGradient=function(){return this._getCache(te,this.__getLinearGradient)},ce.prototype.__getLinearGradient=function(){var t=this.fillLinearGradientColorStops();if(t){for(var e=ie(),i=this.fillLinearGradientStartPoint(),n=this.fillLinearGradientEndPoint(),r=e.createLinearGradient(i.x,i.y,n.x,n.y),o=0;o<t.length;o+=2)r.addColorStop(t[o],t[o+1]);return r}},ce.prototype._getRadialGradient=function(){return this._getCache(ee,this.__getRadialGradient)},ce.prototype.__getRadialGradient=function(){var t=this.fillRadialGradientColorStops();if(t){for(var e=ie(),i=this.fillRadialGradientStartPoint(),n=this.fillRadialGradientEndPoint(),r=e.createRadialGradient(i.x,i.y,this.fillRadialGradientStartRadius(),n.x,n.y,this.fillRadialGradientEndRadius()),o=0;o<t.length;o+=2)r.addColorStop(t[o],t[o+1]);return r}},ce.prototype.getShadowRGBA=function(){return this._getCache(Zt,this._getShadowRGBA)},ce.prototype._getShadowRGBA=function(){if(this.hasShadow()){var t=A.colorToRGBA(this.shadowColor());return"rgba("+t.r+","+t.g+","+t.b+","+t.a*(this.shadowOpacity()||1)+")"}},ce.prototype.hasFill=function(){return this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops())},ce.prototype.hasStroke=function(){return this.strokeEnabled()&&this.strokeWidth()&&!(!this.stroke()&&!this.strokeLinearGradientColorStops())},ce.prototype.hasHitStroke=function(){var t=this.hitStrokeWidth();return"auto"===t?this.hasStroke():this.strokeEnabled()&&!!t},ce.prototype.intersects=function(t){var e=this.getStage().bufferHitCanvas;return e.getContext().clear(),this.drawHit(e),0<e.context.getImageData(Math.round(t.x),Math.round(t.y),1,1).data[3]},ce.prototype.destroy=function(){return lt.prototype.destroy.call(this),delete ne[this.colorKey],delete this.colorKey,this},ce.prototype._useBufferCanvas=function(t){return!(t&&!this.hasShadow()||!this.perfectDrawEnabled()||1===this.getAbsoluteOpacity()||!this.hasFill()||!this.hasStroke()||!this.getStage())},ce.prototype.setStrokeHitEnabled=function(t){A.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),t?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)},ce.prototype.getStrokeHitEnabled=function(){return 0!==this.hitStrokeWidth()},ce.prototype.getSelfRect=function(){var t=this.size();return{x:this._centroid?-t.width/2:0,y:this._centroid?-t.height/2:0,width:t.width,height:t.height}},ce.prototype.getClientRect=function(t){var e=(t=t||{}).skipTransform,i=t.relativeTo,n=this.getSelfRect(),r=!t.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,o=n.width+r,a=n.height+r,s=!t.skipShadow&&this.hasShadow(),h=s?this.shadowOffsetX():0,d=s?this.shadowOffsetY():0,l=o+Math.abs(h),c=a+Math.abs(d),p=s&&this.shadowBlur()||0,u=l+2*p,f=c+2*p,g=0;Math.round(r/2)!==r/2&&(g=1);var y={width:u+g,height:f+g,x:-Math.round(r/2+p)+Math.min(h,0)+n.x,y:-Math.round(r/2+p)+Math.min(d,0)+n.y};return e?y:this._transformedRect(y,i)},ce.prototype.drawScene=function(t,e,i,n){var r,o,a,s,h,d=this.getLayer(),l=t||d.getCanvas(),c=l.getContext(),p=this._getCanvasCache(),u=this.sceneFunc(),f=this.hasShadow(),g=this.hasStroke();return(this.isVisible()||i)&&(p?(c.save(),d._applyTransform(this,c,e),this._drawCachedSceneCanvas(c),c.restore()):u&&(c.save(),this._useBufferCanvas(i)&&!n?((a=(o=this.getStage().bufferCanvas).getContext()).clear(),a.save(),a._applyLineJoin(this),i||(d?d._applyTransform(this,a,e):(r=this.getAbsoluteTransform(e).getMatrix(),c.transform(r[0],r[1],r[2],r[3],r[4],r[5]))),u.call(this,a,this),a.restore(),s=o.pixelRatio,f&&!l.hitCanvas?(c.save(),c._applyShadow(this),c._applyOpacity(this),c._applyGlobalCompositeOperation(this),c.drawImage(o._canvas,0,0,o.width/s,o.height/s),c.restore()):(c._applyOpacity(this),c._applyGlobalCompositeOperation(this),c.drawImage(o._canvas,0,0,o.width/s,o.height/s))):(c._applyLineJoin(this),i||(d?d._applyTransform(this,c,e):(h=this.getAbsoluteTransform(e).getMatrix(),c.transform(h[0],h[1],h[2],h[3],h[4],h[5]))),f&&g&&!l.hitCanvas?(c.save(),i||(c._applyOpacity(this),c._applyGlobalCompositeOperation(this)),c._applyShadow(this),u.call(this,c,this),c.restore(),this.hasFill()&&this.shadowForStrokeEnabled()&&u.call(this,c,this)):f&&!l.hitCanvas?(c.save(),i||(c._applyOpacity(this),c._applyGlobalCompositeOperation(this)),c._applyShadow(this),u.call(this,c,this),c.restore()):(i||(c._applyOpacity(this),c._applyGlobalCompositeOperation(this)),u.call(this,c,this))),c.restore())),this},ce.prototype.drawHit=function(t,e,i){var n,r=this.getLayer(),o=t||r.hitCanvas,a=o&&o.getContext(),s=this.hitFunc()||this.sceneFunc(),h=this._getCanvasCache(),d=h&&h.hit;return this.colorKey||(console.log(this),A.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. See the shape in logs above. If you want to reuse shape you should call remove() instead of destroy()")),(this.shouldDrawHit()||i)&&(d?(a.save(),r._applyTransform(this,a,e),this._drawCachedHitCanvas(a),a.restore()):s&&(a.save(),a._applyLineJoin(this),i||(r?r._applyTransform(this,a,e):(n=this.getAbsoluteTransform(e).getMatrix(),a.transform(n[0],n[1],n[2],n[3],n[4],n[5]))),s.call(this,a,this),a.restore())),this},ce.prototype.drawHitFromCache=function(t){void 0===t&&(t=0);var e,i,n,r,o,a=this._getCanvasCache(),s=this._getCachedSceneCanvas(),h=a.hit,d=h.getContext(),l=h.getWidth(),c=h.getHeight();d.clear(),d.drawImage(s._canvas,0,0,l,c);try{for(n=(i=(e=d.getImageData(0,0,l,c)).data).length,r=A._hexToRgb(this.colorKey),o=0;o<n;o+=4)t<i[o+3]?(i[o]=r.r,i[o+1]=r.g,i[o+2]=r.b,i[o+3]=255):i[o+3]=0;d.putImageData(e,0,0)}catch(t){A.error("Unable to draw hit graph from cached scene canvas. "+t.message)}return this},ce.prototype.hasPointerCapture=function(t){return _t(t,this)},ce.prototype.setPointerCapture=function(t){bt(t,this)},ce.prototype.releaseCapture=function(t){xt(t)},ce);function ce(t){for(var e,i=de.call(this,t)||this;!(e=A.getRandomColor())||e in ne;);return i.colorKey=e,(ne[e]=i).on("shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",re),i.on("shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",oe),i.on("fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva",ae),i.on("fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",se),i.on("fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",he),i}le.prototype._fillFunc=function(t){t.fill()},le.prototype._strokeFunc=function(t){t.stroke()},le.prototype._fillFuncHit=function(t){t.fill()},le.prototype._strokeFuncHit=function(t){t.stroke()},le.prototype._centroid=!1,le.prototype.nodeType="Shape",i(le),w.addGetterSetter(le,"stroke",void 0,_()),w.addGetterSetter(le,"strokeWidth",2,v()),w.addGetterSetter(le,"hitStrokeWidth","auto",m()),w.addGetterSetter(le,"strokeHitEnabled",!0,b()),w.addGetterSetter(le,"perfectDrawEnabled",!0,b()),w.addGetterSetter(le,"shadowForStrokeEnabled",!0,b()),w.addGetterSetter(le,"lineJoin"),w.addGetterSetter(le,"lineCap"),w.addGetterSetter(le,"sceneFunc"),w.addGetterSetter(le,"hitFunc"),w.addGetterSetter(le,"dash"),w.addGetterSetter(le,"dashOffset",0,v()),w.addGetterSetter(le,"shadowColor",void 0,_()),w.addGetterSetter(le,"shadowBlur",0,v()),w.addGetterSetter(le,"shadowOpacity",1,v()),w.addComponentsGetterSetter(le,"shadowOffset",["x","y"]),w.addGetterSetter(le,"shadowOffsetX",0,v()),w.addGetterSetter(le,"shadowOffsetY",0,v()),w.addGetterSetter(le,"fillPatternImage"),w.addGetterSetter(le,"fill",void 0,_()),w.addGetterSetter(le,"fillPatternX",0,v()),w.addGetterSetter(le,"fillPatternY",0,v()),w.addGetterSetter(le,"fillLinearGradientColorStops"),w.addGetterSetter(le,"strokeLinearGradientColorStops"),w.addGetterSetter(le,"fillRadialGradientStartRadius",0),w.addGetterSetter(le,"fillRadialGradientEndRadius",0),w.addGetterSetter(le,"fillRadialGradientColorStops"),w.addGetterSetter(le,"fillPatternRepeat","repeat"),w.addGetterSetter(le,"fillEnabled",!0),w.addGetterSetter(le,"strokeEnabled",!0),w.addGetterSetter(le,"shadowEnabled",!0),w.addGetterSetter(le,"dashEnabled",!0),w.addGetterSetter(le,"strokeScaleEnabled",!0),w.addGetterSetter(le,"fillPriority","color"),w.addComponentsGetterSetter(le,"fillPatternOffset",["x","y"]),w.addGetterSetter(le,"fillPatternOffsetX",0,v()),w.addGetterSetter(le,"fillPatternOffsetY",0,v()),w.addComponentsGetterSetter(le,"fillPatternScale",["x","y"]),w.addGetterSetter(le,"fillPatternScaleX",1,v()),w.addGetterSetter(le,"fillPatternScaleY",1,v()),w.addComponentsGetterSetter(le,"fillLinearGradientStartPoint",["x","y"]),w.addComponentsGetterSetter(le,"strokeLinearGradientStartPoint",["x","y"]),w.addGetterSetter(le,"fillLinearGradientStartPointX",0),w.addGetterSetter(le,"strokeLinearGradientStartPointX",0),w.addGetterSetter(le,"fillLinearGradientStartPointY",0),w.addGetterSetter(le,"strokeLinearGradientStartPointY",0),w.addComponentsGetterSetter(le,"fillLinearGradientEndPoint",["x","y"]),w.addComponentsGetterSetter(le,"strokeLinearGradientEndPoint",["x","y"]),w.addGetterSetter(le,"fillLinearGradientEndPointX",0),w.addGetterSetter(le,"strokeLinearGradientEndPointX",0),w.addGetterSetter(le,"fillLinearGradientEndPointY",0),w.addGetterSetter(le,"strokeLinearGradientEndPointY",0),w.addComponentsGetterSetter(le,"fillRadialGradientStartPoint",["x","y"]),w.addGetterSetter(le,"fillRadialGradientStartPointX",0),w.addGetterSetter(le,"fillRadialGradientStartPointY",0),w.addComponentsGetterSetter(le,"fillRadialGradientEndPoint",["x","y"]),w.addGetterSetter(le,"fillRadialGradientEndPointX",0),w.addGetterSetter(le,"fillRadialGradientEndPointY",0),w.addGetterSetter(le,"fillPatternRotation",0),w.backCompat(le,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"}),o.mapMethods(le);var pe,ue=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],fe=ue.length,ge=(P(ye,pe=Vt),ye.prototype.setSize=function(t){var e=t.width,i=t.height;return pe.prototype.setSize.call(this,{width:e,height:i}),this.hitCanvas.setSize(e,i),this},ye.prototype._validateAdd=function(t){var e=t.getType();"Group"!==e&&"Shape"!==e&&A.throw("You may only add groups and shapes to a layer.")},ye.prototype.getIntersection=function(t,e){var i,n,r,o;if(!this.hitGraphEnabled()||!this.isVisible())return null;for(var a=1,s=!1;;){for(n=0;n<fe;n++){if(r=ue[n],(o=(i=this._getIntersection({x:t.x+r.x*a,y:t.y+r.y*a})).shape)&&e)return o.findAncestor(e,!0);if(o)return o;if(s=!!i.antialiased,!i.antialiased)break}if(!s)return null;a+=1}},ye.prototype._getIntersection=function(t){var e,i,n=this.hitCanvas.pixelRatio,r=this.hitCanvas.context.getImageData(Math.round(t.x*n),Math.round(t.y*n),1,1).data,o=r[3];return 255===o?(e=A._rgbToHex(r[0],r[1],r[2]),(i=ne["#"+e])?{shape:i}:{antialiased:!0}):0<o?{antialiased:!0}:{}},ye.prototype.drawScene=function(t,e){var i=this.getLayer(),n=t||i&&i.getCanvas();return this._fire("beforeDraw",{node:this}),this.clearBeforeDraw()&&n.getContext().clear(),ut.prototype.drawScene.call(this,n,e),this._fire("draw",{node:this}),this},ye.prototype.drawHit=function(t,e){var i=this.getLayer(),n=t||i&&i.hitCanvas;return i&&i.clearBeforeDraw()&&i.getHitCanvas().getContext().clear(),ut.prototype.drawHit.call(this,n,e),this},ye.prototype.clear=function(t){return Vt.prototype.clear.call(this,t),this.getHitCanvas().getContext().clear(t),this},ye.prototype.enableHitGraph=function(){return this.hitGraphEnabled(!0),this},ye.prototype.disableHitGraph=function(){return this.hitGraphEnabled(!1),this},ye.prototype.toggleHitCanvas=function(){var t;this.parent&&(t=this.parent,this.hitCanvas._canvas.parentNode?t.content.removeChild(this.hitCanvas._canvas):t.content.appendChild(this.hitCanvas._canvas))},ye);function ye(){var t=null!==pe&&pe.apply(this,arguments)||this;return t.hitCanvas=new j({pixelRatio:1}),t}ge.prototype.nodeType="Layer",i(ge),w.addGetterSetter(ge,"hitGraphEnabled",!0,b()),o.mapMethods(ge);var ve,me=(P(_e,ve=Vt),_e.prototype._validateAdd=function(t){"Shape"!==t.getType()&&A.throw("You may only add shapes to a fast layer.")},_e.prototype.hitGraphEnabled=function(){return!1},_e.prototype.drawScene=function(t){var e=this.getLayer(),i=t||e&&e.getCanvas();return this.clearBeforeDraw()&&i.getContext().clear(),ut.prototype.drawScene.call(this,i),this},_e.prototype.draw=function(){return this.drawScene(),this},_e);function _e(){return null!==ve&&ve.apply(this,arguments)||this}me.prototype.nodeType="FastLayer",i(me),o.mapMethods(me);var be,xe=(P(Se,be=ut),Se.prototype._validateAdd=function(t){var e=t.getType();"Group"!==e&&"Shape"!==e&&A.throw("You may only add groups and shapes to groups.")},Se);function Se(){return null!==be&&be.apply(this,arguments)||this}xe.prototype.nodeType="Group",i(xe),o.mapMethods(xe);var we=n.performance&&n.performance.now?function(){return n.performance.now()}:function(){return(new Date).getTime()},Ce=(Pe.prototype.setLayers=function(t){var e=[],e=t?0<t.length?t:[t]:[];return this.layers=e,this},Pe.prototype.getLayers=function(){return this.layers},Pe.prototype.addLayer=function(t){for(var e=this.layers,i=e.length,n=0;n<i;n++)if(e[n]._id===t._id)return!1;return this.layers.push(t),!0},Pe.prototype.isRunning=function(){for(var t=Pe.animations,e=t.length,i=0;i<e;i++)if(t[i].id===this.id)return!0;return!1},Pe.prototype.start=function(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=we(),Pe._addAnimation(this),this},Pe.prototype.stop=function(){return Pe._removeAnimation(this),this},Pe.prototype._updateFrameObject=function(t){this.frame.timeDiff=t-this.frame.lastTime,this.frame.lastTime=t,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff},Pe._addAnimation=function(t){this.animations.push(t),this._handleAnimation()},Pe._removeAnimation=function(t){for(var e=t.id,i=this.animations,n=i.length,r=0;r<n;r++)if(i[r].id===e){this.animations.splice(r,1);break}},Pe._runFrames=function(){for(var t,e,i,n,r,o,a,s={},h=this.animations,d=0;d<h.length;d++)if(e=(t=h[d]).layers,i=t.func,t._updateFrameObject(we()),r=e.length,!i||!1!==i.call(t,t.frame))for(n=0;n<r;n++)void 0!==(o=e[n])._id&&(s[o._id]=o);for(a in s)s.hasOwnProperty(a)&&s[a].draw()},Pe._animationLoop=function(){var t=Pe;t.animations.length?(t._runFrames(),requestAnimationFrame(t._animationLoop)):t.animRunning=!1},Pe._handleAnimation=function(){this.animRunning||(this.animRunning=!0,requestAnimationFrame(this._animationLoop))},Pe.animations=[],Pe.animIdCounter=0,Pe.animRunning=!1,Pe);function Pe(t,e){this.id=Pe.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:we(),frameRate:0},this.func=t,this.setLayers(e)}var ke={node:1,duration:1,easing:1,onFinish:1,yoyo:1},Te=0,Ae=["fill","stroke","shadowColor"],Me=(Ge.prototype.fire=function(t){var e=this[t];e&&e()},Ge.prototype.setTime=function(t){t>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():t<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=t,this.update())},Ge.prototype.getTime=function(){return this._time},Ge.prototype.setPosition=function(t){this.prevPos=this._pos,this.propFunc(t),this._pos=t},Ge.prototype.getPosition=function(t){return void 0===t&&(t=this._time),this.func(t,this.begin,this._change,this.duration)},Ge.prototype.play=function(){this.state=2,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")},Ge.prototype.reverse=function(){this.state=3,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")},Ge.prototype.seek=function(t){this.pause(),this._time=t,this.update(),this.fire("onSeek")},Ge.prototype.reset=function(){this.pause(),this._time=0,this.update(),this.fire("onReset")},Ge.prototype.finish=function(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")},Ge.prototype.update=function(){this.setPosition(this.getPosition(this._time))},Ge.prototype.onEnterFrame=function(){var t=this.getTimer()-this._startTime;2===this.state?this.setTime(t):3===this.state&&this.setTime(this.duration-t)},Ge.prototype.pause=function(){this.state=1,this.fire("onPause")},Ge.prototype.getTimer=function(){return(new Date).getTime()},Ge);function Ge(t,e,i,n,r,o,a){this.prop=t,this.propFunc=e,this.begin=n,this._pos=n,this.duration=o,this._change=0,this.prevPos=0,this.yoyo=a,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=i,this._change=r-this.begin,this.pause()}var Re=(Ie.prototype._addAttr=function(t,e){var i,n,r,o,a,s,h,d,l=this.node,c=l._id,p=Ie.tweens[c][t];if(p&&delete Ie.attrs[c][p][t],i=l.getAttr(t),A._isArray(e))if(n=[],o=Math.max(e.length,i.length),"points"===t&&e.length!==i.length&&(e.length>i.length?(s=i,i=A._prepareArrayForTween(i,e,l.closed())):(a=e,e=A._prepareArrayForTween(e,i,l.closed()))),0===t.indexOf("fill"))for(r=0;r<o;r++)r%2==0?n.push(e[r]-i[r]):(h=A.colorToRGBA(i[r]),d=A.colorToRGBA(e[r]),i[r]=h,n.push({r:d.r-h.r,g:d.g-h.g,b:d.b-h.b,a:d.a-h.a}));else for(r=0;r<o;r++)n.push(e[r]-i[r]);else n=-1!==Ae.indexOf(t)?(i=A.colorToRGBA(i),{r:(d=A.colorToRGBA(e)).r-i.r,g:d.g-i.g,b:d.b-i.b,a:d.a-i.a}):e-i;Ie.attrs[c][this._id][t]={start:i,diff:n,end:e,trueEnd:a,trueStart:s},Ie.tweens[c][t]=this._id},Ie.prototype._tweenFunc=function(t){var e,i,n,r,o,a,s,h,d=this.node,l=Ie.attrs[d._id][this._id];for(e in l){if(n=(i=l[e]).start,r=i.diff,h=i.end,A._isArray(n))if(o=[],s=Math.max(n.length,h.length),0===e.indexOf("fill"))for(a=0;a<s;a++)a%2==0?o.push((n[a]||0)+r[a]*t):o.push("rgba("+Math.round(n[a].r+r[a].r*t)+","+Math.round(n[a].g+r[a].g*t)+","+Math.round(n[a].b+r[a].b*t)+","+(n[a].a+r[a].a*t)+")");else for(a=0;a<s;a++)o.push((n[a]||0)+r[a]*t);else o=-1!==Ae.indexOf(e)?"rgba("+Math.round(n.r+r.r*t)+","+Math.round(n.g+r.g*t)+","+Math.round(n.b+r.b*t)+","+(n.a+r.a*t)+")":n+r*t;d.setAttr(e,o)}},Ie.prototype._addListeners=function(){var i=this;this.tween.onPlay=function(){i.anim.start()},this.tween.onReverse=function(){i.anim.start()},this.tween.onPause=function(){i.anim.stop()},this.tween.onFinish=function(){var t=i.node,e=Ie.attrs[t._id][i._id];e.points&&e.points.trueEnd&&t.setAttr("points",e.points.trueEnd),i.onFinish&&i.onFinish.call(i)},this.tween.onReset=function(){var t=i.node,e=Ie.attrs[t._id][i._id];e.points&&e.points.trueStart&&t.points(e.points.trueStart),i.onReset&&i.onReset()}},Ie.prototype.play=function(){return this.tween.play(),this},Ie.prototype.reverse=function(){return this.tween.reverse(),this},Ie.prototype.reset=function(){return this.tween.reset(),this},Ie.prototype.seek=function(t){return this.tween.seek(1e3*t),this},Ie.prototype.pause=function(){return this.tween.pause(),this},Ie.prototype.finish=function(){return this.tween.finish(),this},Ie.prototype.destroy=function(){var t,e=this.node._id,i=this._id,n=Ie.tweens[e];for(t in this.pause(),n)delete Ie.tweens[e][t];delete Ie.attrs[e][i]},Ie.attrs={},Ie.tweens={},Ie);function Ie(t){var e,i=this,n=t.node,r=n._id,o=t.easing||Le.Linear,a=!!t.yoyo,s=void 0===t.duration?.3:0===t.duration?.001:t.duration;this.node=n,this._id=Te++;var h=n.getLayer()||(n instanceof G.Stage?n.getLayers():null);for(e in h||A.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new Ce(function(){i.tween.onEnterFrame()},h),this.tween=new Me(e,function(t){i._tweenFunc(t)},o,0,1,1e3*s,a),this._addListeners(),Ie.attrs[r]||(Ie.attrs[r]={}),Ie.attrs[r][this._id]||(Ie.attrs[r][this._id]={}),Ie.tweens[r]||(Ie.tweens[r]={}),t)void 0===ke[e]&&this._addAttr(e,t[e]);this.reset(),this.onFinish=t.onFinish,this.onReset=t.onReset}lt.prototype.to=function(t){var e=t.onFinish;t.node=this,t.onFinish=function(){this.destroy(),e&&e()},new Re(t).play()};var Oe,Le={BackEaseIn:function(t,e,i,n){return i*(t/=n)*t*(2.70158*t-1.70158)+e},BackEaseOut:function(t,e,i,n){return i*((t=t/n-1)*t*(2.70158*t+1.70158)+1)+e},BackEaseInOut:function(t,e,i,n){var r=1.70158;return(t/=n/2)<1?i/2*(t*t*((1+(r*=1.525))*t-r))+e:i/2*((t-=2)*t*((1+(r*=1.525))*t+r)+2)+e},ElasticEaseIn:function(t,e,i,n,r,o){var a=0;return 0===t?e:1==(t/=n)?e+i:(o=o||.3*n,a=!r||r<Math.abs(i)?(r=i,o/4):o/(2*Math.PI)*Math.asin(i/r),-(r*Math.pow(2,10*--t)*Math.sin((t*n-a)*(2*Math.PI)/o))+e)},ElasticEaseOut:function(t,e,i,n,r,o){var a=0;return 0===t?e:1==(t/=n)?e+i:(o=o||.3*n,a=!r||r<Math.abs(i)?(r=i,o/4):o/(2*Math.PI)*Math.asin(i/r),r*Math.pow(2,-10*t)*Math.sin((t*n-a)*(2*Math.PI)/o)+i+e)},ElasticEaseInOut:function(t,e,i,n,r,o){var a=0;return 0===t?e:2==(t/=n/2)?e+i:(o=o||n*(.3*1.5),a=!r||r<Math.abs(i)?(r=i,o/4):o/(2*Math.PI)*Math.asin(i/r),t<1?r*Math.pow(2,10*--t)*Math.sin((t*n-a)*(2*Math.PI)/o)*-.5+e:r*Math.pow(2,-10*--t)*Math.sin((t*n-a)*(2*Math.PI)/o)*.5+i+e)},BounceEaseOut:function(t,e,i,n){return(t/=n)<1/2.75?i*(7.5625*t*t)+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e},BounceEaseIn:function(t,e,i,n){return i-Le.BounceEaseOut(n-t,0,i,n)+e},BounceEaseInOut:function(t,e,i,n){return t<n/2?.5*Le.BounceEaseIn(2*t,0,i,n)+e:.5*Le.BounceEaseOut(2*t-n,0,i,n)+.5*i+e},EaseIn:function(t,e,i,n){return i*(t/=n)*t+e},EaseOut:function(t,e,i,n){return-i*(t/=n)*(t-2)+e},EaseInOut:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t+e:-i/2*(--t*(t-2)-1)+e},StrongEaseIn:function(t,e,i,n){return i*(t/=n)*t*t*t*t+e},StrongEaseOut:function(t,e,i,n){return i*((t=t/n-1)*t*t*t*t+1)+e},StrongEaseInOut:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t*t*t+e:i/2*((t-=2)*t*t*t*t+2)+e},Linear:function(t,e,i,n){return i*t/n+e}},Ee=A._assign(G,{Collection:o,Util:A,Transform:l,Node:lt,ids:Q,names:J,Container:ut,Stage:jt,stages:Ht,Layer:ge,FastLayer:me,Group:xe,DD:q,Shape:le,shapes:ne,Animation:Ce,Tween:Re,Easings:Le,Context:M,Canvas:N}),De=(P(Fe,Oe=le),Fe.prototype._sceneFunc=function(t){var e=G.getAngle(this.angle()),i=this.clockwise();t.beginPath(),t.arc(0,0,this.outerRadius(),0,e,i),t.arc(0,0,this.innerRadius(),e,0,!i),t.closePath(),t.fillStrokeShape(this)},Fe.prototype.getWidth=function(){return 2*this.outerRadius()},Fe.prototype.getHeight=function(){return 2*this.outerRadius()},Fe.prototype.setWidth=function(t){this.outerRadius(t/2)},Fe.prototype.setHeight=function(t){this.outerRadius(t/2)},Fe);function Fe(){return null!==Oe&&Oe.apply(this,arguments)||this}De.prototype._centroid=!0,De.prototype.className="Arc",De.prototype._attrsAffectingSize=["innerRadius","outerRadius"],i(De),w.addGetterSetter(De,"innerRadius",0,v()),w.addGetterSetter(De,"outerRadius",0,v()),w.addGetterSetter(De,"angle",0,v()),w.addGetterSetter(De,"clockwise",!1,b()),o.mapMethods(De);var Be,Ne=(P(ze,Be=le),ze.prototype._sceneFunc=function(t){var e,i,n,r=this.points(),o=r.length,a=this.tension(),s=this.closed(),h=this.bezier();if(o){if(t.beginPath(),t.moveTo(r[0],r[1]),0!==a&&4<o){for(i=(e=this.getTensionPoints()).length,n=s?0:4,s||t.quadraticCurveTo(e[0],e[1],e[2],e[3]);n<i-2;)t.bezierCurveTo(e[n++],e[n++],e[n++],e[n++],e[n++],e[n++]);s||t.quadraticCurveTo(e[i-2],e[i-1],r[o-2],r[o-1])}else if(h)for(n=2;n<o;)t.bezierCurveTo(r[n++],r[n++],r[n++],r[n++],r[n++],r[n++]);else for(n=2;n<o;n+=2)t.lineTo(r[n],r[n+1]);s?(t.closePath(),t.fillStrokeShape(this)):t.strokeShape(this)}},ze.prototype.getTensionPoints=function(){return this._getCache("tensionPoints",this._getTensionPoints)},ze.prototype._getTensionPoints=function(){return this.closed()?this._getTensionPointsClosed():A._expandPoints(this.points(),this.tension())},ze.prototype._getTensionPointsClosed=function(){var t=this.points(),e=t.length,i=this.tension(),n=A._getControlPoints(t[e-2],t[e-1],t[0],t[1],t[2],t[3],i),r=A._getControlPoints(t[e-4],t[e-3],t[e-2],t[e-1],t[0],t[1],i),o=A._expandPoints(t,i);return[n[2],n[3]].concat(o).concat([r[0],r[1],t[e-2],t[e-1],r[2],r[3],n[0],n[1],t[0],t[1]])},ze.prototype.getWidth=function(){return this.getSelfRect().width},ze.prototype.getHeight=function(){return this.getSelfRect().height},ze.prototype.getSelfRect=function(){var t=this.points();if(t.length<4)return{x:t[0]||0,y:t[1]||0,width:0,height:0};for(var e,i,n=(t=0!==this.tension()?function(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;for(var n=Array(t),r=0,e=0;e<i;e++)for(var o=arguments[e],a=0,s=o.length;a<s;a++,r++)n[r]=o[a];return n}([t[0],t[1]],this._getTensionPoints(),[t[t.length-2],t[t.length-1]]):this.points())[0],r=t[0],o=t[1],a=t[1],s=0;s<t.length/2;s++)e=t[2*s],i=t[2*s+1],n=Math.min(n,e),r=Math.max(r,e),o=Math.min(o,i),a=Math.max(a,i);return{x:n,y:o,width:r-n,height:a-o}},ze);function ze(t){var e=Be.call(this,t)||this;return e.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",function(){this._clearCache("tensionPoints")}),e}Ne.prototype.className="Line",Ne.prototype._attrsAffectingSize=["points","bezier","tension"],i(Ne),w.addGetterSetter(Ne,"closed",!1),w.addGetterSetter(Ne,"bezier",!1),w.addGetterSetter(Ne,"tension",0,v()),w.addGetterSetter(Ne,"points",[],function(){if(G.isUnminified)return function(t,e){return A._isArray(t)?t.forEach(function(t){A._isNumber(t)||A.warn('"'+e+'" attribute has non numeric element '+t+". Make sure that all elements are numbers.")}):A.warn(g(t)+' is a not valid value for "'+e+'" attribute. The value should be a array of numbers.'),t}}()),o.mapMethods(Ne);var We,He=(P(Ye,We=Ne),Ye.prototype._sceneFunc=function(t){We.prototype._sceneFunc.call(this,t);var e=2*Math.PI,i=this.points(),n=i,r=0!==this.tension()&&4<i.length;r&&(n=this.getTensionPoints());var o,a=i.length,s=r?(o=i[a-2]-(n[n.length-2]+n[n.length-4])/2,i[a-1]-(n[n.length-1]+n[n.length-3])/2):(o=i[a-2]-i[a-4],i[a-1]-i[a-3]),h=(Math.atan2(s,o)+e)%e,d=this.pointerLength(),l=this.pointerWidth();t.save(),t.beginPath(),t.translate(i[a-2],i[a-1]),t.rotate(h),t.moveTo(0,0),t.lineTo(-d,l/2),t.lineTo(-d,-l/2),t.closePath(),t.restore(),this.pointerAtBeginning()&&(t.save(),t.translate(i[0],i[1]),s=r?(o=(n[0]+n[2])/2-i[0],(n[1]+n[3])/2-i[1]):(o=i[2]-i[0],i[3]-i[1]),t.rotate((Math.atan2(-s,-o)+e)%e),t.moveTo(0,0),t.lineTo(-d,l/2),t.lineTo(-d,-l/2),t.closePath(),t.restore());var c=this.dashEnabled();c&&(this.attrs.dashEnabled=!1,t.setLineDash([])),t.fillStrokeShape(this),c&&(this.attrs.dashEnabled=!0)},Ye.prototype.getSelfRect=function(){var t=We.prototype.getSelfRect.call(this),e=this.pointerWidth()/2;return{x:t.x-e,y:t.y-e,width:t.width+2*e,height:t.height+2*e}},Ye);function Ye(){return null!==We&&We.apply(this,arguments)||this}He.prototype.className="Arrow",i(He),w.addGetterSetter(He,"pointerLength",10,v()),w.addGetterSetter(He,"pointerWidth",10,v()),w.addGetterSetter(He,"pointerAtBeginning",!1),o.mapMethods(He);var Xe,je=(P(Ue,Xe=le),Ue.prototype._sceneFunc=function(t){t.beginPath(),t.arc(0,0,this.radius(),0,2*Math.PI,!1),t.closePath(),t.fillStrokeShape(this)},Ue.prototype.getWidth=function(){return 2*this.radius()},Ue.prototype.getHeight=function(){return 2*this.radius()},Ue.prototype.setWidth=function(t){this.radius()!==t/2&&this.radius(t/2)},Ue.prototype.setHeight=function(t){this.radius()!==t/2&&this.radius(t/2)},Ue);function Ue(){return null!==Xe&&Xe.apply(this,arguments)||this}je.prototype._centroid=!0,je.prototype.className="Circle",je.prototype._attrsAffectingSize=["radius"],i(je),w.addGetterSetter(je,"radius",0,v()),o.mapMethods(je);var qe,Ve=(P(Ke,qe=le),Ke.prototype._sceneFunc=function(t){var e=this.radiusX(),i=this.radiusY();t.beginPath(),t.save(),e!==i&&t.scale(1,i/e),t.arc(0,0,e,0,2*Math.PI,!1),t.restore(),t.closePath(),t.fillStrokeShape(this)},Ke.prototype.getWidth=function(){return 2*this.radiusX()},Ke.prototype.getHeight=function(){return 2*this.radiusY()},Ke.prototype.setWidth=function(t){this.radiusX(t/2)},Ke.prototype.setHeight=function(t){this.radiusY(t/2)},Ke);function Ke(){return null!==qe&&qe.apply(this,arguments)||this}Ve.prototype.className="Ellipse",Ve.prototype._centroid=!0,Ve.prototype._attrsAffectingSize=["radiusX","radiusY"],i(Ve),w.addComponentsGetterSetter(Ve,"radius",["x","y"]),w.addGetterSetter(Ve,"radiusX",0,v()),w.addGetterSetter(Ve,"radiusY",0,v()),o.mapMethods(Ve);var Qe,Je=(P(Ze,Qe=le),Ze.prototype._useBufferCanvas=function(){return!(!this.hasShadow()&&1===this.getAbsoluteOpacity()||!this.hasStroke()||!this.getStage())},Ze.prototype._sceneFunc=function(t){var e,i,n,r=this.width(),o=this.height(),a=this.image();a&&(e=this.cropWidth(),i=this.cropHeight(),n=e&&i?[a,this.cropX(),this.cropY(),e,i,0,0,r,o]:[a,0,0,r,o]),(this.hasFill()||this.hasStroke())&&(t.beginPath(),t.rect(0,0,r,o),t.closePath(),t.fillStrokeShape(this)),a&&t.drawImage.apply(t,n)},Ze.prototype._hitFunc=function(t){var e=this.width(),i=this.height();t.beginPath(),t.rect(0,0,e,i),t.closePath(),t.fillStrokeShape(this)},Ze.prototype.getWidth=function(){var t,e=this.image();return null!==(t=this.attrs.width)&&void 0!==t?t:e?e.width:0},Ze.prototype.getHeight=function(){var t,e=this.image();return null!==(t=this.attrs.height)&&void 0!==t?t:e?e.height:0},Ze.fromURL=function(t,e){var i=A.createImageElement();i.onload=function(){var t=new Ze({image:i});e(t)},i.crossOrigin="Anonymous",i.src=t},Ze);function Ze(){return null!==Qe&&Qe.apply(this,arguments)||this}Je.prototype.className="Image",i(Je),w.addGetterSetter(Je,"image"),w.addComponentsGetterSetter(Je,"crop",["x","y","width","height"]),w.addGetterSetter(Je,"cropX",0,v()),w.addGetterSetter(Je,"cropY",0,v()),w.addGetterSetter(Je,"cropWidth",0,v()),w.addGetterSetter(Je,"cropHeight",0,v()),o.mapMethods(Je);var $e,ti=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width"],ei="right",ii="down",ni="left",ri=ti.length,oi=(P(ai,$e=xe),ai.prototype.getText=function(){return this.find("Text")[0]},ai.prototype.getTag=function(){return this.find("Tag")[0]},ai.prototype._addListeners=function(t){for(var e=this,i=function(){e._sync()},n=0;n<ri;n++)t.on(ti[n]+"Change.konva",i)},ai.prototype.getWidth=function(){return this.getText().width()},ai.prototype.getHeight=function(){return this.getText().height()},ai.prototype._sync=function(){var t,e,i,n,r,o,a,s=this.getText(),h=this.getTag();if(s&&h){switch(t=s.width(),e=s.height(),i=h.pointerDirection(),n=h.pointerWidth(),a=h.pointerHeight(),o=r=0,i){case"up":r=t/2,o=-1*a;break;case ei:r=t+n,o=e/2;break;case ii:r=t/2,o=e+a;break;case ni:r=-1*n,o=e/2}h.setAttrs({x:-1*r,y:-1*o,width:t,height:e}),s.setAttrs({x:-1*r,y:-1*o})}},ai);function ai(t){var e=$e.call(this,t)||this;return e.on("add.konva",function(t){this._addListeners(t.child),this._sync()}),e}oi.prototype.className="Label",i(oi),o.mapMethods(oi);var si,hi=(P(di,si=le),di.prototype._sceneFunc=function(t){var e=this.width(),i=this.height(),n=this.pointerDirection(),r=this.pointerWidth(),o=this.pointerHeight(),a=Math.min(this.cornerRadius(),e/2,i/2);t.beginPath(),a?t.moveTo(a,0):t.moveTo(0,0),"up"===n&&(t.lineTo((e-r)/2,0),t.lineTo(e/2,-1*o),t.lineTo((e+r)/2,0)),a?(t.lineTo(e-a,0),t.arc(e-a,a,a,3*Math.PI/2,0,!1)):t.lineTo(e,0),n===ei&&(t.lineTo(e,(i-o)/2),t.lineTo(e+r,i/2),t.lineTo(e,(i+o)/2)),a?(t.lineTo(e,i-a),t.arc(e-a,i-a,a,0,Math.PI/2,!1)):t.lineTo(e,i),n===ii&&(t.lineTo((e+r)/2,i),t.lineTo(e/2,i+o),t.lineTo((e-r)/2,i)),a?(t.lineTo(a,i),t.arc(a,i-a,a,Math.PI/2,Math.PI,!1)):t.lineTo(0,i),n===ni&&(t.lineTo(0,(i+o)/2),t.lineTo(-1*r,i/2),t.lineTo(0,(i-o)/2)),a&&(t.lineTo(0,a),t.arc(a,a,a,Math.PI,3*Math.PI/2,!1)),t.closePath(),t.fillStrokeShape(this)},di.prototype.getSelfRect=function(){var t=0,e=0,i=this.pointerWidth(),n=this.pointerHeight(),r=this.pointerDirection(),o=this.width(),a=this.height();return"up"===r?(e-=n,a+=n):r===ii?a+=n:r===ni?(t-=1.5*i,o+=i):r===ei&&(o+=1.5*i),{x:t,y:e,width:o,height:a}},di);function di(){return null!==si&&si.apply(this,arguments)||this}hi.prototype.className="Tag",i(hi),w.addGetterSetter(hi,"pointerDirection","none"),w.addGetterSetter(hi,"pointerWidth",0,v()),w.addGetterSetter(hi,"pointerHeight",0,v()),w.addGetterSetter(hi,"cornerRadius",0,v()),o.mapMethods(hi);var li,ci=(P(pi,li=le),pi.prototype._sceneFunc=function(t){var e=this.dataArray;t.beginPath();for(var i=!1,n=0;n<e.length;n++){var r=e[n].command,o=e[n].points;switch(r){case"L":t.lineTo(o[0],o[1]);break;case"M":t.moveTo(o[0],o[1]);break;case"C":t.bezierCurveTo(o[0],o[1],o[2],o[3],o[4],o[5]);break;case"Q":t.quadraticCurveTo(o[0],o[1],o[2],o[3]);break;case"A":var a=o[0],s=o[1],h=o[2],d=o[3],l=o[4],c=o[5],p=o[6],u=o[7],f=d<h?h:d,g=d<h?1:h/d,y=d<h?d/h:1;t.translate(a,s),t.rotate(p),t.scale(g,y),t.arc(0,0,f,l,l+c,1-u),t.scale(1/g,1/y),t.rotate(-p),t.translate(-a,-s);break;case"z":i=!0,t.closePath()}}i||this.hasFill()?t.fillStrokeShape(this):t.strokeShape(this)},pi.prototype.getSelfRect=function(){var s=[];this.dataArray.forEach(function(t){if("A"===t.command){var e=t.points[4],i=t.points[5],n=t.points[4]+i,r=Math.PI/180;if(Math.abs(e-n)<r&&(r=Math.abs(e-n)),i<0)for(var o=e-r;n<o;o-=r){var a=pi.getPointOnEllipticalArc(t.points[0],t.points[1],t.points[2],t.points[3],o,0);s.push(a.x,a.y)}else for(o=e+r;o<n;o+=r)a=pi.getPointOnEllipticalArc(t.points[0],t.points[1],t.points[2],t.points[3],o,0),s.push(a.x,a.y)}else if("C"===t.command)for(o=0;o<=1;o+=.01)a=pi.getPointOnCubicBezier(o,t.start.x,t.start.y,t.points[0],t.points[1],t.points[2],t.points[3],t.points[4],t.points[5]),s.push(a.x,a.y);else s=s.concat(t.points)});for(var t,e,i=s[0],n=s[0],r=s[1],o=s[1],a=0;a<s.length/2;a++)t=s[2*a],e=s[2*a+1],isNaN(t)||(i=Math.min(i,t),n=Math.max(n,t)),isNaN(e)||(r=Math.min(r,e),o=Math.max(o,e));return{x:Math.round(i),y:Math.round(r),width:Math.round(n-i),height:Math.round(o-r)}},pi.prototype.getLength=function(){return this.pathLength},pi.prototype.getPointAtLength=function(t){var e,i=0,n=this.dataArray.length;if(!n)return null;for(;i<n&&t>this.dataArray[i].pathLength;)t-=this.dataArray[i].pathLength,++i;if(i===n)return{x:(e=this.dataArray[i-1].points.slice(-2))[0],y:e[1]};if(t<.01)return{x:(e=this.dataArray[i].points.slice(0,2))[0],y:e[1]};var r=this.dataArray[i],o=r.points;switch(r.command){case"L":return pi.getPointOnLine(t,r.start.x,r.start.y,o[0],o[1]);case"C":return pi.getPointOnCubicBezier(t/r.pathLength,r.start.x,r.start.y,o[0],o[1],o[2],o[3],o[4],o[5]);case"Q":return pi.getPointOnQuadraticBezier(t/r.pathLength,r.start.x,r.start.y,o[0],o[1],o[2],o[3]);case"A":var a=o[0],s=o[1],h=o[2],d=o[3],l=o[4],c=o[5],p=o[6];return l+=c*t/r.pathLength,pi.getPointOnEllipticalArc(a,s,h,d,l,p)}return null},pi.getLineLength=function(t,e,i,n){return Math.sqrt((i-t)*(i-t)+(n-e)*(n-e))},pi.getPointOnLine=function(t,e,i,n,r,o,a){void 0===o&&(o=e),void 0===a&&(a=i);var s=(r-i)/(n-e+1e-8),h=Math.sqrt(t*t/(1+s*s));n<e&&(h*=-1);var d,l=s*h;if(n===e)d={x:o,y:a+l};else if((a-i)/(o-e+1e-8)==s)d={x:o+h,y:a+l};else{var c=this.getLineLength(e,i,n,r);if(c<1e-8)return;var p=(o-e)*(n-e)+(a-i)*(r-i),u=e+(p/=c*c)*(n-e),f=i+p*(r-i),g=this.getLineLength(o,a,u,f),y=Math.sqrt(t*t-g*g),h=Math.sqrt(y*y/(1+s*s));n<e&&(h*=-1),d={x:u+h,y:f+(l=s*h)}}return d},pi.getPointOnCubicBezier=function(t,e,i,n,r,o,a,s,h){function d(t){return t*t*t}function l(t){return 3*t*t*(1-t)}function c(t){return 3*t*(1-t)*(1-t)}function p(t){return(1-t)*(1-t)*(1-t)}return{x:s*d(t)+o*l(t)+n*c(t)+e*p(t),y:h*d(t)+a*l(t)+r*c(t)+i*p(t)}},pi.getPointOnQuadraticBezier=function(t,e,i,n,r,o,a){function s(t){return t*t}function h(t){return 2*t*(1-t)}function d(t){return(1-t)*(1-t)}return{x:o*s(t)+n*h(t)+e*d(t),y:a*s(t)+r*h(t)+i*d(t)}},pi.getPointOnEllipticalArc=function(t,e,i,n,r,o){var a=Math.cos(o),s=Math.sin(o),h=i*Math.cos(r),d=n*Math.sin(r);return{x:t+(h*a-d*s),y:e+(h*s+d*a)}},pi.parsePathData=function(t){if(!t)return[];for(var e=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],i=(i=t).replace(new RegExp(" ","g"),","),n=0;n<e.length;n++)i=i.replace(new RegExp(e[n],"g"),"|"+e[n]);for(var r,o=i.split("|"),a=[],s=[],h=0,d=0,l=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi,n=1;n<o.length;n++){var c=(p=o[n]).charAt(0),p=p.slice(1);for(s.length=0;r=l.exec(p);)s.push(r[0]);for(var u=[],f=0,g=s.length;f<g;f++){var y=parseFloat(s[f]);isNaN(y)?u.push(0):u.push(y)}for(;0<u.length&&!isNaN(u[0]);){var v,m,_,b,x,S,w,C,P,k,T=null,A=[],M=h,G=d;switch(c){case"l":h+=u.shift(),d+=u.shift(),T="L",A.push(h,d);break;case"L":h=u.shift(),d=u.shift(),A.push(h,d);break;case"m":var R=u.shift(),I=u.shift();if(h+=R,d+=I,T="M",2<a.length&&"z"===a[a.length-1].command)for(var O=a.length-2;0<=O;O--)if("M"===a[O].command){h=a[O].points[0]+R,d=a[O].points[1]+I;break}A.push(h,d),c="l";break;case"M":h=u.shift(),d=u.shift(),T="M",A.push(h,d),c="L";break;case"h":h+=u.shift(),T="L",A.push(h,d);break;case"H":h=u.shift(),T="L",A.push(h,d);break;case"v":d+=u.shift(),T="L",A.push(h,d);break;case"V":d=u.shift(),T="L",A.push(h,d);break;case"C":A.push(u.shift(),u.shift(),u.shift(),u.shift()),h=u.shift(),d=u.shift(),A.push(h,d);break;case"c":A.push(h+u.shift(),d+u.shift(),h+u.shift(),d+u.shift()),h+=u.shift(),d+=u.shift(),T="C",A.push(h,d);break;case"S":m=h,_=d,"C"===(v=a[a.length-1]).command&&(m=h+(h-v.points[2]),_=d+(d-v.points[3])),A.push(m,_,u.shift(),u.shift()),h=u.shift(),d=u.shift(),T="C",A.push(h,d);break;case"s":m=h,_=d,"C"===(v=a[a.length-1]).command&&(m=h+(h-v.points[2]),_=d+(d-v.points[3])),A.push(m,_,h+u.shift(),d+u.shift()),h+=u.shift(),d+=u.shift(),T="C",A.push(h,d);break;case"Q":A.push(u.shift(),u.shift()),h=u.shift(),d=u.shift(),A.push(h,d);break;case"q":A.push(h+u.shift(),d+u.shift()),h+=u.shift(),d+=u.shift(),T="Q",A.push(h,d);break;case"T":m=h,_=d,"Q"===(v=a[a.length-1]).command&&(m=h+(h-v.points[0]),_=d+(d-v.points[1])),h=u.shift(),d=u.shift(),T="Q",A.push(m,_,h,d);break;case"t":m=h,_=d,"Q"===(v=a[a.length-1]).command&&(m=h+(h-v.points[0]),_=d+(d-v.points[1])),h+=u.shift(),d+=u.shift(),T="Q",A.push(m,_,h,d);break;case"A":b=u.shift(),x=u.shift(),S=u.shift(),w=u.shift(),C=u.shift(),P=h,k=d,h=u.shift(),d=u.shift(),T="A",A=this.convertEndpointToCenterParameterization(P,k,h,d,w,C,b,x,S);break;case"a":b=u.shift(),x=u.shift(),S=u.shift(),w=u.shift(),C=u.shift(),P=h,k=d,h+=u.shift(),d+=u.shift(),T="A",A=this.convertEndpointToCenterParameterization(P,k,h,d,w,C,b,x,S)}a.push({command:T||c,points:A,start:{x:M,y:G},pathLength:this.calcLength(M,G,T||c,A)})}"z"!==c&&"Z"!==c||a.push({command:"z",points:[],start:void 0,pathLength:0})}return a},pi.calcLength=function(t,e,i,n){var r,o,a,s,h=pi;switch(i){case"L":return h.getLineLength(t,e,n[0],n[1]);case"C":for(r=0,o=h.getPointOnCubicBezier(0,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),s=.01;s<=1;s+=.01)a=h.getPointOnCubicBezier(s,t,e,n[0],n[1],n[2],n[3],n[4],n[5]),r+=h.getLineLength(o.x,o.y,a.x,a.y),o=a;return r;case"Q":for(r=0,o=h.getPointOnQuadraticBezier(0,t,e,n[0],n[1],n[2],n[3]),s=.01;s<=1;s+=.01)a=h.getPointOnQuadraticBezier(s,t,e,n[0],n[1],n[2],n[3]),r+=h.getLineLength(o.x,o.y,a.x,a.y),o=a;return r;case"A":r=0;var d=n[4],l=n[5],c=n[4]+l,p=Math.PI/180;if(Math.abs(d-c)<p&&(p=Math.abs(d-c)),o=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],d,0),l<0)for(s=d-p;c<s;s-=p)a=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],s,0),r+=h.getLineLength(o.x,o.y,a.x,a.y),o=a;else for(s=d+p;s<c;s+=p)a=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],s,0),r+=h.getLineLength(o.x,o.y,a.x,a.y),o=a;return a=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],c,0),r+=h.getLineLength(o.x,o.y,a.x,a.y)}return 0},pi.convertEndpointToCenterParameterization=function(t,e,i,n,r,o,a,s,h){var d=h*(Math.PI/180),l=Math.cos(d)*(t-i)/2+Math.sin(d)*(e-n)/2,c=-1*Math.sin(d)*(t-i)/2+Math.cos(d)*(e-n)/2,p=l*l/(a*a)+c*c/(s*s);1<p&&(a*=Math.sqrt(p),s*=Math.sqrt(p));var u=Math.sqrt((a*a*(s*s)-a*a*(c*c)-s*s*(l*l))/(a*a*(c*c)+s*s*(l*l)));function f(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function g(t,e){return(t[0]*e[0]+t[1]*e[1])/(f(t)*f(e))}function y(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(g(t,e))}r===o&&(u*=-1),isNaN(u)&&(u=0);var v=u*a*c/s,m=u*-s*l/a,_=(t+i)/2+Math.cos(d)*v-Math.sin(d)*m,b=(e+n)/2+Math.sin(d)*v+Math.cos(d)*m,x=y([1,0],[(l-v)/a,(c-m)/s]),S=[(l-v)/a,(c-m)/s],w=[(-1*l-v)/a,(-1*c-m)/s],C=y(S,w);return g(S,w)<=-1&&(C=Math.PI),1<=g(S,w)&&(C=0),0===o&&0<C&&(C-=2*Math.PI),1===o&&C<0&&(C+=2*Math.PI),[_,b,a,s,x,C,d,o]},pi);function pi(t){var e=li.call(this,t)||this;e.dataArray=[],e.pathLength=0,e.dataArray=pi.parsePathData(e.data());for(var i=e.pathLength=0;i<e.dataArray.length;++i)e.pathLength+=e.dataArray[i].pathLength;return e.on("dataChange.konva",function(){this.dataArray=pi.parsePathData(this.data());for(var t=this.pathLength=0;t<this.dataArray.length;++t)this.pathLength+=this.dataArray[t].pathLength}),e}ci.prototype.className="Path",ci.prototype._attrsAffectingSize=["data"],i(ci),w.addGetterSetter(ci,"data"),o.mapMethods(ci);var ui,fi=(P(gi,ui=le),gi.prototype._sceneFunc=function(t){var e,i,n,r,o=this.cornerRadius(),a=this.width(),s=this.height();t.beginPath(),o?(r=n=i=e=0,"number"==typeof o?e=i=n=r=Math.min(o,a/2,s/2):(e=Math.min(o[0],a/2,s/2),i=Math.min(o[1],a/2,s/2),r=Math.min(o[2],a/2,s/2),n=Math.min(o[3],a/2,s/2)),t.moveTo(e,0),t.lineTo(a-i,0),t.arc(a-i,i,i,3*Math.PI/2,0,!1),t.lineTo(a,s-r),t.arc(a-r,s-r,r,0,Math.PI/2,!1),t.lineTo(n,s),t.arc(n,s-n,n,Math.PI/2,Math.PI,!1),t.lineTo(0,e),t.arc(e,e,e,Math.PI,3*Math.PI/2,!1)):t.rect(0,0,a,s),t.closePath(),t.fillStrokeShape(this)},gi);function gi(){return null!==ui&&ui.apply(this,arguments)||this}fi.prototype.className="Rect",i(fi),w.addGetterSetter(fi,"cornerRadius",0),o.mapMethods(fi);var yi,vi=(P(mi,yi=le),mi.prototype._sceneFunc=function(t){var e,i,n,r=this.sides(),o=this.radius();for(t.beginPath(),t.moveTo(0,0-o),e=1;e<r;e++)i=o*Math.sin(2*e*Math.PI/r),n=-1*o*Math.cos(2*e*Math.PI/r),t.lineTo(i,n);t.closePath(),t.fillStrokeShape(this)},mi.prototype.getWidth=function(){return 2*this.radius()},mi.prototype.getHeight=function(){return 2*this.radius()},mi.prototype.setWidth=function(t){this.radius(t/2)},mi.prototype.setHeight=function(t){this.radius(t/2)},mi);function mi(){return null!==yi&&yi.apply(this,arguments)||this}vi.prototype.className="RegularPolygon",vi.prototype._centroid=!0,vi.prototype._attrsAffectingSize=["radius"],i(vi),w.addGetterSetter(vi,"radius",0,v()),w.addGetterSetter(vi,"sides",0,v()),o.mapMethods(vi);var _i,bi=2*Math.PI,xi=(P(Si,_i=le),Si.prototype._sceneFunc=function(t){t.beginPath(),t.arc(0,0,this.innerRadius(),0,bi,!1),t.moveTo(this.outerRadius(),0),t.arc(0,0,this.outerRadius(),bi,0,!0),t.closePath(),t.fillStrokeShape(this)},Si.prototype.getWidth=function(){return 2*this.outerRadius()},Si.prototype.getHeight=function(){return 2*this.outerRadius()},Si.prototype.setWidth=function(t){this.outerRadius(t/2)},Si.prototype.setHeight=function(t){this.outerRadius(t/2)},Si);function Si(){return null!==_i&&_i.apply(this,arguments)||this}xi.prototype.className="Ring",xi.prototype._centroid=!0,xi.prototype._attrsAffectingSize=["innerRadius","outerRadius"],i(xi),w.addGetterSetter(xi,"innerRadius",0,v()),w.addGetterSetter(xi,"outerRadius",0,v()),o.mapMethods(xi);var wi,Ci=(P(Pi,wi=le),Pi.prototype._sceneFunc=function(t){var e,i,n=this.animation(),r=this.frameIndex(),o=4*r,a=this.animations()[n],s=this.frameOffsets(),h=a[0+o],d=a[1+o],l=a[2+o],c=a[3+o],p=this.image();(this.hasFill()||this.hasStroke())&&(t.beginPath(),t.rect(0,0,l,c),t.closePath(),t.fillStrokeShape(this)),p&&(s?(e=s[n],i=2*r,t.drawImage(p,h,d,l,c,e[0+i],e[1+i],l,c)):t.drawImage(p,h,d,l,c,0,0,l,c))},Pi.prototype._hitFunc=function(t){var e,i,n=this.animation(),r=this.frameIndex(),o=4*r,a=this.animations()[n],s=this.frameOffsets(),h=a[2+o],d=a[3+o];t.beginPath(),s?(e=s[n],i=2*r,t.rect(e[0+i],e[1+i],h,d)):t.rect(0,0,h,d),t.closePath(),t.fillShape(this)},Pi.prototype._useBufferCanvas=function(){return(this.hasShadow()||1!==this.getAbsoluteOpacity())&&this.hasStroke()},Pi.prototype._setInterval=function(){var t=this;this.interval=setInterval(function(){t._updateIndex()},1e3/this.frameRate())},Pi.prototype.start=function(){var t;this.isRunning()||(t=this.getLayer(),this.anim.setLayers(t),this._setInterval(),this.anim.start())},Pi.prototype.stop=function(){this.anim.stop(),clearInterval(this.interval)},Pi.prototype.isRunning=function(){return this.anim.isRunning()},Pi.prototype._updateIndex=function(){var t=this.frameIndex(),e=this.animation();t<this.animations()[e].length/4-1?this.frameIndex(t+1):this.frameIndex(0)},Pi);function Pi(t){var e=wi.call(this,t)||this;return e._updated=!0,e.anim=new Ce(function(){var t=e._updated;return e._updated=!1,t}),e.on("animationChange.konva",function(){this.frameIndex(0)}),e.on("frameIndexChange.konva",function(){this._updated=!0}),e.on("frameRateChange.konva",function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())}),e}Ci.prototype.className="Sprite",i(Ci),w.addGetterSetter(Ci,"animation"),w.addGetterSetter(Ci,"animations"),w.addGetterSetter(Ci,"frameOffsets"),w.addGetterSetter(Ci,"image"),w.addGetterSetter(Ci,"frameIndex",0,v()),w.addGetterSetter(Ci,"frameRate",17,v()),w.backCompat(Ci,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"}),o.mapMethods(Ci);var ki,Ti=(P(Ai,ki=le),Ai.prototype._sceneFunc=function(t){var e=this.innerRadius(),i=this.outerRadius(),n=this.numPoints();t.beginPath(),t.moveTo(0,0-i);for(var r=1;r<2*n;r++){var o=r%2==0?i:e,a=o*Math.sin(r*Math.PI/n),s=-1*o*Math.cos(r*Math.PI/n);t.lineTo(a,s)}t.closePath(),t.fillStrokeShape(this)},Ai.prototype.getWidth=function(){return 2*this.outerRadius()},Ai.prototype.getHeight=function(){return 2*this.outerRadius()},Ai.prototype.setWidth=function(t){this.outerRadius(t/2)},Ai.prototype.setHeight=function(t){this.outerRadius(t/2)},Ai);function Ai(){return null!==ki&&ki.apply(this,arguments)||this}Ti.prototype.className="Star",Ti.prototype._centroid=!0,Ti.prototype._attrsAffectingSize=["innerRadius","outerRadius"],i(Ti),w.addGetterSetter(Ti,"numPoints",5,v()),w.addGetterSetter(Ti,"innerRadius",0,v()),w.addGetterSetter(Ti,"outerRadius",0,v()),o.mapMethods(Ti);var Mi,Gi="auto",Ri="justify",Ii=["fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],Oi=Ii.length;function Li(){return Mi||(Mi=A.createCanvasElement().getContext("2d"))}var Ei,Di=(P(Fi,Ei=le),Fi.prototype._sceneFunc=function(t){var e,i=this.padding(),n=this.fontSize(),r=this.lineHeight()*n,o=this.textArr,a=o.length,s=this.verticalAlign(),h=0,d=this.align(),l=this.getWidth(),c=this.letterSpacing(),p=this.fill(),u=this.textDecoration(),f=-1!==u.indexOf("underline"),g=-1!==u.indexOf("line-through"),y=0,y=r/2,v=0,m=0;for(t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline","middle"),t.setAttr("textAlign","left"),"middle"===s?h=(this.getHeight()-a*r-2*i)/2:"bottom"===s&&(h=this.getHeight()-a*r-2*i),t.translate(i,h+i),e=0;e<a;e++){var _,b,x,v=0,m=0,S=o[e],w=S.text,C=S.width,P=e!==a-1;if(t.save(),"right"===d?v+=l-C-2*i:"center"===d&&(v+=(l-C-2*i)/2),f&&(t.save(),t.beginPath(),t.moveTo(v,y+m+Math.round(n/2)),b=0==(_=w.split(" ").length-1),x=d===Ri&&P&&!b?l-2*i:C,t.lineTo(v+Math.round(x),y+m+Math.round(n/2)),t.lineWidth=n/15,t.strokeStyle=p,t.stroke(),t.restore()),g&&(t.save(),t.beginPath(),t.moveTo(v,y+m),b=0==(_=w.split(" ").length-1),x=d===Ri&&P&&!b?l-2*i:C,t.lineTo(v+Math.round(x),y+m),t.lineWidth=n/15,t.strokeStyle=p,t.stroke(),t.restore()),0!==c||d===Ri){_=w.split(" ").length-1;for(var k=0;k<w.length;k++){var T=w[k];" "===T&&e!==a-1&&d===Ri&&(v+=Math.floor((l-2*i-C)/_)),this._partialTextX=v,this._partialTextY=y+m,this._partialText=T,t.fillStrokeShape(this),v+=Math.round(this.measureSize(T).width)+c}}else this._partialTextX=v,this._partialTextY=y+m,this._partialText=w,t.fillStrokeShape(this);t.restore(),1<a&&(y+=r)}},Fi.prototype._hitFunc=function(t){var e=this.getWidth(),i=this.getHeight();t.beginPath(),t.rect(0,0,e,i),t.closePath(),t.fillStrokeShape(this)},Fi.prototype.setText=function(t){var e=A._isString(t)?t:null==t?"":t+"";return this._setAttr("text",e),this},Fi.prototype.getWidth=function(){return this.attrs.width===Gi||void 0===this.attrs.width?this.getTextWidth()+2*this.padding():this.attrs.width},Fi.prototype.getHeight=function(){return this.attrs.height===Gi||void 0===this.attrs.height?this.fontSize()*this.textArr.length*this.lineHeight()+2*this.padding():this.attrs.height},Fi.prototype.getTextWidth=function(){return this.textWidth},Fi.prototype.getTextHeight=function(){return A.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight},Fi.prototype.measureSize=function(t){var e,i=Li(),n=this.fontSize();return i.save(),i.font=this._getContextFont(),e=i.measureText(t),i.restore(),{width:e.width,height:n}},Fi.prototype._getContextFont=function(){return G.UA.isIE?this.fontStyle()+" "+this.fontSize()+"px "+this.fontFamily():this.fontStyle()+" "+this.fontVariant()+" "+this.fontSize()+"px "+this.fontFamily()},Fi.prototype._addTextLine=function(t){this.align()===Ri&&(t=t.trim());var e=this._getTextWidth(t);return this.textArr.push({text:t,width:e})},Fi.prototype._getTextWidth=function(t){var e=this.letterSpacing(),i=t.length;return Li().measureText(t).width+(i?e*(i-1):0)},Fi.prototype._setTextData=function(){var t=this.text().split("\n"),e=+this.fontSize(),i=0,n=this.lineHeight()*e,r=this.attrs.width,o=this.attrs.height,a=r!==Gi&&void 0!==r,s=o!==Gi&&void 0!==o,h=this.padding(),d=r-2*h,l=o-2*h,c=0,p=this.wrap(),u="none"!==p,f="char"!==p&&u,g=this.ellipsis()&&!u;this.textArr=[],Li().font=this._getContextFont();for(var y=g?this._getTextWidth("…"):0,v=0,m=t.length;v<m;++v){var _=t[v],b=this._getTextWidth(_);if(a&&d<b)for(;0<_.length;){for(var x,S,w=0,C=_.length,P="",k=0;w<C;){var T=w+C>>>1,A=_.slice(0,1+T),M=this._getTextWidth(A)+y;M<=d?(w=1+T,P=A+(g?"…":""),k=M):C=T}if(!P)break;if(!f||0<(S=(" "===(x=_[P.length])||"-"===x)&&k<=d?P.length:Math.max(P.lastIndexOf(" "),P.lastIndexOf("-"))+1)&&(w=S,P=P.slice(0,w),k=this._getTextWidth(P)),P=P.trimRight(),this._addTextLine(P),i=Math.max(i,k),c+=n,!u||s&&l<c+n)break;if(0<(_=(_=_.slice(w)).trimLeft()).length&&(b=this._getTextWidth(_))<=d){this._addTextLine(_),c+=n,i=Math.max(i,b);break}}else this._addTextLine(_),c+=n,i=Math.max(i,b);if(s&&l<c+n)break}this.textHeight=e,this.textWidth=i},Fi.prototype.getStrokeScaleEnabled=function(){return!0},Fi);function Fi(t){var e,i=Ei.call(this,((e=(e=t)||{}).fillLinearGradientColorStops||e.fillRadialGradientColorStops||e.fillPatternImage||(e.fill=e.fill||"black"),e))||this;i._partialTextX=0;for(var n=i._partialTextY=0;n<Oi;n++)i.on(Ii[n]+"Change.konva",i._setTextData);return i._setTextData(),i}Di.prototype._fillFunc=function(t){t.fillText(this._partialText,this._partialTextX,this._partialTextY)},Di.prototype._strokeFunc=function(t){t.strokeText(this._partialText,this._partialTextX,this._partialTextY)},Di.prototype.className="Text",Di.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight"],i(Di),w.overWriteSetter(Di,"width",m()),w.overWriteSetter(Di,"height",m()),w.addGetterSetter(Di,"fontFamily","Arial"),w.addGetterSetter(Di,"fontSize",12,v()),w.addGetterSetter(Di,"fontStyle","normal"),w.addGetterSetter(Di,"fontVariant","normal"),w.addGetterSetter(Di,"padding",0,v()),w.addGetterSetter(Di,"align","left"),w.addGetterSetter(Di,"verticalAlign","top"),w.addGetterSetter(Di,"lineHeight",1,v()),w.addGetterSetter(Di,"wrap","word"),w.addGetterSetter(Di,"ellipsis",!1),w.addGetterSetter(Di,"letterSpacing",0,v()),w.addGetterSetter(Di,"text","",_()),w.addGetterSetter(Di,"textDecoration",""),o.mapMethods(Di);function Bi(t){t.fillText(this.partialText,0,0)}function Ni(t){t.strokeText(this.partialText,0,0)}var zi,Wi=(P(Hi,zi=le),Hi.prototype._sceneFunc=function(t){t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",this.textBaseline()),t.setAttr("textAlign","left"),t.save();var e=this.textDecoration(),i=this.fill(),n=this.fontSize(),r=this.glyphInfo;"underline"===e&&t.beginPath();for(var o=0;o<r.length;o++){t.save();var a=r[o].p0;t.translate(a.x,a.y),t.rotate(r[o].rotation),this.partialText=r[o].text,t.fillStrokeShape(this),"underline"===e&&(0===o&&t.moveTo(0,n/2+1),t.lineTo(n,n/2+1)),t.restore()}"underline"===e&&(t.strokeStyle=i,t.lineWidth=n/20,t.stroke()),t.restore()},Hi.prototype._hitFunc=function(t){t.beginPath();var e,i=this.glyphInfo;1<=i.length&&(e=i[0].p0,t.moveTo(e.x,e.y));for(var n=0;n<i.length;n++){var r=i[n].p1;t.lineTo(r.x,r.y)}t.setAttr("lineWidth",this.fontSize()),t.setAttr("strokeStyle",this.colorKey),t.stroke()},Hi.prototype.getTextWidth=function(){return this.textWidth},Hi.prototype.getTextHeight=function(){return A.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight},Hi.prototype.setText=function(t){return Di.prototype.setText.call(this,t)},Hi.prototype._getContextFont=function(){return Di.prototype._getContextFont.call(this)},Hi.prototype._getTextSize=function(t){var e=this.dummyCanvas.getContext("2d");e.save(),e.font=this._getContextFont();var i=e.measureText(t);return e.restore(),{width:i.width,height:parseInt(this.attrs.fontSize,10)}},Hi.prototype._setTextData=function(){var d=this,t=this._getTextSize(this.attrs.text),l=this.letterSpacing(),c=this.align(),e=this.kerningFunc();this.textWidth=t.width,this.textHeight=t.height;var p=Math.max(this.textWidth+((this.attrs.text||"").length-1)*l,0);this.glyphInfo=[];for(var u=0,i=0;i<d.dataArray.length;i++)0<d.dataArray[i].pathLength&&(u+=d.dataArray[i].pathLength);var n=0;"center"===c&&(n=Math.max(0,u/2-p/2)),"right"===c&&(n=Math.max(0,u-p));for(var f,g,y,r=this.text().split(""),v=this.text().split(" ").length-1,o=-1,m=0,_=function(){m=0;for(var t=d.dataArray,e=o+1;e<t.length;e++){if(0<t[e].pathLength)return t[o=e];"M"===t[e].command&&(f={x:t[e].points[0],y:t[e].points[1]})}return{}},a=function(t){var e=d._getTextSize(t).width+l;" "===t&&"justify"===c&&(e+=(u-p)/v);var i=0,n=0;for(g=void 0;.01<Math.abs(e-i)/e&&n<25;){n++;for(var r=i;void 0===y;)(y=_())&&r+y.pathLength<e&&(r+=y.pathLength,y=void 0);if(y==={}||void 0===f)return;var o=!1;switch(y.command){case"L":ci.getLineLength(f.x,f.y,y.points[0],y.points[1])>e?g=ci.getPointOnLine(e,f.x,f.y,y.points[0],y.points[1],f.x,f.y):y=void 0;break;case"A":var a=y.points[4],s=y.points[5],h=y.points[4]+s;0===m?m=a+1e-8:i<e?m+=Math.PI/180*s/Math.abs(s):m-=Math.PI/360*s/Math.abs(s),(s<0&&m<h||0<=s&&h<m)&&(m=h,o=!0),g=ci.getPointOnEllipticalArc(y.points[0],y.points[1],y.points[2],y.points[3],m,y.points[6]);break;case"C":0===m?m=e>y.pathLength?1e-8:e/y.pathLength:i<e?m+=(e-i)/y.pathLength:m-=(i-e)/y.pathLength,1<m&&(m=1,o=!0),g=ci.getPointOnCubicBezier(m,y.start.x,y.start.y,y.points[0],y.points[1],y.points[2],y.points[3],y.points[4],y.points[5]);break;case"Q":0===m?m=e/y.pathLength:i<e?m+=(e-i)/y.pathLength:m-=(i-e)/y.pathLength,1<m&&(m=1,o=!0),g=ci.getPointOnQuadraticBezier(m,y.start.x,y.start.y,y.points[0],y.points[1],y.points[2],y.points[3])}void 0!==g&&(i=ci.getLineLength(f.x,f.y,g.x,g.y)),o&&(o=!1,y=void 0)}},s=n/(d._getTextSize("C").width+l)-1,h=0;h<s&&(a("C"),void 0!==f&&void 0!==g);h++)f=g;for(var b=0;b<r.length&&(a(r[b]),void 0!==f&&void 0!==g);b++){var x=ci.getLineLength(f.x,f.y,g.x,g.y),S=0;if(e)try{S=e(r[b-1],r[b])*this.fontSize()}catch(t){S=0}f.x+=S,g.x+=S,this.textWidth+=S;var w=ci.getPointOnLine(S+x/2,f.x,f.y,g.x,g.y),C=Math.atan2(g.y-f.y,g.x-f.x);this.glyphInfo.push({transposeX:w.x,transposeY:w.y,text:r[b],rotation:C,p0:f,p1:g}),f=g}},Hi.prototype.getSelfRect=function(){if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};var e=[];this.glyphInfo.forEach(function(t){e.push(t.p0.x),e.push(t.p0.y),e.push(t.p1.x),e.push(t.p1.y)});for(var t,i,n=e[0]||0,r=e[0]||0,o=e[1]||0,a=e[1]||0,s=0;s<e.length/2;s++)t=e[2*s],i=e[2*s+1],n=Math.min(n,t),r=Math.max(r,t),o=Math.min(o,i),a=Math.max(a,i);var h=this.fontSize();return{x:n-h/2,y:o-h/2,width:r-n+h,height:a-o+h}},Hi);function Hi(t){var e=zi.call(this,t)||this;return e.dummyCanvas=A.createCanvasElement(),e.dataArray=[],e.dataArray=ci.parsePathData(e.attrs.data),e.on("dataChange.konva",function(){this.dataArray=ci.parsePathData(this.attrs.data),this._setTextData()}),e.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva",e._setTextData),t&&t.getKerning&&(A.warn('getKerning TextPath API is deprecated. Please use "kerningFunc" instead.'),e.kerningFunc(t.getKerning)),e._setTextData(),e}Wi.prototype._fillFunc=Bi,Wi.prototype._strokeFunc=Ni,Wi.prototype._fillFuncHit=Bi,Wi.prototype._strokeFuncHit=Ni,Wi.prototype.className="TextPath",Wi.prototype._attrsAffectingSize=["text","fontSize","data"],i(Wi),w.addGetterSetter(Wi,"data"),w.addGetterSetter(Wi,"fontFamily","Arial"),w.addGetterSetter(Wi,"fontSize",12,v()),w.addGetterSetter(Wi,"fontStyle","normal"),w.addGetterSetter(Wi,"align","left"),w.addGetterSetter(Wi,"letterSpacing",0,v()),w.addGetterSetter(Wi,"textBaseline","middle"),w.addGetterSetter(Wi,"fontVariant","normal"),w.addGetterSetter(Wi,"text",""),w.addGetterSetter(Wi,"textDecoration",null),w.addGetterSetter(Wi,"kerningFunc",null),o.mapMethods(Wi);var Yi="tr-konva",Xi=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange"].map(function(t){return t+"."+Yi}).join(" "),ji="nodesRect",Ui=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"].map(function(t){return t+"."+Yi}).join(" "),qi={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135},Vi="ontouchstart"in G._global;var Ki=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function Qi(t,e,i){var n=i.x+(t.x-i.x)*Math.cos(e)-(t.y-i.y)*Math.sin(e),r=i.y+(t.x-i.x)*Math.sin(e)+(t.y-i.y)*Math.cos(e);return k(k({},t),{rotation:t.rotation+e,x:n,y:r})}function Ji(t,e){var i;return Qi(t,e,{x:(i=t).x+i.width/2*Math.cos(i.rotation)+i.height/2*Math.sin(-i.rotation),y:i.y+i.height/2*Math.cos(i.rotation)+i.width/2*Math.sin(i.rotation)})}var Zi,$i=(P(tn,Zi=xe),tn.prototype.attachTo=function(t){return this.setNode(t),this},tn.prototype.setNode=function(t){return A.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([t])},tn.prototype.getNode=function(){return this._nodes&&this._nodes[0]},tn.prototype.setNodes=function(t){var n=this;return void 0===t&&(t=[]),this._nodes&&this._nodes.length&&this.detach(),1===(this._nodes=t).length?this.rotation(t[0].rotation()):this.rotation(0),this._nodes.forEach(function(t){function e(){n._resetTransformCache(),n._transforming||n.update()}var i=t._attrsAffectingSize.map(function(t){return t+"Change."+Yi}).join(" ");t.on(i,e),t.on(Ui,e),t.on("_clearTransformCache."+Yi,e),t.on("xChange."+Yi+" yChange."+Yi,e),n._proxyDrag(t)}),this._resetTransformCache(),this.findOne(".top-left")&&this.update(),this},tn.prototype._proxyDrag=function(r){var e,o=this;r.on("dragstart."+Yi,function(){e=r.getAbsolutePosition()}),r.on("dragmove."+Yi,function(){var t,i,n;e&&(t=r.getAbsolutePosition(),i=t.x-e.x,n=t.y-e.y,o.nodes().forEach(function(t){var e;t!==r&&(t.isDragging()||(e=t.getAbsolutePosition(),t.setAbsolutePosition({x:e.x+i,y:e.y+n}),t.startDrag()))}),e=null)})},tn.prototype.getNodes=function(){return this._nodes},tn.prototype.getActiveAnchor=function(){return this._movingAnchorName},tn.prototype.detach=function(){this._nodes&&this._nodes.forEach(function(t){t.off("."+Yi)}),this._nodes=[],this._resetTransformCache()},tn.prototype._resetTransformCache=function(){this._clearCache(ji),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")},tn.prototype._getNodeRect=function(){return this._getCache(ji,this.__getNodeRect)},tn.prototype.__getNodeShape=function(t,e,i){void 0===e&&(e=this.rotation());var n=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),r=t.getAbsoluteScale(i),o=t.getAbsolutePosition(i),a=n.x*r.x-t.offsetX()*r.x,s=n.y*r.y-t.offsetY()*r.y,h=(G.getAngle(t.getAbsoluteRotation())+2*Math.PI)%(2*Math.PI);return Qi({x:o.x+a*Math.cos(h)+s*Math.sin(-h),y:o.y+s*Math.cos(h)+a*Math.sin(h),width:n.width*r.x,height:n.height*r.y,rotation:h},-G.getAngle(e),{x:0,y:0})},tn.prototype.__getNodeRect=function(){var r=this;if(!this.getNode())return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};var o=[];this.nodes().map(function(t){var e=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:r.ignoreStroke()}),i=[{x:e.x,y:e.y},{x:e.x+e.width,y:e.y},{x:e.x+e.width,y:e.y+e.height},{x:e.x,y:e.y+e.height}],n=t.getAbsoluteTransform();i.forEach(function(t){var e=n.point(t);o.push(e)})});var i,n,a,s,h=new l;h.rotate(-G.getAngle(this.rotation())),o.forEach(function(t){var e=h.point(t);void 0===i&&(i=a=e.x,n=s=e.y),i=Math.min(i,e.x),n=Math.min(n,e.y),a=Math.max(a,e.x),s=Math.max(s,e.y)}),h.invert();var t=h.point({x:i,y:n});return{x:t.x,y:t.y,width:a-i,height:s-n,rotation:G.getAngle(this.rotation())}},tn.prototype.getX=function(){return this._getNodeRect().x},tn.prototype.getY=function(){return this._getNodeRect().y},tn.prototype.getWidth=function(){return this._getNodeRect().width},tn.prototype.getHeight=function(){return this._getNodeRect().height},tn.prototype._createElements=function(){this._createBack(),Ki.forEach(function(t){this._createAnchor(t)}.bind(this)),this._createAnchor("rotater")},tn.prototype._createAnchor=function(i){var n=this,r=new fi({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:i+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:Vi?10:"auto"}),e=this;r.on("mousedown touchstart",function(t){e._handleMouseDown(t)}),r.on("dragstart",function(t){r.stopDrag(),t.cancelBubble=!0}),r.on("dragend",function(t){t.cancelBubble=!0}),r.on("mouseenter",function(){var t=G.getAngle(n.rotation()),e=function(t,e){if("rotater"===t)return"crosshair";e+=A._degToRad(qi[t]||0);var i=(A._radToDeg(e)%360+360)%360;return A._inRange(i,337.5,360)||A._inRange(i,0,22.5)?"ns-resize":A._inRange(i,22.5,67.5)?"nesw-resize":A._inRange(i,67.5,112.5)?"ew-resize":A._inRange(i,112.5,157.5)?"nwse-resize":A._inRange(i,157.5,202.5)?"ns-resize":A._inRange(i,202.5,247.5)?"nesw-resize":A._inRange(i,247.5,292.5)?"ew-resize":A._inRange(i,292.5,337.5)?"nwse-resize":(A.error("Transformer has unknown angle for cursor detection: "+i),"pointer")}(i,t);r.getStage().content.style.cursor=e,n._cursorChange=!0}),r.on("mouseout",function(){r.getStage().content.style.cursor="",n._cursorChange=!1}),this.add(r)},tn.prototype._createBack=function(){var n=this,t=new le({name:"back",width:0,height:0,draggable:!0,sceneFunc:function(t){var e=this.getParent(),i=e.padding();t.beginPath(),t.rect(-i,-i,this.width()+2*i,this.height()+2*i),t.moveTo(this.width()/2,-i),e.rotateEnabled()&&t.lineTo(this.width()/2,-e.rotateAnchorOffset()*A._sign(this.height())-i),t.fillStrokeShape(this)},hitFunc:function(t,e){var i;n.shouldOverdrawWholeArea()&&(i=n.padding(),t.beginPath(),t.rect(-i,-i,e.width()+2*i,e.height()+2*i),t.fillStrokeShape(e))}});this.add(t),this._proxyDrag(t)},tn.prototype._handleMouseDown=function(t){this._movingAnchorName=t.target.name().split(" ")[0];var e=this._getNodeRect(),i=e.width,n=e.height,r=Math.sqrt(Math.pow(i,2)+Math.pow(n,2));this.sin=Math.abs(n/r),this.cos=Math.abs(i/r),window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0),this._transforming=!0;var o=t.target.getAbsolutePosition(),a=t.target.getStage().getPointerPosition();this._anchorDragOffset={x:a.x-o.x,y:a.y-o.y},this._fire("transformstart",{evt:t,target:this.getNode()}),this.getNode()._fire("transformstart",{evt:t,target:this.getNode()})},tn.prototype._handleMouseMove=function(t){var e=this.findOne("."+this._movingAnchorName),i=e.getStage();i.setPointersPositions(t);var n=i.getPointerPosition(),r={x:n.x-this._anchorDragOffset.x,y:n.y-this._anchorDragOffset.y},o=e.getAbsolutePosition();e.setAbsolutePosition(r);var a,s,h,d,l,c,p,u,f,g,y,v,m,_,b,x,S,w,C,P,k,T,A,M=e.getAbsolutePosition();o.x===M.x&&o.y===M.y||("rotater"!==this._movingAnchorName?(a=this.keepRatio()||t.shiftKey,p=this.centeredScaling()||t.altKey,"top-left"===this._movingAnchorName?a&&(d=p?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()},s=Math.sqrt(Math.pow(d.x-e.x(),2)+Math.pow(d.y-e.y(),2)),l=this.findOne(".top-left").x()>d.x?-1:1,c=this.findOne(".top-left").y()>d.y?-1:1,w=s*this.cos*l,C=s*this.sin*c,this.findOne(".top-left").x(d.x-w),this.findOne(".top-left").y(d.y-C)):"top-center"===this._movingAnchorName?this.findOne(".top-left").y(e.y()):"top-right"===this._movingAnchorName?(a&&(d=p?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()},s=Math.sqrt(Math.pow(e.x()-d.x,2)+Math.pow(d.y-e.y(),2)),l=this.findOne(".top-right").x()<d.x?-1:1,c=this.findOne(".top-right").y()>d.y?-1:1,w=s*this.cos*l,C=s*this.sin*c,this.findOne(".top-right").x(d.x+w),this.findOne(".top-right").y(d.y-C)),h=e.position(),this.findOne(".top-left").y(h.y),this.findOne(".bottom-right").x(h.x)):"middle-left"===this._movingAnchorName?this.findOne(".top-left").x(e.x()):"middle-right"===this._movingAnchorName?this.findOne(".bottom-right").x(e.x()):"bottom-left"===this._movingAnchorName?(a&&(d=p?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()},s=Math.sqrt(Math.pow(d.x-e.x(),2)+Math.pow(e.y()-d.y,2)),l=d.x<e.x()?-1:1,c=e.y()<d.y?-1:1,w=s*this.cos*l,C=s*this.sin*c,e.x(d.x-w),e.y(d.y+C)),h=e.position(),this.findOne(".top-left").x(h.x),this.findOne(".bottom-right").y(h.y)):"bottom-center"===this._movingAnchorName?this.findOne(".bottom-right").y(e.y()):"bottom-right"===this._movingAnchorName?a&&(d=p?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()},s=Math.sqrt(Math.pow(e.x()-d.x,2)+Math.pow(e.y()-d.y,2)),l=this.findOne(".bottom-right").x()<d.x?-1:1,c=this.findOne(".bottom-right").y()<d.y?-1:1,w=s*this.cos*l,C=s*this.sin*c,this.findOne(".bottom-right").x(d.x+w),this.findOne(".bottom-right").y(d.y+C)):console.error(new Error("Wrong position argument of selection resizer: "+this._movingAnchorName)),(p=this.centeredScaling()||t.altKey)&&(u=this.findOne(".top-left"),f=this.findOne(".bottom-right"),g=u.x(),y=u.y(),v=this.getWidth()-f.x(),m=this.getHeight()-f.y(),f.move({x:-g,y:-y}),u.move({x:v,y:m})),w=(_=this.findOne(".top-left").getAbsolutePosition()).x,C=_.y,b=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),x=this.findOne(".bottom-right").y()-this.findOne(".top-left").y(),this._fitNodesInto({x:w,y:C,width:b,height:x,rotation:G.getAngle(this.rotation())},t)):(S=this._getNodeRect(),w=e.x()-S.width/2,C=-e.y()+S.height/2,P=Math.atan2(-C,w)+Math.PI/2,S.height<0&&(P-=Math.PI),k=G.getAngle(this.rotation())+P,T=G.getAngle(this.rotationSnapTolerance()),A=Ji(S,function(t,e,i){for(var n=e,r=0;r<t.length;r++){var o=G.getAngle(t[r]),a=Math.abs(o-e)%(2*Math.PI);Math.min(a,2*Math.PI-a)<i&&(n=o)}return n}(this.rotationSnaps(),k,T)-S.rotation),this._fitNodesInto(A,t)))},tn.prototype._handleMouseUp=function(t){this._removeEvents(t)},tn.prototype.getAbsoluteTransform=function(){return this.getTransform()},tn.prototype._removeEvents=function(t){var e;this._transforming&&(this._transforming=!1,window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0),e=this.getNode(),this._fire("transformend",{evt:t,target:e}),e&&e.fire("transformend",{evt:t,target:e}),this._movingAnchorName=null)},tn.prototype._fitNodesInto=function(t,o){var e,i,n,r,a,s,h=this,d=this._getNodeRect();A._inRange(t.width,2*-this.padding()-1,1)||A._inRange(t.height,2*-this.padding()-1,1)?this.update():((e=new l).rotate(G.getAngle(this.rotation())),this._movingAnchorName&&t.width<0&&0<=this._movingAnchorName.indexOf("left")?(i=e.point({x:2*-this.padding(),y:0}),t.x+=i.x,t.y+=i.y,t.width+=2*this.padding(),this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=i.x,this._anchorDragOffset.y-=i.y):this._movingAnchorName&&t.width<0&&0<=this._movingAnchorName.indexOf("right")&&(i=e.point({x:2*this.padding(),y:0}),this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=i.x,this._anchorDragOffset.y-=i.y,t.width+=2*this.padding()),this._movingAnchorName&&t.height<0&&0<=this._movingAnchorName.indexOf("top")?(i=e.point({x:0,y:2*-this.padding()}),t.x+=i.x,t.y+=i.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=i.x,this._anchorDragOffset.y-=i.y,t.height+=2*this.padding()):this._movingAnchorName&&t.height<0&&0<=this._movingAnchorName.indexOf("bottom")&&(i=e.point({x:0,y:2*this.padding()}),this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=i.x,this._anchorDragOffset.y-=i.y,t.height+=2*this.padding()),this.boundBoxFunc()&&((n=this.boundBoxFunc()(d,t))?t=n:A.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")),(r=new l).translate(d.x,d.y),r.rotate(d.rotation),r.scale(d.width/1e7,d.height/1e7),(a=new l).translate(t.x,t.y),a.rotate(t.rotation),a.scale(t.width/1e7,t.height/1e7),s=a.multiply(r.invert()),this._nodes.forEach(function(t){var e=t.getParent().getAbsoluteTransform(),i=t.getTransform().copy();i.translate(t.offsetX(),t.offsetY());var n=new l;n.multiply(e.copy().invert()).multiply(s).multiply(e).multiply(i);var r=n.decompose();t._batchTransformChanges(function(){t.setAttrs(r)}),h._fire("transform",{evt:o,target:t}),t._fire("transform",{evt:o,target:t})}),this.rotation(A._getRotation(t.rotation)),this._resetTransformCache(),this.update(),this.getLayer().batchDraw())},tn.prototype.forceUpdate=function(){this._resetTransformCache(),this.update()},tn.prototype._batchChangeChild=function(t,e){var i=this.findOne(t);i._batchTransformChanges(function(){i.setAttrs(e)})},tn.prototype.update=function(){var e=this,t=this._getNodeRect();this.rotation(A._getRotation(t.rotation));var i=t.width,n=t.height,r=this.enabledAnchors(),o=this.resizeEnabled(),a=this.padding(),s=this.anchorSize();this.find("._anchor").each(function(t){t._batchTransformChanges(function(){t.setAttrs({width:s,height:s,offsetX:s/2,offsetY:s/2,stroke:e.anchorStroke(),strokeWidth:e.anchorStrokeWidth(),fill:e.anchorFill(),cornerRadius:e.anchorCornerRadius()})})}),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:s/2+a,offsetY:s/2+a,visible:o&&0<=r.indexOf("top-left")}),this._batchChangeChild(".top-center",{x:i/2,y:0,offsetY:s/2+a,visible:o&&0<=r.indexOf("top-center")}),this._batchChangeChild(".top-right",{x:i,y:0,offsetX:s/2-a,offsetY:s/2+a,visible:o&&0<=r.indexOf("top-right")}),this._batchChangeChild(".middle-left",{x:0,y:n/2,offsetX:s/2+a,visible:o&&0<=r.indexOf("middle-left")}),this._batchChangeChild(".middle-right",{x:i,y:n/2,offsetX:s/2-a,visible:o&&0<=r.indexOf("middle-right")}),this._batchChangeChild(".bottom-left",{x:0,y:n,offsetX:s/2+a,offsetY:s/2-a,visible:o&&0<=r.indexOf("bottom-left")}),this._batchChangeChild(".bottom-center",{x:i/2,y:n,offsetY:s/2-a,visible:o&&0<=r.indexOf("bottom-center")}),this._batchChangeChild(".bottom-right",{x:i,y:n,offsetX:s/2-a,offsetY:s/2-a,visible:o&&0<=r.indexOf("bottom-right")}),this._batchChangeChild(".rotater",{x:i/2,y:-this.rotateAnchorOffset()*A._sign(n)-a,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:i,height:n,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0})},tn.prototype.isTransforming=function(){return this._transforming},tn.prototype.stopTransform=function(){var t;this._transforming&&(this._removeEvents(),(t=this.findOne("."+this._movingAnchorName))&&t.stopDrag())},tn.prototype.destroy=function(){return this.getStage()&&this._cursorChange&&(this.getStage().content.style.cursor=""),xe.prototype.destroy.call(this),this.detach(),this._removeEvents(),this},tn.prototype.toObject=function(){return lt.prototype.toObject.call(this)},tn);function tn(t){var e=Zi.call(this,t)||this;return e._transforming=!1,e._createElements(),e._handleMouseMove=e._handleMouseMove.bind(e),e._handleMouseUp=e._handleMouseUp.bind(e),e.update=e.update.bind(e),e.on(Xi,e.update),e.getNode()&&e.update(),e}$i.prototype.className="Transformer",i($i),w.addGetterSetter($i,"enabledAnchors",Ki,function(t){return t instanceof Array||A.warn("enabledAnchors value should be an array"),t instanceof Array&&t.forEach(function(t){-1===Ki.indexOf(t)&&A.warn("Unknown anchor name: "+t+". Available names are: "+Ki.join(", "))}),t||[]}),w.addGetterSetter($i,"resizeEnabled",!0),w.addGetterSetter($i,"anchorSize",10,v()),w.addGetterSetter($i,"rotateEnabled",!0),w.addGetterSetter($i,"rotationSnaps",[]),w.addGetterSetter($i,"rotateAnchorOffset",50,v()),w.addGetterSetter($i,"rotationSnapTolerance",5,v()),w.addGetterSetter($i,"borderEnabled",!0),w.addGetterSetter($i,"anchorStroke","rgb(0, 161, 255)"),w.addGetterSetter($i,"anchorStrokeWidth",1,v()),w.addGetterSetter($i,"anchorFill","white"),w.addGetterSetter($i,"anchorCornerRadius",0,v()),w.addGetterSetter($i,"borderStroke","rgb(0, 161, 255)"),w.addGetterSetter($i,"borderStrokeWidth",1,v()),w.addGetterSetter($i,"borderDash"),w.addGetterSetter($i,"keepRatio",!0),w.addGetterSetter($i,"centeredScaling",!1),w.addGetterSetter($i,"ignoreStroke",!1),w.addGetterSetter($i,"padding",0,v()),w.addGetterSetter($i,"node"),w.addGetterSetter($i,"nodes"),w.addGetterSetter($i,"boundBoxFunc"),w.addGetterSetter($i,"shouldOverdrawWholeArea",!1),w.backCompat($i,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"}),o.mapMethods($i);var en,nn=(P(rn,en=le),rn.prototype._sceneFunc=function(t){t.beginPath(),t.arc(0,0,this.radius(),0,G.getAngle(this.angle()),this.clockwise()),t.lineTo(0,0),t.closePath(),t.fillStrokeShape(this)},rn.prototype.getWidth=function(){return 2*this.radius()},rn.prototype.getHeight=function(){return 2*this.radius()},rn.prototype.setWidth=function(t){this.radius(t/2)},rn.prototype.setHeight=function(t){this.radius(t/2)},rn);function rn(){return null!==en&&en.apply(this,arguments)||this}function on(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}nn.prototype.className="Wedge",nn.prototype._centroid=!0,nn.prototype._attrsAffectingSize=["radius"],i(nn),w.addGetterSetter(nn,"radius",0,v()),w.addGetterSetter(nn,"angle",0,v()),w.addGetterSetter(nn,"clockwise",!1),w.backCompat(nn,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"}),o.mapMethods(nn);var an=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],sn=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];w.addGetterSetter(lt,"blurRadius",0,v(),w.afterSetFilter);w.addGetterSetter(lt,"brightness",0,v(),w.afterSetFilter);w.addGetterSetter(lt,"contrast",0,v(),w.afterSetFilter);function hn(t,e,i,n,r){var o=i-e,a=r-n;return 0==o?n+a/2:0==a?n:a*((t-e)/o)+n}w.addGetterSetter(lt,"embossStrength",.5,v(),w.afterSetFilter),w.addGetterSetter(lt,"embossWhiteLevel",.5,v(),w.afterSetFilter),w.addGetterSetter(lt,"embossDirection","top-left",null,w.afterSetFilter),w.addGetterSetter(lt,"embossBlend",!1,null,w.afterSetFilter);w.addGetterSetter(lt,"enhance",0,v(),w.afterSetFilter);w.addGetterSetter(lt,"hue",0,v(),w.afterSetFilter),w.addGetterSetter(lt,"saturation",0,v(),w.afterSetFilter),w.addGetterSetter(lt,"luminance",0,v(),w.afterSetFilter);w.addGetterSetter(lt,"hue",0,v(),w.afterSetFilter),w.addGetterSetter(lt,"saturation",0,v(),w.afterSetFilter),w.addGetterSetter(lt,"value",0,v(),w.afterSetFilter);function dn(t,e,i){var n=4*(i*t.width+e),r=[];return r.push(t.data[n++],t.data[n++],t.data[n++],t.data[n++]),r}function ln(t,e){return Math.sqrt(Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)+Math.pow(t[2]-e[2],2))}function cn(t,e){var i=dn(t,0,0),n=dn(t,t.width-1,0),r=dn(t,0,t.height-1),o=dn(t,t.width-1,t.height-1),a=e||10;if(ln(i,n)<a&&ln(n,o)<a&&ln(o,r)<a&&ln(r,i)<a){for(var s=function(t){for(var e=[0,0,0],i=0;i<t.length;i++)e[0]+=t[i][0],e[1]+=t[i][1],e[2]+=t[i][2];return e[0]/=t.length,e[1]/=t.length,e[2]/=t.length,e}([n,i,o,r]),h=[],d=0;d<t.width*t.height;d++){var l=ln(s,[t.data[4*d],t.data[4*d+1],t.data[4*d+2]]);h[d]=l<a?0:255}return h}}w.addGetterSetter(lt,"kaleidoscopePower",2,v(),w.afterSetFilter),w.addGetterSetter(lt,"kaleidoscopeAngle",0,v(),w.afterSetFilter);w.addGetterSetter(lt,"threshold",0,v(),w.afterSetFilter);w.addGetterSetter(lt,"noise",.2,v(),w.afterSetFilter);w.addGetterSetter(lt,"pixelSize",8,v(),w.afterSetFilter);w.addGetterSetter(lt,"levels",.5,v(),w.afterSetFilter);w.addGetterSetter(lt,"red",0,function(t){return this._filterUpToDate=!1,255<t?255:t<0?0:Math.round(t)}),w.addGetterSetter(lt,"green",0,function(t){return this._filterUpToDate=!1,255<t?255:t<0?0:Math.round(t)}),w.addGetterSetter(lt,"blue",0,y,w.afterSetFilter);w.addGetterSetter(lt,"red",0,function(t){return this._filterUpToDate=!1,255<t?255:t<0?0:Math.round(t)}),w.addGetterSetter(lt,"green",0,function(t){return this._filterUpToDate=!1,255<t?255:t<0?0:Math.round(t)}),w.addGetterSetter(lt,"blue",0,y,w.afterSetFilter),w.addGetterSetter(lt,"alpha",1,function(t){return this._filterUpToDate=!1,1<t?1:t<0?0:t});return w.addGetterSetter(lt,"threshold",.5,v(),w.afterSetFilter),Ee.Util._assign(Ee,{Arc:De,Arrow:He,Circle:je,Ellipse:Ve,Image:Je,Label:oi,Tag:hi,Line:Ne,Path:ci,Rect:fi,RegularPolygon:vi,Ring:xi,Sprite:Ci,Star:Ti,Text:Di,TextPath:Wi,Transformer:$i,Wedge:nn,Filters:{Blur:function(t){var e=Math.round(this.blurRadius());0<e&&function(t,e){for(var i,n,r,o,a,s,h,d,l,c,p,u,f,g,y,v,m,_,b,x,S,w,C,P=t.data,k=t.width,T=t.height,A=e+e+1,M=k-1,G=T-1,R=e+1,I=R*(R+1)/2,O=new on,L=null,E=O,D=null,F=null,B=an[e],N=sn[e],z=1;z<A;z++)E=E.next=new on,z===R&&(L=E);for(E.next=O,s=a=0,n=0;n<T;n++){for(y=v=m=_=h=d=l=c=0,p=R*(b=P[a]),u=R*(x=P[a+1]),f=R*(S=P[a+2]),g=R*(w=P[a+3]),h+=I*b,d+=I*x,l+=I*S,c+=I*w,E=O,z=0;z<R;z++)E.r=b,E.g=x,E.b=S,E.a=w,E=E.next;for(z=1;z<R;z++)r=a+((M<z?M:z)<<2),h+=(E.r=b=P[r])*(C=R-z),d+=(E.g=x=P[r+1])*C,l+=(E.b=S=P[r+2])*C,c+=(E.a=w=P[r+3])*C,y+=b,v+=x,m+=S,_+=w,E=E.next;for(D=O,F=L,i=0;i<k;i++)P[a+3]=w=c*B>>N,0!==w?(w=255/w,P[a]=(h*B>>N)*w,P[a+1]=(d*B>>N)*w,P[a+2]=(l*B>>N)*w):P[a]=P[a+1]=P[a+2]=0,h-=p,d-=u,l-=f,c-=g,p-=D.r,u-=D.g,f-=D.b,g-=D.a,r=s+((r=i+e+1)<M?r:M)<<2,h+=y+=D.r=P[r],d+=v+=D.g=P[r+1],l+=m+=D.b=P[r+2],c+=_+=D.a=P[r+3],D=D.next,p+=b=F.r,u+=x=F.g,f+=S=F.b,g+=w=F.a,y-=b,v-=x,m-=S,_-=w,F=F.next,a+=4;s+=k}for(i=0;i<k;i++){for(v=m=_=y=d=l=c=h=0,p=R*(b=P[a=i<<2]),u=R*(x=P[a+1]),f=R*(S=P[a+2]),g=R*(w=P[a+3]),h+=I*b,d+=I*x,l+=I*S,c+=I*w,E=O,z=0;z<R;z++)E.r=b,E.g=x,E.b=S,E.a=w,E=E.next;for(o=k,z=1;z<=e;z++)a=o+i<<2,h+=(E.r=b=P[a])*(C=R-z),d+=(E.g=x=P[a+1])*C,l+=(E.b=S=P[a+2])*C,c+=(E.a=w=P[a+3])*C,y+=b,v+=x,m+=S,_+=w,E=E.next,z<G&&(o+=k);for(a=i,D=O,F=L,n=0;n<T;n++)P[(r=a<<2)+3]=w=c*B>>N,0<w?(w=255/w,P[r]=(h*B>>N)*w,P[r+1]=(d*B>>N)*w,P[r+2]=(l*B>>N)*w):P[r]=P[r+1]=P[r+2]=0,h-=p,d-=u,l-=f,c-=g,p-=D.r,u-=D.g,f-=D.b,g-=D.a,r=i+((r=n+R)<G?r:G)*k<<2,h+=y+=D.r=P[r],d+=v+=D.g=P[r+1],l+=m+=D.b=P[r+2],c+=_+=D.a=P[r+3],D=D.next,p+=b=F.r,u+=x=F.g,f+=S=F.b,g+=w=F.a,y-=b,v-=x,m-=S,_-=w,F=F.next,a+=k}}(t,e)},Brighten:function(t){for(var e=255*this.brightness(),i=t.data,n=i.length,r=0;r<n;r+=4)i[r]+=e,i[r+1]+=e,i[r+2]+=e},Contrast:function(t){for(var e=Math.pow((this.contrast()+100)/100,2),i=t.data,n=i.length,r=150,o=150,a=150,s=0;s<n;s+=4)r=i[s],o=i[s+1],a=i[s+2],r/=255,r-=.5,r*=e,r+=.5,o/=255,o-=.5,o*=e,o+=.5,a/=255,a-=.5,a*=e,a+=.5,r=(r*=255)<0?0:255<r?255:r,o=(o*=255)<0?0:255<o?255:o,a=(a*=255)<0?0:255<a?255:a,i[s]=r,i[s+1]=o,i[s+2]=a},Emboss:function(t){var e=10*this.embossStrength(),i=255*this.embossWhiteLevel(),n=this.embossDirection(),r=this.embossBlend(),o=0,a=0,s=t.data,h=t.width,d=t.height,l=4*h,c=d;switch(n){case"top-left":a=o=-1;break;case"top":o=-1,a=0;break;case"top-right":o=-1,a=1;break;case"right":o=0,a=1;break;case"bottom-right":a=o=1;break;case"bottom":o=1,a=0;break;case"bottom-left":a=-(o=1);break;case"left":o=0,a=-1;break;default:A.error("Unknown emboss direction: "+n)}do{var p=(c-1)*l,u=o;c+u<1&&(u=0),d<c+u&&(u=0);var f=(c-1+u)*h*4,g=h;do{var y=p+4*(g-1),v=a;g+v<1&&(v=0),h<g+v&&(v=0);var m,_,b,x,S=f+4*(g-1+v),w=s[y]-s[S],C=s[1+y]-s[1+S],P=s[2+y]-s[2+S],k=w,T=0<k?k:-k;T<(0<C?C:-C)&&(k=C),T<(0<P?P:-P)&&(k=P),k*=e,r?(m=s[y]+k,_=s[1+y]+k,b=s[2+y]+k,s[y]=255<m?255:m<0?0:m,s[1+y]=255<_?255:_<0?0:_,s[2+y]=255<b?255:b<0?0:b):((x=i-k)<0?x=0:255<x&&(x=255),s[y]=s[1+y]=s[2+y]=x)}while(--g)}while(--c)},Enhance:function(t){var e,i,n,r,o,a,s,h,d,l,c,p,u,f=t.data,g=f.length,y=f[0],v=y,m=f[1],_=m,b=f[2],x=b,S=this.enhance();if(0!==S){for(r=0;r<g;r+=4)(e=f[r+0])<y?y=e:v<e&&(v=e),(i=f[r+1])<m?m=i:_<i&&(_=i),(n=f[r+2])<b?b=n:x<n&&(x=n);for(v===y&&(v=255,y=0),_===m&&(_=255,m=0),x===b&&(x=255,b=0),u=0<S?(a=v+S*(255-v),s=y-S*y,d=_+S*(255-_),l=m-S*m,p=x+S*(255-x),b-S*b):(a=v+S*(v-(o=.5*(v+y))),s=y+S*(y-o),d=_+S*(_-(h=.5*(_+m))),l=m+S*(m-h),p=x+S*(x-(c=.5*(x+b))),b+S*(b-c)),r=0;r<g;r+=4)f[r+0]=hn(f[r+0],y,v,s,a),f[r+1]=hn(f[r+1],m,_,l,d),f[r+2]=hn(f[r+2],b,x,u,p)}},Grayscale:function(t){for(var e,i=t.data,n=i.length,r=0;r<n;r+=4)e=.34*i[r]+.5*i[r+1]+.16*i[r+2],i[r]=e,i[r+1]=e,i[r+2]=e},HSL:function(t){for(var e,i,n,r,o=t.data,a=o.length,s=Math.pow(2,this.saturation()),h=Math.abs(this.hue()+360)%360,d=127*this.luminance(),l=s*Math.cos(h*Math.PI/180),c=s*Math.sin(h*Math.PI/180),p=.299+.701*l+.167*c,u=.587-.587*l+.33*c,f=.114-.114*l-.497*c,g=.299-.299*l-.328*c,y=.587+.413*l+.035*c,v=.114-.114*l+.293*c,m=.299-.3*l+1.25*c,_=.587-.586*l-1.05*c,b=.114+.886*l-.2*c,x=0;x<a;x+=4)e=o[x+0],i=o[x+1],n=o[x+2],r=o[x+3],o[x+0]=p*e+u*i+f*n+d,o[x+1]=g*e+y*i+v*n+d,o[x+2]=m*e+_*i+b*n+d,o[x+3]=r},HSV:function(t){for(var e,i,n,r,o=t.data,a=o.length,s=Math.pow(2,this.value()),h=Math.pow(2,this.saturation()),d=Math.abs(this.hue()+360)%360,l=s*h*Math.cos(d*Math.PI/180),c=s*h*Math.sin(d*Math.PI/180),p=.299*s+.701*l+.167*c,u=.587*s-.587*l+.33*c,f=.114*s-.114*l-.497*c,g=.299*s-.299*l-.328*c,y=.587*s+.413*l+.035*c,v=.114*s-.114*l+.293*c,m=.299*s-.3*l+1.25*c,_=.587*s-.586*l-1.05*c,b=.114*s+.886*l-.2*c,x=0;x<a;x+=4)e=o[x+0],i=o[x+1],n=o[x+2],r=o[x+3],o[x+0]=p*e+u*i+f*n,o[x+1]=g*e+y*i+v*n,o[x+2]=m*e+_*i+b*n,o[x+3]=r},Invert:function(t){for(var e=t.data,i=e.length,n=0;n<i;n+=4)e[n]=255-e[n],e[n+1]=255-e[n+1],e[n+2]=255-e[n+2]},Kaleidoscope:function(t){var e,i,n,r,o,a,s,h,d,l=t.width,c=t.height,p=Math.round(this.kaleidoscopePower()),u=Math.round(this.kaleidoscopeAngle()),f=Math.floor(l*(u%360)/360);if(!(p<1)){var g=A.createCanvasElement();g.width=l,g.height=c;var y=g.getContext("2d").getImageData(0,0,l,c);!function(t,e,i){for(var n,r,o,a,s=t.data,h=e.data,d=t.width,l=t.height,c=i.polarCenterX||d/2,p=i.polarCenterY||l/2,u=0,f=0,g=0,y=0,v=Math.sqrt(c*c+p*p),m=d-c,_=l-p,b=Math.sqrt(m*m+_*_),v=v<b?b:v,x=l,S=d,w=360/S*Math.PI/180,C=0;C<S;C+=1)for(o=Math.sin(C*w),a=Math.cos(C*w),r=0;r<x;r+=1)m=Math.floor(c+v*r/x*a),u=s[(n=4*((_=Math.floor(p+v*r/x*o))*d+m))+0],f=s[n+1],g=s[n+2],y=s[n+3],h[(n=4*(C+r*d))+0]=u,h[n+1]=f,h[n+2]=g,h[n+3]=y}(t,y,{polarCenterX:l/2,polarCenterY:c/2});for(var v=l/Math.pow(2,p);v<=8;)v*=2,--p;var m=v=Math.ceil(v),_=0,b=m,x=1;for(l<f+v&&(_=m,b=0,x=-1),i=0;i<c;i+=1)for(e=_;e!==b;e+=x)h=4*(l*i+Math.round(e+f)%l),r=y.data[h+0],o=y.data[h+1],a=y.data[h+2],s=y.data[h+3],d=4*(l*i+e),y.data[d+0]=r,y.data[d+1]=o,y.data[d+2]=a,y.data[d+3]=s;for(i=0;i<c;i+=1)for(m=Math.floor(v),n=0;n<p;n+=1){for(e=0;e<m+1;e+=1)h=4*(l*i+e),r=y.data[h+0],o=y.data[h+1],a=y.data[h+2],s=y.data[h+3],d=4*(l*i+2*m-e-1),y.data[d+0]=r,y.data[d+1]=o,y.data[d+2]=a,y.data[d+3]=s;m*=2}!function(t,e,i){var n,r,o,a,s,h,d=t.data,l=e.data,c=t.width,p=t.height,u=i.polarCenterX||c/2,f=i.polarCenterY||p/2,g=0,y=0,v=0,m=0,_=Math.sqrt(u*u+f*f),b=c-u,x=p-f,S=Math.sqrt(b*b+x*x),_=_<S?S:_,w=p,C=c,P=i.polarRotation||0;for(b=0;b<c;b+=1)for(x=0;x<p;x+=1)r=b-u,o=x-f,a=Math.sqrt(r*r+o*o)*w/_,s=(s=(180*Math.atan2(o,r)/Math.PI+360+P)%360)*C/360,h=Math.floor(s),g=d[(n=4*(Math.floor(a)*c+h))+0],y=d[n+1],v=d[n+2],m=d[n+3],l[(n=4*(x*c+b))+0]=g,l[n+1]=y,l[n+2]=v,l[n+3]=m}(y,t,{polarRotation:0})}},Mask:function(t){var e=cn(t,this.threshold());return e&&function(t,e){for(var i=0;i<t.width*t.height;i++)t.data[4*i+3]=e[i]}(t,e=function(t,e,i){for(var n=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],r=Math.round(Math.sqrt(n.length)),o=Math.floor(r/2),a=[],s=0;s<i;s++)for(var h=0;h<e;h++){for(var d=s*e+h,l=0,c=0;c<r;c++)for(var p=0;p<r;p++){var u,f=s+c-o,g=h+p-o;0<=f&&f<i&&0<=g&&g<e&&(u=n[c*r+p],l+=t[f*e+g]*u)}a[d]=l}return a}(e=function(t,e,i){for(var n=[1,1,1,1,1,1,1,1,1],r=Math.round(Math.sqrt(n.length)),o=Math.floor(r/2),a=[],s=0;s<i;s++)for(var h=0;h<e;h++){for(var d=s*e+h,l=0,c=0;c<r;c++)for(var p=0;p<r;p++){var u,f=s+c-o,g=h+p-o;0<=f&&f<i&&0<=g&&g<e&&(u=n[c*r+p],l+=t[f*e+g]*u)}a[d]=1020<=l?255:0}return a}(e=function(t,e,i){for(var n=[1,1,1,1,0,1,1,1,1],r=Math.round(Math.sqrt(n.length)),o=Math.floor(r/2),a=[],s=0;s<i;s++)for(var h=0;h<e;h++){for(var d=s*e+h,l=0,c=0;c<r;c++)for(var p=0;p<r;p++){var u,f=s+c-o,g=h+p-o;0<=f&&f<i&&0<=g&&g<e&&(u=n[c*r+p],l+=t[f*e+g]*u)}a[d]=2040===l?255:0}return a}(e,t.width,t.height),t.width,t.height),t.width,t.height)),t},Noise:function(t){for(var e=255*this.noise(),i=t.data,n=i.length,r=e/2,o=0;o<n;o+=4)i[o+0]+=r-2*r*Math.random(),i[o+1]+=r-2*r*Math.random(),i[o+2]+=r-2*r*Math.random()},Pixelate:function(t){var e,i,n,r,o,a,s,h,d,l,c,p,u,f,g=Math.ceil(this.pixelSize()),y=t.width,v=t.height,m=Math.ceil(y/g),_=Math.ceil(v/g),b=t.data;if(g<=0)A.error("pixelSize value can not be <= 0");else for(p=0;p<m;p+=1)for(u=0;u<_;u+=1){for(d=(h=p*g)+g,c=(l=u*g)+g,f=s=a=o=r=0,e=h;e<d;e+=1)if(!(y<=e))for(i=l;i<c;i+=1)v<=i||(r+=b[(n=4*(y*i+e))+0],o+=b[n+1],a+=b[n+2],s+=b[n+3],f+=1);for(r/=f,o/=f,a/=f,s/=f,e=h;e<d;e+=1)if(!(y<=e))for(i=l;i<c;i+=1)v<=i||(b[(n=4*(y*i+e))+0]=r,b[n+1]=o,b[n+2]=a,b[n+3]=s)}},Posterize:function(t){for(var e=Math.round(254*this.levels())+1,i=t.data,n=i.length,r=255/e,o=0;o<n;o+=1)i[o]=Math.floor(i[o]/r)*r},RGB:function(t){for(var e,i=t.data,n=i.length,r=this.red(),o=this.green(),a=this.blue(),s=0;s<n;s+=4)e=(.34*i[s]+.5*i[s+1]+.16*i[s+2])/255,i[s]=e*r,i[s+1]=e*o,i[s+2]=e*a,i[s+3]=i[s+3]},RGBA:function(t){for(var e,i=t.data,n=i.length,r=this.red(),o=this.green(),a=this.blue(),s=this.alpha(),h=0;h<n;h+=4)e=1-s,i[h]=r*s+i[h]*e,i[h+1]=o*s+i[h+1]*e,i[h+2]=a*s+i[h+2]*e},Sepia:function(t){for(var e,i,n,r=t.data,o=r.length,a=0;a<o;a+=4)e=r[a+0],i=r[a+1],n=r[a+2],r[a+0]=Math.min(255,.393*e+.769*i+.189*n),r[a+1]=Math.min(255,.349*e+.686*i+.168*n),r[a+2]=Math.min(255,.272*e+.534*i+.131*n)},Solarize:function(t){var e=t.data,i=t.width,n=4*i,r=t.height;do{var o=(r-1)*n,a=i;do{var s=o+4*(a-1),h=e[s],d=e[1+s],l=e[2+s];127<h&&(h=255-h),127<d&&(d=255-d),127<l&&(l=255-l),e[s]=h,e[1+s]=d,e[2+s]=l}while(--a)}while(--r)},Threshold:function(t){for(var e=255*this.threshold(),i=t.data,n=i.length,r=0;r<n;r+=1)i[r]=i[r]<e?0:255}}})});