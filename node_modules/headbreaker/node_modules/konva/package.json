{"name": "konva", "version": "6.0.0", "author": "<PERSON>", "files": ["README.md", "konva.js", "konva.min.js", "konva.d.ts", "types", "lib"], "main": "./lib/index.js", "typings": "./types/index-types.d.ts", "scripts": {"start": "npm run watch & gulp", "lint": "gulp lint", "build": "npm run compile && gulp build", "full-build": "npm run build && npm t", "test": "node ./test/import-test.js && mocha-headless-chrome -f ./test/runner.html -a disable-web-security && npm run test:types", "test:types": "tsc -p ./test/ --noEmit", "prettier": "prettier --write \"src/**/*.js\" \"test/**/*.js\" --single-quote", "tsc": "tsc -d --declarationDir ./types --removeComments --module CommonJS || echo \"tsc faild for some file(s).\"", "rollup": "rollup -c", "clean": "rm -rf ./lib && rm -rf ./types", "compile": "npm run clean && npm run tsc && cp ./src/index-types.d.ts ./types && npm run rollup", "watch": "rollup -c -w"}, "devDependencies": {"chai": "4.2.0", "github-release-from-changelog": "^2.1.1", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-connect": "^5.7.0", "gulp-eslint": "^6.0.0", "gulp-exec": "^4.0.0", "gulp-jscpd": "0.0.8", "gulp-jsdoc3": "^3.0.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.0.0", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "gulp-util": "^3.0.8", "install": "^0.13.0", "mocha": "7.1.2", "mocha-headless-chrome": "^3.0.0", "npm": "^6.14.5", "prettier": "^2.0.5", "rollup": "^2.8.2", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.1", "rollup-plugin-typescript2": "^0.27.0", "typescript": "^3.8.3"}, "keywords": ["canvas", "animations", "graphic", "html5"], "prettier": {"singleQuote": true}, "browser": {"canvas": false, "jsdom": false}, "bugs": {"url": "https://github.com/konvajs/konva/issues"}, "homepage": "http://konvajs.org/", "readmeFilename": "README.md", "repository": {"type": "git", "url": "git://github.com/konvajs/konva.git"}, "license": "MIT", "dependencies": {}}