import { Collection } from './Util';
import { Node, NodeConfig } from './Node';
import { GetSet, IRect } from './types';
export interface ContainerConfig extends NodeConfig {
    clearBeforeDraw?: boolean;
    clipFunc?: (ctx: CanvasRenderingContext2D) => void;
    clipX?: number;
    clipY?: number;
    clipWidth?: number;
    clipHeight?: number;
}
export declare abstract class Container<ChildType extends Node> extends Node<ContainerConfig> {
    children: Collection<ChildType>;
    getChildren(filterFunc?: (item: Node) => boolean): Collection<Node<NodeConfig>>;
    hasChildren(): boolean;
    removeChildren(): this;
    destroyChildren(): this;
    abstract _validateAdd(node: Node): void;
    add(...children: ChildType[]): this;
    destroy(): this;
    find<ChildNode extends Node = Node>(selector: any): Collection<Node>;
    get(selector: any): Collection<Node<NodeConfig>>;
    findOne<ChildNode extends Node = Node>(selector: string | Function): Node;
    _generalFind<ChildNode extends Node = Node>(selector: string | Function, findOne: boolean): Collection<Node<NodeConfig>>;
    private _descendants;
    toObject(): any;
    isAncestorOf(node: Node): boolean;
    clone(obj?: any): any;
    getAllIntersections(pos: any): any[];
    _setChildrenIndices(): void;
    drawScene(can: any, top: any, caching: any): this;
    drawHit(can: any, top: any, caching: any): this;
    _drawChildren(canvas: any, drawMethod: any, top: any, caching?: any, skipBuffer?: any, skipComposition?: any): void;
    shouldDrawHit(canvas?: any): boolean;
    getClientRect(config?: {
        skipTransform?: boolean;
        skipShadow?: boolean;
        skipStroke?: boolean;
        relativeTo?: Container<Node>;
    }): IRect;
    clip: GetSet<IRect, this>;
    clipX: GetSet<number, this>;
    clipY: GetSet<number, this>;
    clipWidth: GetSet<number, this>;
    clipHeight: GetSet<number, this>;
    clipFunc: GetSet<(ctx: CanvasRenderingContext2D, shape: Container<ChildType>) => void, this>;
}
