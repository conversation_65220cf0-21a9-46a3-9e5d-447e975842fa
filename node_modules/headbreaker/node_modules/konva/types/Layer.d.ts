import { BaseLayer } from './BaseLayer';
import { HitCanvas } from './Canvas';
import { GetSet, Vector2d } from './types';
export declare class Layer extends BaseLayer {
    hitCanvas: HitCanvas;
    setSize({ width, height }: {
        width: any;
        height: any;
    }): this;
    _validateAdd(child: any): void;
    getIntersection(pos: Vector2d, selector?: string): any;
    _getIntersection(pos: any): {
        shape: any;
        antialiased?: undefined;
    } | {
        antialiased: boolean;
        shape?: undefined;
    } | {
        shape?: undefined;
        antialiased?: undefined;
    };
    drawScene(can: any, top: any): this;
    drawHit(can: any, top: any): this;
    clear(bounds?: any): this;
    enableHitGraph(): this;
    disableHitGraph(): this;
    toggleHitCanvas(): void;
    hitGraphEnabled: GetSet<boolean, this>;
}
