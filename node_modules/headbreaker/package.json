{"name": "headbreaker", "version": "3.0.0", "description": "Jigsaw puzzles library for js", "main": "src/index.js", "types": "dist/headbreaker.d.ts", "scripts": {"test": "./node_modules/mocha/bin/mocha.js", "all": "npm test && npm run build && npm run types && npm run api", "build": "webpack --config webpack.prod.js", "api": "node_modules/jsdoc-import-support/jsdoc.js src/ -d docs/api -R README.md; exit 0", "types": "node_modules/jsdoc-import-support/jsdoc.js -t node_modules/tsd-jsdoc/dist/ -d dist/ -r src/; mv dist/types.d.ts dist/headbreaker.d.ts; exit 0"}, "repository": {"type": "git", "url": "git+https://github.com/flbulgarelli/headbreaker.git"}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/flbulgarelli/headbreaker/issues"}, "homepage": "https://github.com/flbulgarelli/headbreaker#readme", "devDependencies": {"clean-webpack-plugin": "^1.0.1", "html-webpack-plugin": "^5.5.0", "jsdoc-import-support": "^3.6.4", "mocha": "^10.0.0", "tsd-jsdoc": "^2.5.0", "webpack": "^4.29.0", "webpack-cli": "^3.2.1", "webpack-dev-server": "^4.11.1", "webpack-merge": "^4.2.1"}, "optionalDependencies": {"konva": "^6.0.0"}}