!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Konva=e()}(this,(function(){"use strict";
/*
   * Konva JavaScript Framework v9.3.20
   * http://konvajs.org/
   * Licensed under the MIT
   * Date: Thu Mar 20 2025
   *
   * Original work Copyright (C) 2011 - 2013 by <PERSON> (KineticJS)
   * Modified work Copyright (C) 2014 - present by <PERSON> (Konva)
   *
   * @license
   */const t=Math.PI/180;const e="undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope?self:{},i={_global:e,version:"9.3.20",isBrowser:"undefined"!=typeof window&&("[object Window]"==={}.toString.call(window)||"[object global]"==={}.toString.call(window)),isUnminified:/param/.test(function(t){}.toString()),dblClickWindow:400,getAngle:e=>i.angleDeg?e*t:e,enableTrace:!1,pointerEventsEnabled:!0,autoDrawEnabled:!0,hitOnDragEnabled:!1,capturePointerEventsEnabled:!1,_mouseListenClick:!1,_touchListenClick:!1,_pointerListenClick:!1,_mouseInDblClickWindow:!1,_touchInDblClickWindow:!1,_pointerInDblClickWindow:!1,_mouseDblClickPointerId:null,_touchDblClickPointerId:null,_pointerDblClickPointerId:null,_fixTextRendering:!1,pixelRatio:"undefined"!=typeof window&&window.devicePixelRatio||1,dragDistance:3,angleDeg:!0,showWarnings:!0,dragButtons:[0,1],isDragging:()=>i.DD.isDragging,isTransforming(){var t;return null===(t=i.Transformer)||void 0===t?void 0:t.isTransforming()},isDragReady:()=>!!i.DD.node,releaseCanvasOnDestroy:!0,document:e.document,_injectGlobal(t){e.Konva=t}},n=t=>{i[t.prototype.getClassName()]=t};i._injectGlobal(i);class s{constructor(t=[1,0,0,1,0,0]){this.dirty=!1,this.m=t&&t.slice()||[1,0,0,1,0,0]}reset(){this.m[0]=1,this.m[1]=0,this.m[2]=0,this.m[3]=1,this.m[4]=0,this.m[5]=0}copy(){return new s(this.m)}copyInto(t){t.m[0]=this.m[0],t.m[1]=this.m[1],t.m[2]=this.m[2],t.m[3]=this.m[3],t.m[4]=this.m[4],t.m[5]=this.m[5]}point(t){const e=this.m;return{x:e[0]*t.x+e[2]*t.y+e[4],y:e[1]*t.x+e[3]*t.y+e[5]}}translate(t,e){return this.m[4]+=this.m[0]*t+this.m[2]*e,this.m[5]+=this.m[1]*t+this.m[3]*e,this}scale(t,e){return this.m[0]*=t,this.m[1]*=t,this.m[2]*=e,this.m[3]*=e,this}rotate(t){const e=Math.cos(t),i=Math.sin(t),n=this.m[0]*e+this.m[2]*i,s=this.m[1]*e+this.m[3]*i,r=this.m[0]*-i+this.m[2]*e,a=this.m[1]*-i+this.m[3]*e;return this.m[0]=n,this.m[1]=s,this.m[2]=r,this.m[3]=a,this}getTranslation(){return{x:this.m[4],y:this.m[5]}}skew(t,e){const i=this.m[0]+this.m[2]*e,n=this.m[1]+this.m[3]*e,s=this.m[2]+this.m[0]*t,r=this.m[3]+this.m[1]*t;return this.m[0]=i,this.m[1]=n,this.m[2]=s,this.m[3]=r,this}multiply(t){const e=this.m[0]*t.m[0]+this.m[2]*t.m[1],i=this.m[1]*t.m[0]+this.m[3]*t.m[1],n=this.m[0]*t.m[2]+this.m[2]*t.m[3],s=this.m[1]*t.m[2]+this.m[3]*t.m[3],r=this.m[0]*t.m[4]+this.m[2]*t.m[5]+this.m[4],a=this.m[1]*t.m[4]+this.m[3]*t.m[5]+this.m[5];return this.m[0]=e,this.m[1]=i,this.m[2]=n,this.m[3]=s,this.m[4]=r,this.m[5]=a,this}invert(){const t=1/(this.m[0]*this.m[3]-this.m[1]*this.m[2]),e=this.m[3]*t,i=-this.m[1]*t,n=-this.m[2]*t,s=this.m[0]*t,r=t*(this.m[2]*this.m[5]-this.m[3]*this.m[4]),a=t*(this.m[1]*this.m[4]-this.m[0]*this.m[5]);return this.m[0]=e,this.m[1]=i,this.m[2]=n,this.m[3]=s,this.m[4]=r,this.m[5]=a,this}getMatrix(){return this.m}decompose(){const t=this.m[0],e=this.m[1],i=this.m[2],n=this.m[3],s=t*n-e*i,r={x:this.m[4],y:this.m[5],rotation:0,scaleX:0,scaleY:0,skewX:0,skewY:0};if(0!=t||0!=e){const a=Math.sqrt(t*t+e*e);r.rotation=e>0?Math.acos(t/a):-Math.acos(t/a),r.scaleX=a,r.scaleY=s/a,r.skewX=(t*i+e*n)/s,r.skewY=0}else if(0!=i||0!=n){const a=Math.sqrt(i*i+n*n);r.rotation=Math.PI/2-(n>0?Math.acos(-i/a):-Math.acos(i/a)),r.scaleX=s/a,r.scaleY=a,r.skewX=0,r.skewY=(t*i+e*n)/s}return r.rotation=g._getRotation(r.rotation),r}}const r=Math.PI/180,a=180/Math.PI,o="Konva error: ",h={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,132,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,255,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,203],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[119,128,144],slategrey:[119,128,144],snow:[255,255,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],transparent:[255,255,255,0],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,5]},l=/rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)/;let d=[];const c="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||function(t){setTimeout(t,60)},g={_isElement:t=>!(!t||1!=t.nodeType),_isFunction:t=>!!(t&&t.constructor&&t.call&&t.apply),_isPlainObject:t=>!!t&&t.constructor===Object,_isArray:t=>"[object Array]"===Object.prototype.toString.call(t),_isNumber:t=>"[object Number]"===Object.prototype.toString.call(t)&&!isNaN(t)&&isFinite(t),_isString:t=>"[object String]"===Object.prototype.toString.call(t),_isBoolean:t=>"[object Boolean]"===Object.prototype.toString.call(t),isObject:t=>t instanceof Object,isValidSelector(t){if("string"!=typeof t)return!1;const e=t[0];return"#"===e||"."===e||e===e.toUpperCase()},_sign:t=>0===t||t>0?1:-1,requestAnimFrame(t){d.push(t),1===d.length&&c((function(){const t=d;d=[],t.forEach((function(t){t()}))}))},createCanvasElement(){const t=document.createElement("canvas");try{t.style=t.style||{}}catch(t){}return t},createImageElement:()=>document.createElement("img"),_isInDocument(t){for(;t=t.parentNode;)if(t==document)return!0;return!1},_urlToImage(t,e){const i=g.createImageElement();i.onload=function(){e(i)},i.src=t},_rgbToHex:(t,e,i)=>((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1),_hexToRgb(t){t=t.replace("#","");const e=parseInt(t,16);return{r:e>>16&255,g:e>>8&255,b:255&e}},getRandomColor(){let t=(16777215*Math.random()|0).toString(16);for(;t.length<6;)t="0"+t;return"#"+t},getRGB(t){let e;return t in h?(e=h[t],{r:e[0],g:e[1],b:e[2]}):"#"===t[0]?this._hexToRgb(t.substring(1)):"rgb("===t.substr(0,4)?(e=l.exec(t.replace(/ /g,"")),{r:parseInt(e[1],10),g:parseInt(e[2],10),b:parseInt(e[3],10)}):{r:0,g:0,b:0}},colorToRGBA:t=>(t=t||"black",g._namedColorToRBA(t)||g._hex3ColorToRGBA(t)||g._hex4ColorToRGBA(t)||g._hex6ColorToRGBA(t)||g._hex8ColorToRGBA(t)||g._rgbColorToRGBA(t)||g._rgbaColorToRGBA(t)||g._hslColorToRGBA(t)),_namedColorToRBA(t){const e=h[t.toLowerCase()];return e?{r:e[0],g:e[1],b:e[2],a:1}:null},_rgbColorToRGBA(t){if(0===t.indexOf("rgb(")){const e=(t=t.match(/rgb\(([^)]+)\)/)[1]).split(/ *, */).map(Number);return{r:e[0],g:e[1],b:e[2],a:1}}},_rgbaColorToRGBA(t){if(0===t.indexOf("rgba(")){const e=(t=t.match(/rgba\(([^)]+)\)/)[1]).split(/ *, */).map(((t,e)=>"%"===t.slice(-1)?3===e?parseInt(t)/100:parseInt(t)/100*255:Number(t)));return{r:e[0],g:e[1],b:e[2],a:e[3]}}},_hex8ColorToRGBA(t){if("#"===t[0]&&9===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:parseInt(t.slice(7,9),16)/255}},_hex6ColorToRGBA(t){if("#"===t[0]&&7===t.length)return{r:parseInt(t.slice(1,3),16),g:parseInt(t.slice(3,5),16),b:parseInt(t.slice(5,7),16),a:1}},_hex4ColorToRGBA(t){if("#"===t[0]&&5===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:parseInt(t[4]+t[4],16)/255}},_hex3ColorToRGBA(t){if("#"===t[0]&&4===t.length)return{r:parseInt(t[1]+t[1],16),g:parseInt(t[2]+t[2],16),b:parseInt(t[3]+t[3],16),a:1}},_hslColorToRGBA(t){if(/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.test(t)){const[e,...i]=/hsl\((\d+),\s*([\d.]+)%,\s*([\d.]+)%\)/g.exec(t),n=Number(i[0])/360,s=Number(i[1])/100,r=Number(i[2])/100;let a,o,h;if(0===s)return h=255*r,{r:Math.round(h),g:Math.round(h),b:Math.round(h),a:1};a=r<.5?r*(1+s):r+s-r*s;const l=2*r-a,d=[0,0,0];for(let t=0;t<3;t++)o=n+1/3*-(t-1),o<0&&o++,o>1&&o--,h=6*o<1?l+6*(a-l)*o:2*o<1?a:3*o<2?l+(a-l)*(2/3-o)*6:l,d[t]=255*h;return{r:Math.round(d[0]),g:Math.round(d[1]),b:Math.round(d[2]),a:1}}},haveIntersection:(t,e)=>!(e.x>t.x+t.width||e.x+e.width<t.x||e.y>t.y+t.height||e.y+e.height<t.y),cloneObject(t){const e={};for(const i in t)this._isPlainObject(t[i])?e[i]=this.cloneObject(t[i]):this._isArray(t[i])?e[i]=this.cloneArray(t[i]):e[i]=t[i];return e},cloneArray:t=>t.slice(0),degToRad:t=>t*r,radToDeg:t=>t*a,_degToRad:t=>(g.warn("Util._degToRad is removed. Please use public Util.degToRad instead."),g.degToRad(t)),_radToDeg:t=>(g.warn("Util._radToDeg is removed. Please use public Util.radToDeg instead."),g.radToDeg(t)),_getRotation:t=>i.angleDeg?g.radToDeg(t):t,_capitalize:t=>t.charAt(0).toUpperCase()+t.slice(1),throw(t){throw new Error(o+t)},error(t){console.error(o+t)},warn(t){i.showWarnings&&console.warn("Konva warning: "+t)},each(t,e){for(const i in t)e(i,t[i])},_inRange:(t,e,i)=>e<=t&&t<i,_getProjectionToSegment(t,e,i,n,s,r){let a,o,h;const l=(t-i)*(t-i)+(e-n)*(e-n);if(0==l)a=t,o=e,h=(s-i)*(s-i)+(r-n)*(r-n);else{const d=((s-t)*(i-t)+(r-e)*(n-e))/l;d<0?(a=t,o=e,h=(t-s)*(t-s)+(e-r)*(e-r)):d>1?(a=i,o=n,h=(i-s)*(i-s)+(n-r)*(n-r)):(a=t+d*(i-t),o=e+d*(n-e),h=(a-s)*(a-s)+(o-r)*(o-r))}return[a,o,h]},_getProjectionToLine(t,e,i){const n=g.cloneObject(t);let s=Number.MAX_VALUE;return e.forEach((function(r,a){if(!i&&a===e.length-1)return;const o=e[(a+1)%e.length],h=g._getProjectionToSegment(r.x,r.y,o.x,o.y,t.x,t.y),l=h[0],d=h[1],c=h[2];c<s&&(n.x=l,n.y=d,s=c)})),n},_prepareArrayForTween(t,e,i){const n=[],s=[];if(t.length>e.length){const i=e;e=t,t=i}for(let e=0;e<t.length;e+=2)n.push({x:t[e],y:t[e+1]});for(let t=0;t<e.length;t+=2)s.push({x:e[t],y:e[t+1]});const r=[];return s.forEach((function(t){const e=g._getProjectionToLine(t,n,i);r.push(e.x),r.push(e.y)})),r},_prepareToStringify(t){let e;t.visitedByCircularReferenceRemoval=!0;for(const i in t)if(t.hasOwnProperty(i)&&t[i]&&"object"==typeof t[i])if(e=Object.getOwnPropertyDescriptor(t,i),t[i].visitedByCircularReferenceRemoval||g._isElement(t[i])){if(!e.configurable)return null;delete t[i]}else if(null===g._prepareToStringify(t[i])){if(!e.configurable)return null;delete t[i]}return delete t.visitedByCircularReferenceRemoval,t},_assign(t,e){for(const i in e)t[i]=e[i];return t},_getFirstPointerId:t=>t.touches?t.changedTouches[0].identifier:t.pointerId||999,releaseCanvas(...t){i.releaseCanvasOnDestroy&&t.forEach((t=>{t.width=0,t.height=0}))},drawRoundedRectPath(t,e,i,n){let s=0,r=0,a=0,o=0;"number"==typeof n?s=r=a=o=Math.min(n,e/2,i/2):(s=Math.min(n[0]||0,e/2,i/2),r=Math.min(n[1]||0,e/2,i/2),o=Math.min(n[2]||0,e/2,i/2),a=Math.min(n[3]||0,e/2,i/2)),t.moveTo(s,0),t.lineTo(e-r,0),t.arc(e-r,r,r,3*Math.PI/2,0,!1),t.lineTo(e,i-o),t.arc(e-o,i-o,o,0,Math.PI/2,!1),t.lineTo(a,i),t.arc(a,i-a,a,Math.PI/2,Math.PI,!1),t.lineTo(0,s),t.arc(s,s,s,Math.PI,3*Math.PI/2,!1)}};function u(t){return g._isString(t)?'"'+t+'"':"[object Number]"===Object.prototype.toString.call(t)||g._isBoolean(t)?t:Object.prototype.toString.call(t)}function f(t){return t>255?255:t<0?0:Math.round(t)}function p(){if(i.isUnminified)return function(t,e){return g._isNumber(t)||g.warn(u(t)+' is a not valid value for "'+e+'" attribute. The value should be a number.'),t}}function m(t){if(i.isUnminified)return function(e,i){let n=g._isNumber(e),s=g._isArray(e)&&e.length==t;return n||s||g.warn(u(e)+' is a not valid value for "'+i+'" attribute. The value should be a number or Array<number>('+t+")"),e}}function _(){if(i.isUnminified)return function(t,e){return g._isNumber(t)||"auto"===t||g.warn(u(t)+' is a not valid value for "'+e+'" attribute. The value should be a number or "auto".'),t}}function y(){if(i.isUnminified)return function(t,e){return g._isString(t)||g.warn(u(t)+' is a not valid value for "'+e+'" attribute. The value should be a string.'),t}}function v(){if(i.isUnminified)return function(t,e){const i=g._isString(t),n="[object CanvasGradient]"===Object.prototype.toString.call(t)||t&&t.addColorStop;return i||n||g.warn(u(t)+' is a not valid value for "'+e+'" attribute. The value should be a string or a native gradient.'),t}}function x(){if(i.isUnminified)return function(t,e){return!0===t||!1===t||g.warn(u(t)+' is a not valid value for "'+e+'" attribute. The value should be a boolean.'),t}}const b="get",S="set",w={addGetterSetter(t,e,i,n,s){w.addGetter(t,e,i),w.addSetter(t,e,n,s),w.addOverloadedGetterSetter(t,e)},addGetter(t,e,i){var n=b+g._capitalize(e);t.prototype[n]=t.prototype[n]||function(){const t=this.attrs[e];return void 0===t?i:t}},addSetter(t,e,i,n){var s=S+g._capitalize(e);t.prototype[s]||w.overWriteSetter(t,e,i,n)},overWriteSetter(t,e,i,n){var s=S+g._capitalize(e);t.prototype[s]=function(t){return i&&null!=t&&(t=i.call(this,t,e)),this._setAttr(e,t),n&&n.call(this),this}},addComponentsGetterSetter(t,e,n,s,r){const a=n.length,o=g._capitalize,h=b+o(e),l=S+o(e);t.prototype[h]=function(){const t={};for(let i=0;i<a;i++){const s=n[i];t[s]=this.getAttr(e+o(s))}return t};const d=function(t){if(i.isUnminified)return function(e,i){return null==e||g.isObject(e)||g.warn(u(e)+' is a not valid value for "'+i+'" attribute. The value should be an object with properties '+t),e}}(n);t.prototype[l]=function(t){const i=this.attrs[e];s&&(t=s.call(this,t,e)),d&&d.call(this,t,e);for(const i in t)t.hasOwnProperty(i)&&this._setAttr(e+o(i),t[i]);return t||n.forEach((t=>{this._setAttr(e+o(t),void 0)})),this._fireChangeEvent(e,i,t),r&&r.call(this),this},w.addOverloadedGetterSetter(t,e)},addOverloadedGetterSetter(t,e){var i=g._capitalize(e),n=S+i,s=b+i;t.prototype[e]=function(){return arguments.length?(this[n](arguments[0]),this):this[s]()}},addDeprecatedGetterSetter(t,e,i,n){g.error("Adding deprecated "+e);const s=b+g._capitalize(e),r=e+" property is deprecated and will be removed soon. Look at Konva change log for more information.";t.prototype[s]=function(){g.error(r);const t=this.attrs[e];return void 0===t?i:t},w.addSetter(t,e,n,(function(){g.error(r)})),w.addOverloadedGetterSetter(t,e)},backCompat(t,e){g.each(e,(function(e,i){const n=t.prototype[i],s=b+g._capitalize(e),r=S+g._capitalize(e);function a(){n.apply(this,arguments),g.error('"'+e+'" method is deprecated and will be removed soon. Use ""'+i+'" instead.')}t.prototype[e]=a,t.prototype[s]=a,t.prototype[r]=a}))},afterSetFilter(){this._filterUpToDate=!1}};const C=["arc","arcTo","beginPath","bezierCurveTo","clearRect","clip","closePath","createLinearGradient","createPattern","createRadialGradient","drawImage","ellipse","fill","fillText","getImageData","createImageData","lineTo","moveTo","putImageData","quadraticCurveTo","rect","roundRect","restore","rotate","save","scale","setLineDash","setTransform","stroke","strokeText","transform","translate"];class P{constructor(t){this.canvas=t,i.enableTrace&&(this.traceArr=[],this._enableTrace())}fillShape(t){t.fillEnabled()&&this._fill(t)}_fill(t){}strokeShape(t){t.hasStroke()&&this._stroke(t)}_stroke(t){}fillStrokeShape(t){t.attrs.fillAfterStrokeEnabled?(this.strokeShape(t),this.fillShape(t)):(this.fillShape(t),this.strokeShape(t))}getTrace(t,e){let i,n,s,r,a=this.traceArr,o=a.length,h="";for(i=0;i<o;i++)n=a[i],s=n.method,s?(r=n.args,h+=s,t?h+="()":g._isArray(r[0])?h+="(["+r.join(",")+"])":(e&&(r=r.map((t=>"number"==typeof t?Math.floor(t):t))),h+="("+r.join(",")+")")):(h+=n.property,t||(h+="="+n.val)),h+=";";return h}clearTrace(){this.traceArr=[]}_trace(t){let e,i=this.traceArr;i.push(t),e=i.length,e>=100&&i.shift()}reset(){const t=this.getCanvas().getPixelRatio();this.setTransform(1*t,0,0,1*t,0,0)}getCanvas(){return this.canvas}clear(t){const e=this.getCanvas();t?this.clearRect(t.x||0,t.y||0,t.width||0,t.height||0):this.clearRect(0,0,e.getWidth()/e.pixelRatio,e.getHeight()/e.pixelRatio)}_applyLineCap(t){const e=t.attrs.lineCap;e&&this.setAttr("lineCap",e)}_applyOpacity(t){const e=t.getAbsoluteOpacity();1!==e&&this.setAttr("globalAlpha",e)}_applyLineJoin(t){const e=t.attrs.lineJoin;e&&this.setAttr("lineJoin",e)}setAttr(t,e){this._context[t]=e}arc(t,e,i,n,s,r){this._context.arc(t,e,i,n,s,r)}arcTo(t,e,i,n,s){this._context.arcTo(t,e,i,n,s)}beginPath(){this._context.beginPath()}bezierCurveTo(t,e,i,n,s,r){this._context.bezierCurveTo(t,e,i,n,s,r)}clearRect(t,e,i,n){this._context.clearRect(t,e,i,n)}clip(...t){this._context.clip.apply(this._context,t)}closePath(){this._context.closePath()}createImageData(t,e){const i=arguments;return 2===i.length?this._context.createImageData(t,e):1===i.length?this._context.createImageData(t):void 0}createLinearGradient(t,e,i,n){return this._context.createLinearGradient(t,e,i,n)}createPattern(t,e){return this._context.createPattern(t,e)}createRadialGradient(t,e,i,n,s,r){return this._context.createRadialGradient(t,e,i,n,s,r)}drawImage(t,e,i,n,s,r,a,o,h){const l=arguments,d=this._context;3===l.length?d.drawImage(t,e,i):5===l.length?d.drawImage(t,e,i,n,s):9===l.length&&d.drawImage(t,e,i,n,s,r,a,o,h)}ellipse(t,e,i,n,s,r,a,o){this._context.ellipse(t,e,i,n,s,r,a,o)}isPointInPath(t,e,i,n){return i?this._context.isPointInPath(i,t,e,n):this._context.isPointInPath(t,e,n)}fill(...t){this._context.fill.apply(this._context,t)}fillRect(t,e,i,n){this._context.fillRect(t,e,i,n)}strokeRect(t,e,i,n){this._context.strokeRect(t,e,i,n)}fillText(t,e,i,n){n?this._context.fillText(t,e,i,n):this._context.fillText(t,e,i)}measureText(t){return this._context.measureText(t)}getImageData(t,e,i,n){return this._context.getImageData(t,e,i,n)}lineTo(t,e){this._context.lineTo(t,e)}moveTo(t,e){this._context.moveTo(t,e)}rect(t,e,i,n){this._context.rect(t,e,i,n)}roundRect(t,e,i,n,s){this._context.roundRect(t,e,i,n,s)}putImageData(t,e,i){this._context.putImageData(t,e,i)}quadraticCurveTo(t,e,i,n){this._context.quadraticCurveTo(t,e,i,n)}restore(){this._context.restore()}rotate(t){this._context.rotate(t)}save(){this._context.save()}scale(t,e){this._context.scale(t,e)}setLineDash(t){this._context.setLineDash?this._context.setLineDash(t):"mozDash"in this._context?this._context.mozDash=t:"webkitLineDash"in this._context&&(this._context.webkitLineDash=t)}getLineDash(){return this._context.getLineDash()}setTransform(t,e,i,n,s,r){this._context.setTransform(t,e,i,n,s,r)}stroke(t){t?this._context.stroke(t):this._context.stroke()}strokeText(t,e,i,n){this._context.strokeText(t,e,i,n)}transform(t,e,i,n,s,r){this._context.transform(t,e,i,n,s,r)}translate(t,e){this._context.translate(t,e)}_enableTrace(){let t,e,i=this,n=C.length,s=this.setAttr;const r=function(t){let n,s=i[t];i[t]=function(){return e=function(t){const e=[],i=t.length,n=g;for(let s=0;s<i;s++){let i=t[s];n._isNumber(i)?i=Math.round(1e3*i)/1e3:n._isString(i)||(i+=""),e.push(i)}return e}(Array.prototype.slice.call(arguments,0)),n=s.apply(i,arguments),i._trace({method:t,args:e}),n}};for(t=0;t<n;t++)r(C[t]);i.setAttr=function(){s.apply(i,arguments);const t=arguments[0];let e=arguments[1];"shadowOffsetX"!==t&&"shadowOffsetY"!==t&&"shadowBlur"!==t||(e/=this.canvas.getPixelRatio()),i._trace({property:t,val:e})}}_applyGlobalCompositeOperation(t){const e=t.attrs.globalCompositeOperation;!e||"source-over"===e||this.setAttr("globalCompositeOperation",e)}}["fillStyle","strokeStyle","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","letterSpacing","lineCap","lineDashOffset","lineJoin","lineWidth","miterLimit","direction","font","textAlign","textBaseline","globalAlpha","globalCompositeOperation","imageSmoothingEnabled"].forEach((function(t){Object.defineProperty(P.prototype,t,{get(){return this._context[t]},set(e){this._context[t]=e}})}));class k extends P{constructor(t,{willReadFrequently:e=!1}={}){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:e})}_fillColor(t){const e=t.fill();this.setAttr("fillStyle",e),t._fillFunc(this)}_fillPattern(t){this.setAttr("fillStyle",t._getFillPattern()),t._fillFunc(this)}_fillLinearGradient(t){const e=t._getLinearGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fillRadialGradient(t){const e=t._getRadialGradient();e&&(this.setAttr("fillStyle",e),t._fillFunc(this))}_fill(t){const e=t.fill(),i=t.getFillPriority();if(e&&"color"===i)return void this._fillColor(t);const n=t.getFillPatternImage();if(n&&"pattern"===i)return void this._fillPattern(t);const s=t.getFillLinearGradientColorStops();if(s&&"linear-gradient"===i)return void this._fillLinearGradient(t);const r=t.getFillRadialGradientColorStops();r&&"radial-gradient"===i?this._fillRadialGradient(t):e?this._fillColor(t):n?this._fillPattern(t):s?this._fillLinearGradient(t):r&&this._fillRadialGradient(t)}_strokeLinearGradient(t){const e=t.getStrokeLinearGradientStartPoint(),i=t.getStrokeLinearGradientEndPoint(),n=t.getStrokeLinearGradientColorStops(),s=this.createLinearGradient(e.x,e.y,i.x,i.y);if(n){for(let t=0;t<n.length;t+=2)s.addColorStop(n[t],n[t+1]);this.setAttr("strokeStyle",s)}}_stroke(t){const e=t.dash(),i=t.getStrokeScaleEnabled();if(t.hasStroke()){if(!i){this.save();const t=this.getCanvas().getPixelRatio();this.setTransform(t,0,0,t,0,0)}this._applyLineCap(t),e&&t.dashEnabled()&&(this.setLineDash(e),this.setAttr("lineDashOffset",t.dashOffset())),this.setAttr("lineWidth",t.strokeWidth()),t.getShadowForStrokeEnabled()||this.setAttr("shadowColor","rgba(0,0,0,0)");t.getStrokeLinearGradientColorStops()?this._strokeLinearGradient(t):this.setAttr("strokeStyle",t.stroke()),t._strokeFunc(this),i||this.restore()}}_applyShadow(t){var e,i,n;const s=null!==(e=t.getShadowRGBA())&&void 0!==e?e:"black",r=null!==(i=t.getShadowBlur())&&void 0!==i?i:5,a=null!==(n=t.getShadowOffset())&&void 0!==n?n:{x:0,y:0},o=t.getAbsoluteScale(),h=this.canvas.getPixelRatio(),l=o.x*h,d=o.y*h;this.setAttr("shadowColor",s),this.setAttr("shadowBlur",r*Math.min(Math.abs(l),Math.abs(d))),this.setAttr("shadowOffsetX",a.x*l),this.setAttr("shadowOffsetY",a.y*d)}}class A extends P{constructor(t){super(t),this._context=t._canvas.getContext("2d",{willReadFrequently:!0})}_fill(t){this.save(),this.setAttr("fillStyle",t.colorKey),t._fillFuncHit(this),this.restore()}strokeShape(t){t.hasHitStroke()&&this._stroke(t)}_stroke(t){if(t.hasHitStroke()){const e=t.getStrokeScaleEnabled();if(!e){this.save();const t=this.getCanvas().getPixelRatio();this.setTransform(t,0,0,t,0,0)}this._applyLineCap(t);const i=t.hitStrokeWidth(),n="auto"===i?t.strokeWidth():i;this.setAttr("lineWidth",n),this.setAttr("strokeStyle",t.colorKey),t._strokeFuncHit(this),e||this.restore()}}}let T;class M{constructor(t){this.pixelRatio=1,this.width=0,this.height=0,this.isCache=!1;const e=(t||{}).pixelRatio||i.pixelRatio||function(){if(T)return T;const t=g.createCanvasElement(),e=t.getContext("2d");return T=(i._global.devicePixelRatio||1)/(e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1),g.releaseCanvas(t),T}();this.pixelRatio=e,this._canvas=g.createCanvasElement(),this._canvas.style.padding="0",this._canvas.style.margin="0",this._canvas.style.border="0",this._canvas.style.background="transparent",this._canvas.style.position="absolute",this._canvas.style.top="0",this._canvas.style.left="0"}getContext(){return this.context}getPixelRatio(){return this.pixelRatio}setPixelRatio(t){const e=this.pixelRatio;this.pixelRatio=t,this.setSize(this.getWidth()/e,this.getHeight()/e)}setWidth(t){this.width=this._canvas.width=t*this.pixelRatio,this._canvas.style.width=t+"px";const e=this.pixelRatio;this.getContext()._context.scale(e,e)}setHeight(t){this.height=this._canvas.height=t*this.pixelRatio,this._canvas.style.height=t+"px";const e=this.pixelRatio;this.getContext()._context.scale(e,e)}getWidth(){return this.width}getHeight(){return this.height}setSize(t,e){this.setWidth(t||0),this.setHeight(e||0)}toDataURL(t,e){try{return this._canvas.toDataURL(t,e)}catch(t){try{return this._canvas.toDataURL()}catch(t){return g.error("Unable to get data URL. "+t.message+" For more info read https://konvajs.org/docs/posts/Tainted_Canvas.html."),""}}}}class G extends M{constructor(t={width:0,height:0,willReadFrequently:!1}){super(t),this.context=new k(this,{willReadFrequently:t.willReadFrequently}),this.setSize(t.width,t.height)}}class R extends M{constructor(t={width:0,height:0}){super(t),this.hitCanvas=!0,this.context=new A(this),this.setSize(t.width,t.height)}}const E={get isDragging(){let t=!1;return E._dragElements.forEach((e=>{"dragging"===e.dragStatus&&(t=!0)})),t},justDragged:!1,get node(){let t;return E._dragElements.forEach((e=>{t=e.node})),t},_dragElements:new Map,_drag(t){const e=[];E._dragElements.forEach(((i,n)=>{const{node:s}=i,r=s.getStage();r.setPointersPositions(t),void 0===i.pointerId&&(i.pointerId=g._getFirstPointerId(t));const a=r._changedPointerPositions.find((t=>t.id===i.pointerId));if(a){if("dragging"!==i.dragStatus){const e=s.dragDistance();if(Math.max(Math.abs(a.x-i.startPointerPos.x),Math.abs(a.y-i.startPointerPos.y))<e)return;if(s.startDrag({evt:t}),!s.isDragging())return}s._setDragPosition(t,i),e.push(s)}})),e.forEach((e=>{e.fire("dragmove",{type:"dragmove",target:e,evt:t},!0)}))},_endDragBefore(t){const e=[];E._dragElements.forEach((n=>{const{node:s}=n,r=s.getStage();t&&r.setPointersPositions(t);if(!r._changedPointerPositions.find((t=>t.id===n.pointerId)))return;"dragging"!==n.dragStatus&&"stopped"!==n.dragStatus||(E.justDragged=!0,i._mouseListenClick=!1,i._touchListenClick=!1,i._pointerListenClick=!1,n.dragStatus="stopped");const a=n.node.getLayer()||n.node instanceof i.Stage&&n.node;a&&-1===e.indexOf(a)&&e.push(a)})),e.forEach((t=>{t.draw()}))},_endDragAfter(t){E._dragElements.forEach(((e,i)=>{"stopped"===e.dragStatus&&e.node.fire("dragend",{type:"dragend",target:e.node,evt:t},!0),"dragging"!==e.dragStatus&&E._dragElements.delete(i)}))}};i.isBrowser&&(window.addEventListener("mouseup",E._endDragBefore,!0),window.addEventListener("touchend",E._endDragBefore,!0),window.addEventListener("touchcancel",E._endDragBefore,!0),window.addEventListener("mousemove",E._drag),window.addEventListener("touchmove",E._drag),window.addEventListener("mouseup",E._endDragAfter,!1),window.addEventListener("touchend",E._endDragAfter,!1),window.addEventListener("touchcancel",E._endDragAfter,!1));const D="absoluteOpacity",L="allEventListeners",I="absoluteTransform",O="absoluteScale",F="canvas",B="listening",N="mouseenter",H="mouseleave",z="Shape",W=" ",Y="stage",X="transform",j="visible",q=["xChange.konva","yChange.konva","scaleXChange.konva","scaleYChange.konva","skewXChange.konva","skewYChange.konva","rotationChange.konva","offsetXChange.konva","offsetYChange.konva","transformsEnabledChange.konva"].join(W);let U=1;class V{constructor(t){this._id=U++,this.eventListeners={},this.attrs={},this.index=0,this._allEventListeners=null,this.parent=null,this._cache=new Map,this._attachedDepsListeners=new Map,this._lastPos=null,this._batchingTransformChange=!1,this._needClearTransformCache=!1,this._filterUpToDate=!1,this._isUnderCache=!1,this._dragEventId=null,this._shouldFireChangeEvents=!1,this.setAttrs(t),this._shouldFireChangeEvents=!0}hasChildren(){return!1}_clearCache(t){t!==X&&t!==I||!this._cache.get(t)?t?this._cache.delete(t):this._cache.clear():this._cache.get(t).dirty=!0}_getCache(t,e){let i=this._cache.get(t);return(void 0===i||(t===X||t===I)&&!0===i.dirty)&&(i=e.call(this),this._cache.set(t,i)),i}_calculate(t,e,i){if(!this._attachedDepsListeners.get(t)){const i=e.map((t=>t+"Change.konva")).join(W);this.on(i,(()=>{this._clearCache(t)})),this._attachedDepsListeners.set(t,!0)}return this._getCache(t,i)}_getCanvasCache(){return this._cache.get(F)}_clearSelfAndDescendantCache(t){this._clearCache(t),t===I&&this.fire("absoluteTransformChange")}clearCache(){if(this._cache.has(F)){const{scene:t,filter:e,hit:i}=this._cache.get(F);g.releaseCanvas(t,e,i),this._cache.delete(F)}return this._clearSelfAndDescendantCache(),this._requestDraw(),this}cache(t){const e=t||{};let i={};void 0!==e.x&&void 0!==e.y&&void 0!==e.width&&void 0!==e.height||(i=this.getClientRect({skipTransform:!0,relativeTo:this.getParent()||void 0}));let n=Math.ceil(e.width||i.width),s=Math.ceil(e.height||i.height),r=e.pixelRatio,a=void 0===e.x?Math.floor(i.x):e.x,o=void 0===e.y?Math.floor(i.y):e.y,h=e.offset||0,l=e.drawBorder||!1,d=e.hitCanvasPixelRatio||1;if(!n||!s)return void g.error("Can not cache the node. Width or height of the node equals 0. Caching is skipped.");n+=2*h+(Math.abs(Math.round(i.x)-a)>.5?1:0),s+=2*h+(Math.abs(Math.round(i.y)-o)>.5?1:0),a-=h,o-=h;const c=new G({pixelRatio:r,width:n,height:s}),u=new G({pixelRatio:r,width:0,height:0,willReadFrequently:!0}),f=new R({pixelRatio:d,width:n,height:s}),p=c.getContext(),m=f.getContext();return f.isCache=!0,c.isCache=!0,this._cache.delete(F),this._filterUpToDate=!1,!1===e.imageSmoothingEnabled&&(c.getContext()._context.imageSmoothingEnabled=!1,u.getContext()._context.imageSmoothingEnabled=!1),p.save(),m.save(),p.translate(-a,-o),m.translate(-a,-o),this._isUnderCache=!0,this._clearSelfAndDescendantCache(D),this._clearSelfAndDescendantCache(O),this.drawScene(c,this),this.drawHit(f,this),this._isUnderCache=!1,p.restore(),m.restore(),l&&(p.save(),p.beginPath(),p.rect(0,0,n,s),p.closePath(),p.setAttr("strokeStyle","red"),p.setAttr("lineWidth",5),p.stroke(),p.restore()),this._cache.set(F,{scene:c,filter:u,hit:f,x:a,y:o}),this._requestDraw(),this}isCached(){return this._cache.has(F)}getClientRect(t){throw new Error('abstract "getClientRect" method call')}_transformedRect(t,e){const i=[{x:t.x,y:t.y},{x:t.x+t.width,y:t.y},{x:t.x+t.width,y:t.y+t.height},{x:t.x,y:t.y+t.height}];let n=1/0,s=1/0,r=-1/0,a=-1/0;const o=this.getAbsoluteTransform(e);return i.forEach((function(t){const e=o.point(t);void 0===n&&(n=r=e.x,s=a=e.y),n=Math.min(n,e.x),s=Math.min(s,e.y),r=Math.max(r,e.x),a=Math.max(a,e.y)})),{x:n,y:s,width:r-n,height:a-s}}_drawCachedSceneCanvas(t){t.save(),t._applyOpacity(this),t._applyGlobalCompositeOperation(this);const e=this._getCanvasCache();t.translate(e.x,e.y);const i=this._getCachedSceneCanvas(),n=i.pixelRatio;t.drawImage(i._canvas,0,0,i.width/n,i.height/n),t.restore()}_drawCachedHitCanvas(t){const e=this._getCanvasCache(),i=e.hit;t.save(),t.translate(e.x,e.y),t.drawImage(i._canvas,0,0,i.width/i.pixelRatio,i.height/i.pixelRatio),t.restore()}_getCachedSceneCanvas(){let t,e,i,n,s=this.filters(),r=this._getCanvasCache(),a=r.scene,o=r.filter,h=o.getContext();if(s){if(!this._filterUpToDate){const r=a.pixelRatio;o.setSize(a.width/a.pixelRatio,a.height/a.pixelRatio);try{for(t=s.length,h.clear(),h.drawImage(a._canvas,0,0,a.getWidth()/r,a.getHeight()/r),e=h.getImageData(0,0,o.getWidth(),o.getHeight()),i=0;i<t;i++)n=s[i],"function"==typeof n?(n.call(this,e),h.putImageData(e,0,0)):g.error("Filter should be type of function, but got "+typeof n+" instead. Please check correct filters")}catch(t){g.error("Unable to apply filter. "+t.message+" This post my help you https://konvajs.org/docs/posts/Tainted_Canvas.html.")}this._filterUpToDate=!0}return o}return a}on(t,e){if(this._cache&&this._cache.delete(L),3===arguments.length)return this._delegate.apply(this,arguments);let i,n,s,r,a,o=t.split(W),h=o.length;for(i=0;i<h;i++)n=o[i],s=n.split("."),r=s[0],a=s[1]||"",this.eventListeners[r]||(this.eventListeners[r]=[]),this.eventListeners[r].push({name:a,handler:e});return this}off(t,e){let i,n,s,r,a,o,h=(t||"").split(W),l=h.length;if(this._cache&&this._cache.delete(L),!t)for(n in this.eventListeners)this._off(n);for(i=0;i<l;i++)if(s=h[i],r=s.split("."),a=r[0],o=r[1],a)this.eventListeners[a]&&this._off(a,o,e);else for(n in this.eventListeners)this._off(n,o,e);return this}dispatchEvent(t){const e={target:this,type:t.type,evt:t};return this.fire(t.type,e),this}addEventListener(t,e){return this.on(t,(function(t){e.call(this,t.evt)})),this}removeEventListener(t){return this.off(t),this}_delegate(t,e,i){const n=this;this.on(t,(function(t){const s=t.target.findAncestors(e,!0,n);for(let e=0;e<s.length;e++)(t=g.cloneObject(t)).currentTarget=s[e],i.call(s[e],t)}))}remove(){return this.isDragging()&&this.stopDrag(),E._dragElements.delete(this._id),this._remove(),this}_clearCaches(){this._clearSelfAndDescendantCache(I),this._clearSelfAndDescendantCache(D),this._clearSelfAndDescendantCache(O),this._clearSelfAndDescendantCache(Y),this._clearSelfAndDescendantCache(j),this._clearSelfAndDescendantCache(B)}_remove(){this._clearCaches();const t=this.getParent();t&&t.children&&(t.children.splice(this.index,1),t._setChildrenIndices(),this.parent=null)}destroy(){return this.remove(),this.clearCache(),this}getAttr(t){const e="get"+g._capitalize(t);return g._isFunction(this[e])?this[e]():this.attrs[t]}getAncestors(){let t=this.getParent(),e=[];for(;t;)e.push(t),t=t.getParent();return e}getAttrs(){return this.attrs||{}}setAttrs(t){return this._batchTransformChanges((()=>{let e,i;if(!t)return this;for(e in t)"children"!==e&&(i="set"+g._capitalize(e),g._isFunction(this[i])?this[i](t[e]):this._setAttr(e,t[e]))})),this}isListening(){return this._getCache(B,this._isListening)}_isListening(t){if(!this.listening())return!1;const e=this.getParent();return!e||e===t||this===t||e._isListening(t)}isVisible(){return this._getCache(j,this._isVisible)}_isVisible(t){if(!this.visible())return!1;const e=this.getParent();return!e||e===t||this===t||e._isVisible(t)}shouldDrawHit(t,e=!1){if(t)return this._isVisible(t)&&this._isListening(t);const n=this.getLayer();let s=!1;E._dragElements.forEach((t=>{"dragging"===t.dragStatus&&("Stage"===t.node.nodeType||t.node.getLayer()===n)&&(s=!0)}));const r=!e&&!i.hitOnDragEnabled&&(s||i.isTransforming());return this.isListening()&&this.isVisible()&&!r}show(){return this.visible(!0),this}hide(){return this.visible(!1),this}getZIndex(){return this.index||0}getAbsoluteZIndex(){let t,e,i,n,s=this.getDepth(),r=this,a=0;const o=this.getStage();return"Stage"!==r.nodeType&&o&&function o(h){for(t=[],e=h.length,i=0;i<e;i++)n=h[i],a++,n.nodeType!==z&&(t=t.concat(n.getChildren().slice())),n._id===r._id&&(i=e);t.length>0&&t[0].getDepth()<=s&&o(t)}(o.getChildren()),a}getDepth(){let t=0,e=this.parent;for(;e;)t++,e=e.parent;return t}_batchTransformChanges(t){this._batchingTransformChange=!0,t(),this._batchingTransformChange=!1,this._needClearTransformCache&&(this._clearCache(X),this._clearSelfAndDescendantCache(I)),this._needClearTransformCache=!1}setPosition(t){return this._batchTransformChanges((()=>{this.x(t.x),this.y(t.y)})),this}getPosition(){return{x:this.x(),y:this.y()}}getRelativePointerPosition(){const t=this.getStage();if(!t)return null;const e=t.getPointerPosition();if(!e)return null;const i=this.getAbsoluteTransform().copy();return i.invert(),i.point(e)}getAbsolutePosition(t){let e=!1,i=this.parent;for(;i;){if(i.isCached()){e=!0;break}i=i.parent}e&&!t&&(t=!0);const n=this.getAbsoluteTransform(t).getMatrix(),r=new s,a=this.offset();return r.m=n.slice(),r.translate(a.x,a.y),r.getTranslation()}setAbsolutePosition(t){const{x:e,y:i,...n}=this._clearTransform();this.attrs.x=e,this.attrs.y=i,this._clearCache(X);const s=this._getAbsoluteTransform().copy();return s.invert(),s.translate(t.x,t.y),t={x:this.attrs.x+s.getTranslation().x,y:this.attrs.y+s.getTranslation().y},this._setTransform(n),this.setPosition({x:t.x,y:t.y}),this._clearCache(X),this._clearSelfAndDescendantCache(I),this}_setTransform(t){let e;for(e in t)this.attrs[e]=t[e]}_clearTransform(){const t={x:this.x(),y:this.y(),rotation:this.rotation(),scaleX:this.scaleX(),scaleY:this.scaleY(),offsetX:this.offsetX(),offsetY:this.offsetY(),skewX:this.skewX(),skewY:this.skewY()};return this.attrs.x=0,this.attrs.y=0,this.attrs.rotation=0,this.attrs.scaleX=1,this.attrs.scaleY=1,this.attrs.offsetX=0,this.attrs.offsetY=0,this.attrs.skewX=0,this.attrs.skewY=0,t}move(t){let e=t.x,i=t.y,n=this.x(),s=this.y();return void 0!==e&&(n+=e),void 0!==i&&(s+=i),this.setPosition({x:n,y:s}),this}_eachAncestorReverse(t,e){let i,n,s=[],r=this.getParent();if(!e||e._id!==this._id){for(s.unshift(this);r&&(!e||r._id!==e._id);)s.unshift(r),r=r.parent;for(i=s.length,n=0;n<i;n++)t(s[n])}}rotate(t){return this.rotation(this.rotation()+t),this}moveToTop(){if(!this.parent)return g.warn("Node has no parent. moveToTop function is ignored."),!1;const t=this.index;return t<this.parent.getChildren().length-1&&(this.parent.children.splice(t,1),this.parent.children.push(this),this.parent._setChildrenIndices(),!0)}moveUp(){if(!this.parent)return g.warn("Node has no parent. moveUp function is ignored."),!1;const t=this.index;return t<this.parent.getChildren().length-1&&(this.parent.children.splice(t,1),this.parent.children.splice(t+1,0,this),this.parent._setChildrenIndices(),!0)}moveDown(){if(!this.parent)return g.warn("Node has no parent. moveDown function is ignored."),!1;const t=this.index;return t>0&&(this.parent.children.splice(t,1),this.parent.children.splice(t-1,0,this),this.parent._setChildrenIndices(),!0)}moveToBottom(){if(!this.parent)return g.warn("Node has no parent. moveToBottom function is ignored."),!1;const t=this.index;return t>0&&(this.parent.children.splice(t,1),this.parent.children.unshift(this),this.parent._setChildrenIndices(),!0)}setZIndex(t){if(!this.parent)return g.warn("Node has no parent. zIndex parameter is ignored."),this;(t<0||t>=this.parent.children.length)&&g.warn("Unexpected value "+t+" for zIndex property. zIndex is just index of a node in children of its parent. Expected value is from 0 to "+(this.parent.children.length-1)+".");const e=this.index;return this.parent.children.splice(e,1),this.parent.children.splice(t,0,this),this.parent._setChildrenIndices(),this}getAbsoluteOpacity(){return this._getCache(D,this._getAbsoluteOpacity)}_getAbsoluteOpacity(){let t=this.opacity();const e=this.getParent();return e&&!e._isUnderCache&&(t*=e.getAbsoluteOpacity()),t}moveTo(t){return this.getParent()!==t&&(this._remove(),t.add(this)),this}toObject(){let t,e,i,n,s,r=this.getAttrs();const a={attrs:{},className:this.getClassName()};for(t in r)e=r[t],s=g.isObject(e)&&!g._isPlainObject(e)&&!g._isArray(e),s||(i="function"==typeof this[t]&&this[t],delete r[t],n=i?i.call(this):null,r[t]=e,n!==e&&(a.attrs[t]=e));return g._prepareToStringify(a)}toJSON(){return JSON.stringify(this.toObject())}getParent(){return this.parent}findAncestors(t,e,i){const n=[];e&&this._isMatch(t)&&n.push(this);let s=this.parent;for(;s;){if(s===i)return n;s._isMatch(t)&&n.push(s),s=s.parent}return n}isAncestorOf(t){return!1}findAncestor(t,e,i){return this.findAncestors(t,e,i)[0]}_isMatch(t){if(!t)return!1;if("function"==typeof t)return t(this);let e,i,n=t.replace(/ /g,"").split(","),s=n.length;for(e=0;e<s;e++)if(i=n[e],g.isValidSelector(i)||(g.warn('Selector "'+i+'" is invalid. Allowed selectors examples are "#foo", ".bar" or "Group".'),g.warn('If you have a custom shape with such className, please change it to start with upper letter like "Triangle".'),g.warn("Konva is awesome, right?")),"#"===i.charAt(0)){if(this.id()===i.slice(1))return!0}else if("."===i.charAt(0)){if(this.hasName(i.slice(1)))return!0}else if(this.className===i||this.nodeType===i)return!0;return!1}getLayer(){const t=this.getParent();return t?t.getLayer():null}getStage(){return this._getCache(Y,this._getStage)}_getStage(){const t=this.getParent();return t?t.getStage():null}fire(t,e={},i){return e.target=e.target||this,i?this._fireAndBubble(t,e):this._fire(t,e),this}getAbsoluteTransform(t){return t?this._getAbsoluteTransform(t):this._getCache(I,this._getAbsoluteTransform)}_getAbsoluteTransform(t){let e;if(t)return e=new s,this._eachAncestorReverse((function(t){const i=t.transformsEnabled();"all"===i?e.multiply(t.getTransform()):"position"===i&&e.translate(t.x()-t.offsetX(),t.y()-t.offsetY())}),t),e;{e=this._cache.get(I)||new s,this.parent?this.parent.getAbsoluteTransform().copyInto(e):e.reset();const t=this.transformsEnabled();if("all"===t)e.multiply(this.getTransform());else if("position"===t){const t=this.attrs.x||0,i=this.attrs.y||0,n=this.attrs.offsetX||0,s=this.attrs.offsetY||0;e.translate(t-n,i-s)}return e.dirty=!1,e}}getAbsoluteScale(t){let e=this;for(;e;)e._isUnderCache&&(t=e),e=e.getParent();const i=this.getAbsoluteTransform(t).decompose();return{x:i.scaleX,y:i.scaleY}}getAbsoluteRotation(){return this.getAbsoluteTransform().decompose().rotation}getTransform(){return this._getCache(X,this._getTransform)}_getTransform(){var t,e;const n=this._cache.get(X)||new s;n.reset();const r=this.x(),a=this.y(),o=i.getAngle(this.rotation()),h=null!==(t=this.attrs.scaleX)&&void 0!==t?t:1,l=null!==(e=this.attrs.scaleY)&&void 0!==e?e:1,d=this.attrs.skewX||0,c=this.attrs.skewY||0,g=this.attrs.offsetX||0,u=this.attrs.offsetY||0;return 0===r&&0===a||n.translate(r,a),0!==o&&n.rotate(o),0===d&&0===c||n.skew(d,c),1===h&&1===l||n.scale(h,l),0===g&&0===u||n.translate(-1*g,-1*u),n.dirty=!1,n}clone(t){let e,i,n,s,r,a=g.cloneObject(this.attrs);for(e in t)a[e]=t[e];const o=new this.constructor(a);for(e in this.eventListeners)for(i=this.eventListeners[e],n=i.length,s=0;s<n;s++)r=i[s],r.name.indexOf("konva")<0&&(o.eventListeners[e]||(o.eventListeners[e]=[]),o.eventListeners[e].push(r));return o}_toKonvaCanvas(t){t=t||{};const e=this.getClientRect(),i=this.getStage(),n=void 0!==t.x?t.x:Math.floor(e.x),s=void 0!==t.y?t.y:Math.floor(e.y),r=t.pixelRatio||1,a=new G({width:t.width||Math.ceil(e.width)||(i?i.width():0),height:t.height||Math.ceil(e.height)||(i?i.height():0),pixelRatio:r}),o=a.getContext(),h=new G({width:a.width/a.pixelRatio+Math.abs(n),height:a.height/a.pixelRatio+Math.abs(s),pixelRatio:a.pixelRatio});return!1===t.imageSmoothingEnabled&&(o._context.imageSmoothingEnabled=!1),o.save(),(n||s)&&o.translate(-1*n,-1*s),this.drawScene(a,void 0,h),o.restore(),a}toCanvas(t){return this._toKonvaCanvas(t)._canvas}toDataURL(t){const e=(t=t||{}).mimeType||null,i=t.quality||null,n=this._toKonvaCanvas(t).toDataURL(e,i);return t.callback&&t.callback(n),n}toImage(t){return new Promise(((e,i)=>{try{const i=null==t?void 0:t.callback;i&&delete t.callback,g._urlToImage(this.toDataURL(t),(function(t){e(t),null==i||i(t)}))}catch(t){i(t)}}))}toBlob(t){return new Promise(((e,i)=>{try{const i=null==t?void 0:t.callback;i&&delete t.callback,this.toCanvas(t).toBlob((t=>{e(t),null==i||i(t)}),null==t?void 0:t.mimeType,null==t?void 0:t.quality)}catch(t){i(t)}}))}setSize(t){return this.width(t.width),this.height(t.height),this}getSize(){return{width:this.width(),height:this.height()}}getClassName(){return this.className||this.nodeType}getType(){return this.nodeType}getDragDistance(){return void 0!==this.attrs.dragDistance?this.attrs.dragDistance:this.parent?this.parent.getDragDistance():i.dragDistance}_off(t,e,i){let n,s,r,a=this.eventListeners[t];for(n=0;n<a.length;n++)if(s=a[n].name,r=a[n].handler,!("konva"===s&&"konva"!==e||e&&s!==e||i&&i!==r)){if(a.splice(n,1),0===a.length){delete this.eventListeners[t];break}n--}}_fireChangeEvent(t,e,i){this._fire(t+"Change",{oldVal:e,newVal:i})}addName(t){if(!this.hasName(t)){const e=this.name(),i=e?e+" "+t:t;this.name(i)}return this}hasName(t){if(!t)return!1;const e=this.name();if(!e)return!1;return-1!==(e||"").split(/\s/g).indexOf(t)}removeName(t){const e=(this.name()||"").split(/\s/g),i=e.indexOf(t);return-1!==i&&(e.splice(i,1),this.name(e.join(" "))),this}setAttr(t,e){const i=this["set"+g._capitalize(t)];return g._isFunction(i)?i.call(this,e):this._setAttr(t,e),this}_requestDraw(){if(i.autoDrawEnabled){const t=this.getLayer()||this.getStage();null==t||t.batchDraw()}}_setAttr(t,e){const i=this.attrs[t];(i!==e||g.isObject(e))&&(null==e?delete this.attrs[t]:this.attrs[t]=e,this._shouldFireChangeEvents&&this._fireChangeEvent(t,i,e),this._requestDraw())}_setComponentAttr(t,e,i){let n;void 0!==i&&(n=this.attrs[t],n||(this.attrs[t]=this.getAttr(t)),this.attrs[t][e]=i,this._fireChangeEvent(t,n,i))}_fireAndBubble(t,e,i){e&&this.nodeType===z&&(e.target=this);if(!((t===N||t===H)&&(i&&(this===i||this.isAncestorOf&&this.isAncestorOf(i))||"Stage"===this.nodeType&&!i))){this._fire(t,e);const n=(t===N||t===H)&&i&&i.isAncestorOf&&i.isAncestorOf(this)&&!i.isAncestorOf(this.parent);(e&&!e.cancelBubble||!e)&&this.parent&&this.parent.isListening()&&!n&&(i&&i.parent?this._fireAndBubble.call(this.parent,t,e,i):this._fireAndBubble.call(this.parent,t,e))}}_getProtoListeners(t){var e,i,n;const s=null!==(e=this._cache.get(L))&&void 0!==e?e:{};let r=null==s?void 0:s[t];if(void 0===r){r=[];let e=Object.getPrototypeOf(this);for(;e;){const s=null!==(n=null===(i=e.eventListeners)||void 0===i?void 0:i[t])&&void 0!==n?n:[];r.push(...s),e=Object.getPrototypeOf(e)}s[t]=r,this._cache.set(L,s)}return r}_fire(t,e){(e=e||{}).currentTarget=this,e.type=t;const i=this._getProtoListeners(t);if(i)for(var n=0;n<i.length;n++)i[n].handler.call(this,e);const s=this.eventListeners[t];if(s)for(n=0;n<s.length;n++)s[n].handler.call(this,e)}draw(){return this.drawScene(),this.drawHit(),this}_createDragElement(t){const e=t?t.pointerId:void 0,i=this.getStage(),n=this.getAbsolutePosition();if(!i)return;const s=i._getPointerById(e)||i._changedPointerPositions[0]||n;E._dragElements.set(this._id,{node:this,startPointerPos:s,offset:{x:s.x-n.x,y:s.y-n.y},dragStatus:"ready",pointerId:e})}startDrag(t,e=!0){E._dragElements.has(this._id)||this._createDragElement(t);E._dragElements.get(this._id).dragStatus="dragging",this.fire("dragstart",{type:"dragstart",target:this,evt:t&&t.evt},e)}_setDragPosition(t,e){const i=this.getStage()._getPointerById(e.pointerId);if(!i)return;let n={x:i.x-e.offset.x,y:i.y-e.offset.y};const s=this.dragBoundFunc();if(void 0!==s){const e=s.call(this,n,t);e?n=e:g.warn("dragBoundFunc did not return any value. That is unexpected behavior. You must return new absolute position from dragBoundFunc.")}this._lastPos&&this._lastPos.x===n.x&&this._lastPos.y===n.y||(this.setAbsolutePosition(n),this._requestDraw()),this._lastPos=n}stopDrag(t){const e=E._dragElements.get(this._id);e&&(e.dragStatus="stopped"),E._endDragBefore(t),E._endDragAfter(t)}setDraggable(t){this._setAttr("draggable",t),this._dragChange()}isDragging(){const t=E._dragElements.get(this._id);return!!t&&"dragging"===t.dragStatus}_listenDrag(){this._dragCleanup(),this.on("mousedown.konva touchstart.konva",(function(t){if(!(!(void 0!==t.evt.button)||i.dragButtons.indexOf(t.evt.button)>=0))return;if(this.isDragging())return;let e=!1;E._dragElements.forEach((t=>{this.isAncestorOf(t.node)&&(e=!0)})),e||this._createDragElement(t)}))}_dragChange(){if(this.attrs.draggable)this._listenDrag();else{this._dragCleanup();if(!this.getStage())return;const t=E._dragElements.get(this._id),e=t&&"dragging"===t.dragStatus,i=t&&"ready"===t.dragStatus;e?this.stopDrag():i&&E._dragElements.delete(this._id)}}_dragCleanup(){this.off("mousedown.konva"),this.off("touchstart.konva")}isClientRectOnScreen(t={x:0,y:0}){const e=this.getStage();if(!e)return!1;const i={x:-t.x,y:-t.y,width:e.width()+2*t.x,height:e.height()+2*t.y};return g.haveIntersection(i,this.getClientRect())}static create(t,e){return g._isString(t)&&(t=JSON.parse(t)),this._createNode(t,e)}static _createNode(t,e){let n,s,r,a=V.prototype.getClassName.call(t),o=t.children;e&&(t.attrs.container=e),i[a]||(g.warn('Can not find a node with class name "'+a+'". Fallback to "Shape".'),a="Shape");if(n=new(0,i[a])(t.attrs),o)for(s=o.length,r=0;r<s;r++)n.add(V._createNode(o[r]));return n}}V.prototype.nodeType="Node",V.prototype._attrsAffectingSize=[],V.prototype.eventListeners={},V.prototype.on.call(V.prototype,q,(function(){this._batchingTransformChange?this._needClearTransformCache=!0:(this._clearCache(X),this._clearSelfAndDescendantCache(I))})),V.prototype.on.call(V.prototype,"visibleChange.konva",(function(){this._clearSelfAndDescendantCache(j)})),V.prototype.on.call(V.prototype,"listeningChange.konva",(function(){this._clearSelfAndDescendantCache(B)})),V.prototype.on.call(V.prototype,"opacityChange.konva",(function(){this._clearSelfAndDescendantCache(D)}));const K=w.addGetterSetter;K(V,"zIndex"),K(V,"absolutePosition"),K(V,"position"),K(V,"x",0,p()),K(V,"y",0,p()),K(V,"globalCompositeOperation","source-over",y()),K(V,"opacity",1,p()),K(V,"name","",y()),K(V,"id","",y()),K(V,"rotation",0,p()),w.addComponentsGetterSetter(V,"scale",["x","y"]),K(V,"scaleX",1,p()),K(V,"scaleY",1,p()),w.addComponentsGetterSetter(V,"skew",["x","y"]),K(V,"skewX",0,p()),K(V,"skewY",0,p()),w.addComponentsGetterSetter(V,"offset",["x","y"]),K(V,"offsetX",0,p()),K(V,"offsetY",0,p()),K(V,"dragDistance",void 0,p()),K(V,"width",0,p()),K(V,"height",0,p()),K(V,"listening",!0,x()),K(V,"preventDefault",!0,x()),K(V,"filters",void 0,(function(t){return this._filterUpToDate=!1,t})),K(V,"visible",!0,x()),K(V,"transformsEnabled","all",y()),K(V,"size"),K(V,"dragBoundFunc"),K(V,"draggable",!1,x()),w.backCompat(V,{rotateDeg:"rotate",setRotationDeg:"setRotation",getRotationDeg:"getRotation"});class Q extends V{constructor(){super(...arguments),this.children=[]}getChildren(t){if(!t)return this.children||[];const e=this.children||[],i=[];return e.forEach((function(e){t(e)&&i.push(e)})),i}hasChildren(){return this.getChildren().length>0}removeChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.remove()})),this.children=[],this._requestDraw(),this}destroyChildren(){return this.getChildren().forEach((t=>{t.parent=null,t.index=0,t.destroy()})),this.children=[],this._requestDraw(),this}add(...t){if(0===t.length)return this;if(t.length>1){for(let e=0;e<t.length;e++)this.add(t[e]);return this}const e=t[0];return e.getParent()?(e.moveTo(this),this):(this._validateAdd(e),e.index=this.getChildren().length,e.parent=this,e._clearCaches(),this.getChildren().push(e),this._fire("add",{child:e}),this._requestDraw(),this)}destroy(){return this.hasChildren()&&this.destroyChildren(),super.destroy(),this}find(t){return this._generalFind(t,!1)}findOne(t){const e=this._generalFind(t,!0);return e.length>0?e[0]:void 0}_generalFind(t,e){const i=[];return this._descendants((n=>{const s=n._isMatch(t);return s&&i.push(n),!(!s||!e)})),i}_descendants(t){let e=!1;const i=this.getChildren();for(const n of i){if(e=t(n),e)return!0;if(n.hasChildren()&&(e=n._descendants(t),e))return!0}return!1}toObject(){const t=V.prototype.toObject.call(this);return t.children=[],this.getChildren().forEach((e=>{t.children.push(e.toObject())})),t}isAncestorOf(t){let e=t.getParent();for(;e;){if(e._id===this._id)return!0;e=e.getParent()}return!1}clone(t){const e=V.prototype.clone.call(this,t);return this.getChildren().forEach((function(t){e.add(t.clone())})),e}getAllIntersections(t){const e=[];return this.find("Shape").forEach((i=>{i.isVisible()&&i.intersects(t)&&e.push(i)})),e}_clearSelfAndDescendantCache(t){var e;super._clearSelfAndDescendantCache(t),this.isCached()||null===(e=this.children)||void 0===e||e.forEach((function(e){e._clearSelfAndDescendantCache(t)}))}_setChildrenIndices(){var t;null===(t=this.children)||void 0===t||t.forEach((function(t,e){t.index=e})),this._requestDraw()}drawScene(t,e,i){const n=this.getLayer(),s=t||n&&n.getCanvas(),r=s&&s.getContext(),a=this._getCanvasCache(),o=a&&a.scene,h=s&&s.isCache;if(!this.isVisible()&&!h)return this;if(o){r.save();const t=this.getAbsoluteTransform(e).getMatrix();r.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedSceneCanvas(r),r.restore()}else this._drawChildren("drawScene",s,e,i);return this}drawHit(t,e){if(!this.shouldDrawHit(e))return this;const i=this.getLayer(),n=t||i&&i.hitCanvas,s=n&&n.getContext(),r=this._getCanvasCache();if(r&&r.hit){s.save();const t=this.getAbsoluteTransform(e).getMatrix();s.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedHitCanvas(s),s.restore()}else this._drawChildren("drawHit",n,e);return this}_drawChildren(t,e,i,n){var s;const r=e&&e.getContext(),a=this.clipWidth(),o=this.clipHeight(),h=this.clipFunc(),l="number"==typeof a&&"number"==typeof o||h,d=i===this;if(l){r.save();const t=this.getAbsoluteTransform(i);let e,n=t.getMatrix();if(r.transform(n[0],n[1],n[2],n[3],n[4],n[5]),r.beginPath(),h)e=h.call(this,r,this);else{const t=this.clipX(),e=this.clipY();r.rect(t||0,e||0,a,o)}r.clip.apply(r,e),n=t.copy().invert().getMatrix(),r.transform(n[0],n[1],n[2],n[3],n[4],n[5])}const c=!d&&"source-over"!==this.globalCompositeOperation()&&"drawScene"===t;c&&(r.save(),r._applyGlobalCompositeOperation(this)),null===(s=this.children)||void 0===s||s.forEach((function(s){s[t](e,i,n)})),c&&r.restore(),l&&r.restore()}getClientRect(t={}){var e;const i=t.skipTransform,n=t.relativeTo;let s,r,a,o,h={x:1/0,y:1/0,width:0,height:0};const l=this;null===(e=this.children)||void 0===e||e.forEach((function(e){if(!e.visible())return;const i=e.getClientRect({relativeTo:l,skipShadow:t.skipShadow,skipStroke:t.skipStroke});0===i.width&&0===i.height||(void 0===s?(s=i.x,r=i.y,a=i.x+i.width,o=i.y+i.height):(s=Math.min(s,i.x),r=Math.min(r,i.y),a=Math.max(a,i.x+i.width),o=Math.max(o,i.y+i.height)))}));const d=this.find("Shape");let c=!1;for(let t=0;t<d.length;t++){if(d[t]._isVisible(this)){c=!0;break}}return h=c&&void 0!==s?{x:s,y:r,width:a-s,height:o-r}:{x:0,y:0,width:0,height:0},i?h:this._transformedRect(h,n)}}w.addComponentsGetterSetter(Q,"clip",["x","y","width","height"]),w.addGetterSetter(Q,"clipX",void 0,p()),w.addGetterSetter(Q,"clipY",void 0,p()),w.addGetterSetter(Q,"clipWidth",void 0,p()),w.addGetterSetter(Q,"clipHeight",void 0,p()),w.addGetterSetter(Q,"clipFunc");const J=new Map,$=void 0!==i._global.PointerEvent;function Z(t){return J.get(t)}function tt(t){return{evt:t,pointerId:t.pointerId}}function et(t,e){return J.get(t)===e}function it(t,e){nt(t);e.getStage()&&(J.set(t,e),$&&e._fire("gotpointercapture",tt(new PointerEvent("gotpointercapture"))))}function nt(t,e){const i=J.get(t);if(!i)return;const n=i.getStage();n&&n.content,J.delete(t),$&&i._fire("lostpointercapture",tt(new PointerEvent("lostpointercapture")))}const st="mouseleave",rt="mouseover",at="mouseenter",ot="mousemove",ht="mousedown",lt="mouseup",dt="pointermove",ct="pointerdown",gt="pointerup",ut="pointercancel",ft="pointerout",pt="pointerleave",mt="pointerover",_t="pointerenter",yt="contextmenu",vt="touchstart",xt="touchend",bt="touchmove",St="touchcancel",wt="wheel",Ct=[[at,"_pointerenter"],[ht,"_pointerdown"],[ot,"_pointermove"],[lt,"_pointerup"],[st,"_pointerleave"],[vt,"_pointerdown"],[bt,"_pointermove"],[xt,"_pointerup"],[St,"_pointercancel"],[rt,"_pointerover"],[wt,"_wheel"],[yt,"_contextmenu"],[ct,"_pointerdown"],[dt,"_pointermove"],[gt,"_pointerup"],[ut,"_pointercancel"],["lostpointercapture","_lostpointercapture"]],Pt={mouse:{[ft]:"mouseout",[pt]:st,[mt]:rt,[_t]:at,[dt]:ot,[ct]:ht,[gt]:lt,[ut]:"mousecancel",pointerclick:"click",pointerdblclick:"dblclick"},touch:{[ft]:"touchout",[pt]:"touchleave",[mt]:"touchover",[_t]:"touchenter",[dt]:bt,[ct]:vt,[gt]:xt,[ut]:St,pointerclick:"tap",pointerdblclick:"dbltap"},pointer:{[ft]:ft,[pt]:pt,[mt]:mt,[_t]:_t,[dt]:dt,[ct]:ct,[gt]:gt,[ut]:ut,pointerclick:"pointerclick",pointerdblclick:"pointerdblclick"}},kt=t=>t.indexOf("pointer")>=0?"pointer":t.indexOf("touch")>=0?"touch":"mouse",At=t=>{const e=kt(t);return"pointer"===e?i.pointerEventsEnabled&&Pt.pointer:"touch"===e?Pt.touch:"mouse"===e?Pt.mouse:void 0};function Tt(t={}){return(t.clipFunc||t.clipWidth||t.clipHeight)&&g.warn("Stage does not support clipping. Please use clip for Layers or Groups."),t}const Mt=[];class Gt extends Q{constructor(t){super(Tt(t)),this._pointerPositions=[],this._changedPointerPositions=[],this._buildDOM(),this._bindContentEvents(),Mt.push(this),this.on("widthChange.konva heightChange.konva",this._resizeDOM),this.on("visibleChange.konva",this._checkVisibility),this.on("clipWidthChange.konva clipHeightChange.konva clipFuncChange.konva",(()=>{Tt(this.attrs)})),this._checkVisibility()}_validateAdd(t){const e="Layer"===t.getType(),i="FastLayer"===t.getType();e||i||g.throw("You may only add layers to the stage.")}_checkVisibility(){if(!this.content)return;const t=this.visible()?"":"none";this.content.style.display=t}setContainer(t){if("string"==typeof t){if("."===t.charAt(0)){const e=t.slice(1);t=document.getElementsByClassName(e)[0]}else{var e;e="#"!==t.charAt(0)?t:t.slice(1),t=document.getElementById(e)}if(!t)throw"Can not find container in document with id "+e}return this._setAttr("container",t),this.content&&(this.content.parentElement&&this.content.parentElement.removeChild(this.content),t.appendChild(this.content)),this}shouldDrawHit(){return!0}clear(){const t=this.children,e=t.length;for(let i=0;i<e;i++)t[i].clear();return this}clone(t){return t||(t={}),t.container="undefined"!=typeof document&&document.createElement("div"),Q.prototype.clone.call(this,t)}destroy(){super.destroy();const t=this.content;t&&g._isInDocument(t)&&this.container().removeChild(t);const e=Mt.indexOf(this);return e>-1&&Mt.splice(e,1),g.releaseCanvas(this.bufferCanvas._canvas,this.bufferHitCanvas._canvas),this}getPointerPosition(){const t=this._pointerPositions[0]||this._changedPointerPositions[0];return t?{x:t.x,y:t.y}:(g.warn("Pointer position is missing and not registered by the stage. Looks like it is outside of the stage container. You can set it manually from event: stage.setPointersPositions(event);"),null)}_getPointerById(t){return this._pointerPositions.find((e=>e.id===t))}getPointersPositions(){return this._pointerPositions}getStage(){return this}getContent(){return this.content}_toKonvaCanvas(t){(t=t||{}).x=t.x||0,t.y=t.y||0,t.width=t.width||this.width(),t.height=t.height||this.height();const e=new G({width:t.width,height:t.height,pixelRatio:t.pixelRatio||1}),i=e.getContext()._context,n=this.children;return(t.x||t.y)&&i.translate(-1*t.x,-1*t.y),n.forEach((function(e){if(!e.isVisible())return;const n=e._toKonvaCanvas(t);i.drawImage(n._canvas,t.x,t.y,n.getWidth()/n.getPixelRatio(),n.getHeight()/n.getPixelRatio())})),e}getIntersection(t){if(!t)return null;const e=this.children;for(let i=e.length-1;i>=0;i--){const n=e[i].getIntersection(t);if(n)return n}return null}_resizeDOM(){const t=this.width(),e=this.height();this.content&&(this.content.style.width=t+"px",this.content.style.height=e+"px"),this.bufferCanvas.setSize(t,e),this.bufferHitCanvas.setSize(t,e),this.children.forEach((i=>{i.setSize({width:t,height:e}),i.draw()}))}add(t,...e){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}super.add(t);const n=this.children.length;return n>5&&g.warn("The stage has "+n+" layers. Recommended maximum number of layers is 3-5. Adding more layers into the stage may drop the performance. Rethink your tree structure, you can use Konva.Group."),t.setSize({width:this.width(),height:this.height()}),t.draw(),i.isBrowser&&this.content.appendChild(t.canvas._canvas),this}getParent(){return null}getLayer(){return null}hasPointerCapture(t){return et(t,this)}setPointerCapture(t){it(t,this)}releaseCapture(t){nt(t)}getLayers(){return this.children}_bindContentEvents(){i.isBrowser&&Ct.forEach((([t,e])=>{this.content.addEventListener(t,(t=>{this[e](t)}),{passive:!1})}))}_pointerenter(t){this.setPointersPositions(t);const e=At(t.type);e&&this._fire(e.pointerenter,{evt:t,target:this,currentTarget:this})}_pointerover(t){this.setPointersPositions(t);const e=At(t.type);e&&this._fire(e.pointerover,{evt:t,target:this,currentTarget:this})}_getTargetShape(t){let e=this[t+"targetShape"];return e&&!e.getStage()&&(e=null),e}_pointerleave(t){const e=At(t.type),n=kt(t.type);if(!e)return;this.setPointersPositions(t);const s=this._getTargetShape(n),r=!(i.isDragging()||i.isTransforming())||i.hitOnDragEnabled;s&&r?(s._fireAndBubble(e.pointerout,{evt:t}),s._fireAndBubble(e.pointerleave,{evt:t}),this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this[n+"targetShape"]=null):r&&(this._fire(e.pointerleave,{evt:t,target:this,currentTarget:this}),this._fire(e.pointerout,{evt:t,target:this,currentTarget:this})),this.pointerPos=null,this._pointerPositions=[]}_pointerdown(t){const e=At(t.type),n=kt(t.type);if(!e)return;this.setPointersPositions(t);let s=!1;this._changedPointerPositions.forEach((r=>{const a=this.getIntersection(r);if(E.justDragged=!1,i["_"+n+"ListenClick"]=!0,!a||!a.isListening())return void(this[n+"ClickStartShape"]=void 0);i.capturePointerEventsEnabled&&a.setPointerCapture(r.id),this[n+"ClickStartShape"]=a,a._fireAndBubble(e.pointerdown,{evt:t,pointerId:r.id}),s=!0;const o=t.type.indexOf("touch")>=0;a.preventDefault()&&t.cancelable&&o&&t.preventDefault()})),s||this._fire(e.pointerdown,{evt:t,target:this,currentTarget:this,pointerId:this._pointerPositions[0].id})}_pointermove(t){const e=At(t.type),n=kt(t.type);if(!e)return;i.isDragging()&&E.node.preventDefault()&&t.cancelable&&t.preventDefault(),this.setPointersPositions(t);if(!(!(i.isDragging()||i.isTransforming())||i.hitOnDragEnabled))return;const s={};let r=!1;const a=this._getTargetShape(n);this._changedPointerPositions.forEach((i=>{const o=Z(i.id)||this.getIntersection(i),h=i.id,l={evt:t,pointerId:h},d=a!==o;if(d&&a&&(a._fireAndBubble(e.pointerout,{...l},o),a._fireAndBubble(e.pointerleave,{...l},o)),o){if(s[o._id])return;s[o._id]=!0}o&&o.isListening()?(r=!0,d&&(o._fireAndBubble(e.pointerover,{...l},a),o._fireAndBubble(e.pointerenter,{...l},a),this[n+"targetShape"]=o),o._fireAndBubble(e.pointermove,{...l})):a&&(this._fire(e.pointerover,{evt:t,target:this,currentTarget:this,pointerId:h}),this[n+"targetShape"]=null)})),r||this._fire(e.pointermove,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id})}_pointerup(t){const e=At(t.type),n=kt(t.type);if(!e)return;this.setPointersPositions(t);const s=this[n+"ClickStartShape"],r=this[n+"ClickEndShape"],a={};let o=!1;this._changedPointerPositions.forEach((h=>{const l=Z(h.id)||this.getIntersection(h);if(l){if(l.releaseCapture(h.id),a[l._id])return;a[l._id]=!0}const d=h.id,c={evt:t,pointerId:d};let g=!1;i["_"+n+"InDblClickWindow"]?(g=!0,clearTimeout(this[n+"DblTimeout"])):E.justDragged||(i["_"+n+"InDblClickWindow"]=!0,clearTimeout(this[n+"DblTimeout"])),this[n+"DblTimeout"]=setTimeout((function(){i["_"+n+"InDblClickWindow"]=!1}),i.dblClickWindow),l&&l.isListening()?(o=!0,this[n+"ClickEndShape"]=l,l._fireAndBubble(e.pointerup,{...c}),i["_"+n+"ListenClick"]&&s&&s===l&&(l._fireAndBubble(e.pointerclick,{...c}),g&&r&&r===l&&l._fireAndBubble(e.pointerdblclick,{...c}))):(this[n+"ClickEndShape"]=null,i["_"+n+"ListenClick"]&&this._fire(e.pointerclick,{evt:t,target:this,currentTarget:this,pointerId:d}),g&&this._fire(e.pointerdblclick,{evt:t,target:this,currentTarget:this,pointerId:d}))})),o||this._fire(e.pointerup,{evt:t,target:this,currentTarget:this,pointerId:this._changedPointerPositions[0].id}),i["_"+n+"ListenClick"]=!1,t.cancelable&&"touch"!==n&&"pointer"!==n&&t.preventDefault()}_contextmenu(t){this.setPointersPositions(t);const e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(yt,{evt:t}):this._fire(yt,{evt:t,target:this,currentTarget:this})}_wheel(t){this.setPointersPositions(t);const e=this.getIntersection(this.getPointerPosition());e&&e.isListening()?e._fireAndBubble(wt,{evt:t}):this._fire(wt,{evt:t,target:this,currentTarget:this})}_pointercancel(t){this.setPointersPositions(t);const e=Z(t.pointerId)||this.getIntersection(this.getPointerPosition());e&&e._fireAndBubble(gt,tt(t)),nt(t.pointerId)}_lostpointercapture(t){nt(t.pointerId)}setPointersPositions(t){const e=this._getContentPosition();let i=null,n=null;void 0!==(t=t||window.event).touches?(this._pointerPositions=[],this._changedPointerPositions=[],Array.prototype.forEach.call(t.touches,(t=>{this._pointerPositions.push({id:t.identifier,x:(t.clientX-e.left)/e.scaleX,y:(t.clientY-e.top)/e.scaleY})})),Array.prototype.forEach.call(t.changedTouches||t.touches,(t=>{this._changedPointerPositions.push({id:t.identifier,x:(t.clientX-e.left)/e.scaleX,y:(t.clientY-e.top)/e.scaleY})}))):(i=(t.clientX-e.left)/e.scaleX,n=(t.clientY-e.top)/e.scaleY,this.pointerPos={x:i,y:n},this._pointerPositions=[{x:i,y:n,id:g._getFirstPointerId(t)}],this._changedPointerPositions=[{x:i,y:n,id:g._getFirstPointerId(t)}])}_setPointerPosition(t){g.warn('Method _setPointerPosition is deprecated. Use "stage.setPointersPositions(event)" instead.'),this.setPointersPositions(t)}_getContentPosition(){if(!this.content||!this.content.getBoundingClientRect)return{top:0,left:0,scaleX:1,scaleY:1};const t=this.content.getBoundingClientRect();return{top:t.top,left:t.left,scaleX:t.width/this.content.clientWidth||1,scaleY:t.height/this.content.clientHeight||1}}_buildDOM(){if(this.bufferCanvas=new G({width:this.width(),height:this.height()}),this.bufferHitCanvas=new R({pixelRatio:1,width:this.width(),height:this.height()}),!i.isBrowser)return;const t=this.container();if(!t)throw"Stage has no container. A container is required.";t.innerHTML="",this.content=document.createElement("div"),this.content.style.position="relative",this.content.style.userSelect="none",this.content.className="konvajs-content",this.content.setAttribute("role","presentation"),t.appendChild(this.content),this._resizeDOM()}cache(){return g.warn("Cache function is not allowed for stage. You may use cache only for layers, groups and shapes."),this}clearCache(){return this}batchDraw(){return this.getChildren().forEach((function(t){t.batchDraw()})),this}}Gt.prototype.nodeType="Stage",n(Gt),w.addGetterSetter(Gt,"container"),i.isBrowser&&document.addEventListener("visibilitychange",(()=>{Mt.forEach((t=>{t.batchDraw()}))}));const Rt="hasShadow",Et="shadowRGBA",Dt="patternImage",Lt="linearGradient",It="radialGradient";let Ot;function Ft(){return Ot||(Ot=g.createCanvasElement().getContext("2d"),Ot)}const Bt={};class Nt extends V{constructor(t){let e;for(super(t);e=g.getRandomColor(),!e||e in Bt;);this.colorKey=e,Bt[e]=this}getContext(){return g.warn("shape.getContext() method is deprecated. Please do not use it."),this.getLayer().getContext()}getCanvas(){return g.warn("shape.getCanvas() method is deprecated. Please do not use it."),this.getLayer().getCanvas()}getSceneFunc(){return this.attrs.sceneFunc||this._sceneFunc}getHitFunc(){return this.attrs.hitFunc||this._hitFunc}hasShadow(){return this._getCache(Rt,this._hasShadow)}_hasShadow(){return this.shadowEnabled()&&0!==this.shadowOpacity()&&!!(this.shadowColor()||this.shadowBlur()||this.shadowOffsetX()||this.shadowOffsetY())}_getFillPattern(){return this._getCache(Dt,this.__getFillPattern)}__getFillPattern(){if(this.fillPatternImage()){const t=Ft().createPattern(this.fillPatternImage(),this.fillPatternRepeat()||"repeat");if(t&&t.setTransform){const e=new s;e.translate(this.fillPatternX(),this.fillPatternY()),e.rotate(i.getAngle(this.fillPatternRotation())),e.scale(this.fillPatternScaleX(),this.fillPatternScaleY()),e.translate(-1*this.fillPatternOffsetX(),-1*this.fillPatternOffsetY());const n=e.getMatrix(),r="undefined"==typeof DOMMatrix?{a:n[0],b:n[1],c:n[2],d:n[3],e:n[4],f:n[5]}:new DOMMatrix(n);t.setTransform(r)}return t}}_getLinearGradient(){return this._getCache(Lt,this.__getLinearGradient)}__getLinearGradient(){const t=this.fillLinearGradientColorStops();if(t){const e=Ft(),i=this.fillLinearGradientStartPoint(),n=this.fillLinearGradientEndPoint(),s=e.createLinearGradient(i.x,i.y,n.x,n.y);for(let e=0;e<t.length;e+=2)s.addColorStop(t[e],t[e+1]);return s}}_getRadialGradient(){return this._getCache(It,this.__getRadialGradient)}__getRadialGradient(){const t=this.fillRadialGradientColorStops();if(t){const e=Ft(),i=this.fillRadialGradientStartPoint(),n=this.fillRadialGradientEndPoint(),s=e.createRadialGradient(i.x,i.y,this.fillRadialGradientStartRadius(),n.x,n.y,this.fillRadialGradientEndRadius());for(let e=0;e<t.length;e+=2)s.addColorStop(t[e],t[e+1]);return s}}getShadowRGBA(){return this._getCache(Et,this._getShadowRGBA)}_getShadowRGBA(){if(!this.hasShadow())return;const t=g.colorToRGBA(this.shadowColor());return t?"rgba("+t.r+","+t.g+","+t.b+","+t.a*(this.shadowOpacity()||1)+")":void 0}hasFill(){return this._calculate("hasFill",["fillEnabled","fill","fillPatternImage","fillLinearGradientColorStops","fillRadialGradientColorStops"],(()=>this.fillEnabled()&&!!(this.fill()||this.fillPatternImage()||this.fillLinearGradientColorStops()||this.fillRadialGradientColorStops())))}hasStroke(){return this._calculate("hasStroke",["strokeEnabled","strokeWidth","stroke","strokeLinearGradientColorStops"],(()=>this.strokeEnabled()&&this.strokeWidth()&&!(!this.stroke()&&!this.strokeLinearGradientColorStops())))}hasHitStroke(){const t=this.hitStrokeWidth();return"auto"===t?this.hasStroke():this.strokeEnabled()&&!!t}intersects(t){const e=this.getStage();if(!e)return!1;const i=e.bufferHitCanvas;i.getContext().clear(),this.drawHit(i,void 0,!0);return i.context.getImageData(Math.round(t.x),Math.round(t.y),1,1).data[3]>0}destroy(){return V.prototype.destroy.call(this),delete Bt[this.colorKey],delete this.colorKey,this}_useBufferCanvas(t){var e;if(!(null===(e=this.attrs.perfectDrawEnabled)||void 0===e||e))return!1;const i=t||this.hasFill(),n=this.hasStroke(),s=1!==this.getAbsoluteOpacity();if(i&&n&&s)return!0;const r=this.hasShadow(),a=this.shadowForStrokeEnabled();return!!(i&&n&&r&&a)}setStrokeHitEnabled(t){g.warn("strokeHitEnabled property is deprecated. Please use hitStrokeWidth instead."),t?this.hitStrokeWidth("auto"):this.hitStrokeWidth(0)}getStrokeHitEnabled(){return 0!==this.hitStrokeWidth()}getSelfRect(){const t=this.size();return{x:this._centroid?-t.width/2:0,y:this._centroid?-t.height/2:0,width:t.width,height:t.height}}getClientRect(t={}){let e=!1,i=this.getParent();for(;i;){if(i.isCached()){e=!0;break}i=i.getParent()}const n=t.skipTransform,s=t.relativeTo||e&&this.getStage()||void 0,r=this.getSelfRect(),a=!t.skipStroke&&this.hasStroke()&&this.strokeWidth()||0,o=r.width+a,h=r.height+a,l=!t.skipShadow&&this.hasShadow(),d=l?this.shadowOffsetX():0,c=l?this.shadowOffsetY():0,g=o+Math.abs(d),u=h+Math.abs(c),f=l&&this.shadowBlur()||0,p={width:g+2*f,height:u+2*f,x:-(a/2+f)+Math.min(d,0)+r.x,y:-(a/2+f)+Math.min(c,0)+r.y};return n?p:this._transformedRect(p,s)}drawScene(t,e,i){const n=this.getLayer();let s,r,a=t||n.getCanvas(),o=a.getContext(),h=this._getCanvasCache(),l=this.getSceneFunc(),d=this.hasShadow();const c=a.isCache,g=e===this;if(!this.isVisible()&&!g)return this;if(h){o.save();const t=this.getAbsoluteTransform(e).getMatrix();return o.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedSceneCanvas(o),o.restore(),this}if(!l)return this;if(o.save(),this._useBufferCanvas()&&!c){s=this.getStage();const t=i||s.bufferCanvas;r=t.getContext(),r.clear(),r.save(),r._applyLineJoin(this);var u=this.getAbsoluteTransform(e).getMatrix();r.transform(u[0],u[1],u[2],u[3],u[4],u[5]),l.call(this,r,this),r.restore();const n=t.pixelRatio;d&&o._applyShadow(this),o._applyOpacity(this),o._applyGlobalCompositeOperation(this),o.drawImage(t._canvas,0,0,t.width/n,t.height/n)}else{if(o._applyLineJoin(this),!g){u=this.getAbsoluteTransform(e).getMatrix();o.transform(u[0],u[1],u[2],u[3],u[4],u[5]),o._applyOpacity(this),o._applyGlobalCompositeOperation(this)}d&&o._applyShadow(this),l.call(this,o,this)}return o.restore(),this}drawHit(t,e,i=!1){if(!this.shouldDrawHit(e,i))return this;const n=this.getLayer(),s=t||n.hitCanvas,r=s&&s.getContext(),a=this.hitFunc()||this.sceneFunc(),o=this._getCanvasCache(),h=o&&o.hit;if(this.colorKey||g.warn("Looks like your canvas has a destroyed shape in it. Do not reuse shape after you destroyed it. If you want to reuse shape you should call remove() instead of destroy()"),h){r.save();const t=this.getAbsoluteTransform(e).getMatrix();return r.transform(t[0],t[1],t[2],t[3],t[4],t[5]),this._drawCachedHitCanvas(r),r.restore(),this}if(!a)return this;r.save(),r._applyLineJoin(this);if(!(this===e)){const t=this.getAbsoluteTransform(e).getMatrix();r.transform(t[0],t[1],t[2],t[3],t[4],t[5])}return a.call(this,r,this),r.restore(),this}drawHitFromCache(t=0){const e=this._getCanvasCache(),i=this._getCachedSceneCanvas(),n=e.hit,s=n.getContext(),r=n.getWidth(),a=n.getHeight();s.clear(),s.drawImage(i._canvas,0,0,r,a);try{const e=s.getImageData(0,0,r,a),i=e.data,n=i.length,o=g._hexToRgb(this.colorKey);for(let e=0;e<n;e+=4){i[e+3]>t?(i[e]=o.r,i[e+1]=o.g,i[e+2]=o.b,i[e+3]=255):i[e+3]=0}s.putImageData(e,0,0)}catch(t){g.error("Unable to draw hit graph from cached scene canvas. "+t.message)}return this}hasPointerCapture(t){return et(t,this)}setPointerCapture(t){it(t,this)}releaseCapture(t){nt(t)}}Nt.prototype._fillFunc=function(t){const e=this.attrs.fillRule;e?t.fill(e):t.fill()},Nt.prototype._strokeFunc=function(t){t.stroke()},Nt.prototype._fillFuncHit=function(t){const e=this.attrs.fillRule;e?t.fill(e):t.fill()},Nt.prototype._strokeFuncHit=function(t){t.stroke()},Nt.prototype._centroid=!1,Nt.prototype.nodeType="Shape",n(Nt),Nt.prototype.eventListeners={},Nt.prototype.on.call(Nt.prototype,"shadowColorChange.konva shadowBlurChange.konva shadowOffsetChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(Rt)})),Nt.prototype.on.call(Nt.prototype,"shadowColorChange.konva shadowOpacityChange.konva shadowEnabledChange.konva",(function(){this._clearCache(Et)})),Nt.prototype.on.call(Nt.prototype,"fillPriorityChange.konva fillPatternImageChange.konva fillPatternRepeatChange.konva fillPatternScaleXChange.konva fillPatternScaleYChange.konva fillPatternOffsetXChange.konva fillPatternOffsetYChange.konva fillPatternXChange.konva fillPatternYChange.konva fillPatternRotationChange.konva",(function(){this._clearCache(Dt)})),Nt.prototype.on.call(Nt.prototype,"fillPriorityChange.konva fillLinearGradientColorStopsChange.konva fillLinearGradientStartPointXChange.konva fillLinearGradientStartPointYChange.konva fillLinearGradientEndPointXChange.konva fillLinearGradientEndPointYChange.konva",(function(){this._clearCache(Lt)})),Nt.prototype.on.call(Nt.prototype,"fillPriorityChange.konva fillRadialGradientColorStopsChange.konva fillRadialGradientStartPointXChange.konva fillRadialGradientStartPointYChange.konva fillRadialGradientEndPointXChange.konva fillRadialGradientEndPointYChange.konva fillRadialGradientStartRadiusChange.konva fillRadialGradientEndRadiusChange.konva",(function(){this._clearCache(It)})),w.addGetterSetter(Nt,"stroke",void 0,v()),w.addGetterSetter(Nt,"strokeWidth",2,p()),w.addGetterSetter(Nt,"fillAfterStrokeEnabled",!1),w.addGetterSetter(Nt,"hitStrokeWidth","auto",_()),w.addGetterSetter(Nt,"strokeHitEnabled",!0,x()),w.addGetterSetter(Nt,"perfectDrawEnabled",!0,x()),w.addGetterSetter(Nt,"shadowForStrokeEnabled",!0,x()),w.addGetterSetter(Nt,"lineJoin"),w.addGetterSetter(Nt,"lineCap"),w.addGetterSetter(Nt,"sceneFunc"),w.addGetterSetter(Nt,"hitFunc"),w.addGetterSetter(Nt,"dash"),w.addGetterSetter(Nt,"dashOffset",0,p()),w.addGetterSetter(Nt,"shadowColor",void 0,y()),w.addGetterSetter(Nt,"shadowBlur",0,p()),w.addGetterSetter(Nt,"shadowOpacity",1,p()),w.addComponentsGetterSetter(Nt,"shadowOffset",["x","y"]),w.addGetterSetter(Nt,"shadowOffsetX",0,p()),w.addGetterSetter(Nt,"shadowOffsetY",0,p()),w.addGetterSetter(Nt,"fillPatternImage"),w.addGetterSetter(Nt,"fill",void 0,v()),w.addGetterSetter(Nt,"fillPatternX",0,p()),w.addGetterSetter(Nt,"fillPatternY",0,p()),w.addGetterSetter(Nt,"fillLinearGradientColorStops"),w.addGetterSetter(Nt,"strokeLinearGradientColorStops"),w.addGetterSetter(Nt,"fillRadialGradientStartRadius",0),w.addGetterSetter(Nt,"fillRadialGradientEndRadius",0),w.addGetterSetter(Nt,"fillRadialGradientColorStops"),w.addGetterSetter(Nt,"fillPatternRepeat","repeat"),w.addGetterSetter(Nt,"fillEnabled",!0),w.addGetterSetter(Nt,"strokeEnabled",!0),w.addGetterSetter(Nt,"shadowEnabled",!0),w.addGetterSetter(Nt,"dashEnabled",!0),w.addGetterSetter(Nt,"strokeScaleEnabled",!0),w.addGetterSetter(Nt,"fillPriority","color"),w.addComponentsGetterSetter(Nt,"fillPatternOffset",["x","y"]),w.addGetterSetter(Nt,"fillPatternOffsetX",0,p()),w.addGetterSetter(Nt,"fillPatternOffsetY",0,p()),w.addComponentsGetterSetter(Nt,"fillPatternScale",["x","y"]),w.addGetterSetter(Nt,"fillPatternScaleX",1,p()),w.addGetterSetter(Nt,"fillPatternScaleY",1,p()),w.addComponentsGetterSetter(Nt,"fillLinearGradientStartPoint",["x","y"]),w.addComponentsGetterSetter(Nt,"strokeLinearGradientStartPoint",["x","y"]),w.addGetterSetter(Nt,"fillLinearGradientStartPointX",0),w.addGetterSetter(Nt,"strokeLinearGradientStartPointX",0),w.addGetterSetter(Nt,"fillLinearGradientStartPointY",0),w.addGetterSetter(Nt,"strokeLinearGradientStartPointY",0),w.addComponentsGetterSetter(Nt,"fillLinearGradientEndPoint",["x","y"]),w.addComponentsGetterSetter(Nt,"strokeLinearGradientEndPoint",["x","y"]),w.addGetterSetter(Nt,"fillLinearGradientEndPointX",0),w.addGetterSetter(Nt,"strokeLinearGradientEndPointX",0),w.addGetterSetter(Nt,"fillLinearGradientEndPointY",0),w.addGetterSetter(Nt,"strokeLinearGradientEndPointY",0),w.addComponentsGetterSetter(Nt,"fillRadialGradientStartPoint",["x","y"]),w.addGetterSetter(Nt,"fillRadialGradientStartPointX",0),w.addGetterSetter(Nt,"fillRadialGradientStartPointY",0),w.addComponentsGetterSetter(Nt,"fillRadialGradientEndPoint",["x","y"]),w.addGetterSetter(Nt,"fillRadialGradientEndPointX",0),w.addGetterSetter(Nt,"fillRadialGradientEndPointY",0),w.addGetterSetter(Nt,"fillPatternRotation",0),w.addGetterSetter(Nt,"fillRule",void 0,y()),w.backCompat(Nt,{dashArray:"dash",getDashArray:"getDash",setDashArray:"getDash",drawFunc:"sceneFunc",getDrawFunc:"getSceneFunc",setDrawFunc:"setSceneFunc",drawHitFunc:"hitFunc",getDrawHitFunc:"getHitFunc",setDrawHitFunc:"setHitFunc"});const Ht=[{x:0,y:0},{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],zt=Ht.length;class Wt extends Q{constructor(t){super(t),this.canvas=new G,this.hitCanvas=new R({pixelRatio:1}),this._waitingForDraw=!1,this.on("visibleChange.konva",this._checkVisibility),this._checkVisibility(),this.on("imageSmoothingEnabledChange.konva",this._setSmoothEnabled),this._setSmoothEnabled()}createPNGStream(){return this.canvas._canvas.createPNGStream()}getCanvas(){return this.canvas}getNativeCanvasElement(){return this.canvas._canvas}getHitCanvas(){return this.hitCanvas}getContext(){return this.getCanvas().getContext()}clear(t){return this.getContext().clear(t),this.getHitCanvas().getContext().clear(t),this}setZIndex(t){super.setZIndex(t);const e=this.getStage();return e&&e.content&&(e.content.removeChild(this.getNativeCanvasElement()),t<e.children.length-1?e.content.insertBefore(this.getNativeCanvasElement(),e.children[t+1].getCanvas()._canvas):e.content.appendChild(this.getNativeCanvasElement())),this}moveToTop(){V.prototype.moveToTop.call(this);const t=this.getStage();return t&&t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.appendChild(this.getNativeCanvasElement())),!0}moveUp(){if(!V.prototype.moveUp.call(this))return!1;const t=this.getStage();return!(!t||!t.content)&&(t.content.removeChild(this.getNativeCanvasElement()),this.index<t.children.length-1?t.content.insertBefore(this.getNativeCanvasElement(),t.children[this.index+1].getCanvas()._canvas):t.content.appendChild(this.getNativeCanvasElement()),!0)}moveDown(){if(V.prototype.moveDown.call(this)){const t=this.getStage();if(t){const e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[this.index+1].getCanvas()._canvas))}return!0}return!1}moveToBottom(){if(V.prototype.moveToBottom.call(this)){const t=this.getStage();if(t){const e=t.children;t.content&&(t.content.removeChild(this.getNativeCanvasElement()),t.content.insertBefore(this.getNativeCanvasElement(),e[1].getCanvas()._canvas))}return!0}return!1}getLayer(){return this}remove(){const t=this.getNativeCanvasElement();return V.prototype.remove.call(this),t&&t.parentNode&&g._isInDocument(t)&&t.parentNode.removeChild(t),this}getStage(){return this.parent}setSize({width:t,height:e}){return this.canvas.setSize(t,e),this.hitCanvas.setSize(t,e),this._setSmoothEnabled(),this}_validateAdd(t){const e=t.getType();"Group"!==e&&"Shape"!==e&&g.throw("You may only add groups and shapes to a layer.")}_toKonvaCanvas(t){return(t=t||{}).width=t.width||this.getWidth(),t.height=t.height||this.getHeight(),t.x=void 0!==t.x?t.x:this.x(),t.y=void 0!==t.y?t.y:this.y(),V.prototype._toKonvaCanvas.call(this,t)}_checkVisibility(){const t=this.visible();this.canvas._canvas.style.display=t?"block":"none"}_setSmoothEnabled(){this.getContext()._context.imageSmoothingEnabled=this.imageSmoothingEnabled()}getWidth(){if(this.parent)return this.parent.width()}setWidth(){g.warn('Can not change width of layer. Use "stage.width(value)" function instead.')}getHeight(){if(this.parent)return this.parent.height()}setHeight(){g.warn('Can not change height of layer. Use "stage.height(value)" function instead.')}batchDraw(){return this._waitingForDraw||(this._waitingForDraw=!0,g.requestAnimFrame((()=>{this.draw(),this._waitingForDraw=!1}))),this}getIntersection(t){if(!this.isListening()||!this.isVisible())return null;let e=1,i=!1;for(;;){for(let n=0;n<zt;n++){const s=Ht[n],r=this._getIntersection({x:t.x+s.x*e,y:t.y+s.y*e}),a=r.shape;if(a)return a;if(i=!!r.antialiased,!r.antialiased)break}if(!i)return null;e+=1}}_getIntersection(t){const e=this.hitCanvas.pixelRatio,i=this.hitCanvas.context.getImageData(Math.round(t.x*e),Math.round(t.y*e),1,1).data,n=i[3];if(255===n){const t=g._rgbToHex(i[0],i[1],i[2]),e=Bt["#"+t];return e?{shape:e}:{antialiased:!0}}return n>0?{antialiased:!0}:{}}drawScene(t,e){const i=this.getLayer(),n=t||i&&i.getCanvas();return this._fire("beforeDraw",{node:this}),this.clearBeforeDraw()&&n.getContext().clear(),Q.prototype.drawScene.call(this,n,e),this._fire("draw",{node:this}),this}drawHit(t,e){const i=this.getLayer(),n=t||i&&i.hitCanvas;return i&&i.clearBeforeDraw()&&i.getHitCanvas().getContext().clear(),Q.prototype.drawHit.call(this,n,e),this}enableHitGraph(){return this.hitGraphEnabled(!0),this}disableHitGraph(){return this.hitGraphEnabled(!1),this}setHitGraphEnabled(t){g.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening(t)}getHitGraphEnabled(t){return g.warn("hitGraphEnabled method is deprecated. Please use layer.listening() instead."),this.listening()}toggleHitCanvas(){if(!this.parent||!this.parent.content)return;const t=this.parent;!!this.hitCanvas._canvas.parentNode?t.content.removeChild(this.hitCanvas._canvas):t.content.appendChild(this.hitCanvas._canvas)}destroy(){return g.releaseCanvas(this.getNativeCanvasElement(),this.getHitCanvas()._canvas),super.destroy()}}Wt.prototype.nodeType="Layer",n(Wt),w.addGetterSetter(Wt,"imageSmoothingEnabled",!0),w.addGetterSetter(Wt,"clearBeforeDraw",!0),w.addGetterSetter(Wt,"hitGraphEnabled",!0,x());class Yt extends Wt{constructor(t){super(t),this.listening(!1),g.warn('Konva.Fast layer is deprecated. Please use "new Konva.Layer({ listening: false })" instead.')}}Yt.prototype.nodeType="FastLayer",n(Yt);class Xt extends Q{_validateAdd(t){const e=t.getType();"Group"!==e&&"Shape"!==e&&g.throw("You may only add groups and shapes to groups.")}}Xt.prototype.nodeType="Group",n(Xt);const jt=e.performance&&e.performance.now?function(){return e.performance.now()}:function(){return(new Date).getTime()};class qt{constructor(t,e){this.id=qt.animIdCounter++,this.frame={time:0,timeDiff:0,lastTime:jt(),frameRate:0},this.func=t,this.setLayers(e)}setLayers(t){let e=[];return t&&(e=Array.isArray(t)?t:[t]),this.layers=e,this}getLayers(){return this.layers}addLayer(t){const e=this.layers,i=e.length;for(let n=0;n<i;n++)if(e[n]._id===t._id)return!1;return this.layers.push(t),!0}isRunning(){const t=qt.animations,e=t.length;for(let i=0;i<e;i++)if(t[i].id===this.id)return!0;return!1}start(){return this.stop(),this.frame.timeDiff=0,this.frame.lastTime=jt(),qt._addAnimation(this),this}stop(){return qt._removeAnimation(this),this}_updateFrameObject(t){this.frame.timeDiff=t-this.frame.lastTime,this.frame.lastTime=t,this.frame.time+=this.frame.timeDiff,this.frame.frameRate=1e3/this.frame.timeDiff}static _addAnimation(t){this.animations.push(t),this._handleAnimation()}static _removeAnimation(t){const e=t.id,i=this.animations,n=i.length;for(let t=0;t<n;t++)if(i[t].id===e){this.animations.splice(t,1);break}}static _runFrames(){const t={},e=this.animations;for(let i=0;i<e.length;i++){const n=e[i],s=n.layers,r=n.func;n._updateFrameObject(jt());const a=s.length;let o;if(o=!r||!1!==r.call(n,n.frame),o)for(let e=0;e<a;e++){const i=s[e];void 0!==i._id&&(t[i._id]=i)}}for(const e in t)t.hasOwnProperty(e)&&t[e].batchDraw()}static _animationLoop(){const t=qt;t.animations.length?(t._runFrames(),g.requestAnimFrame(t._animationLoop)):t.animRunning=!1}static _handleAnimation(){this.animRunning||(this.animRunning=!0,g.requestAnimFrame(this._animationLoop))}}qt.animations=[],qt.animIdCounter=0,qt.animRunning=!1;const Ut={node:1,duration:1,easing:1,onFinish:1,yoyo:1},Vt=["fill","stroke","shadowColor"];let Kt=0;class Qt{constructor(t,e,i,n,s,r,a){this.prop=t,this.propFunc=e,this.begin=n,this._pos=n,this.duration=r,this._change=0,this.prevPos=0,this.yoyo=a,this._time=0,this._position=0,this._startTime=0,this._finish=0,this.func=i,this._change=s-this.begin,this.pause()}fire(t){const e=this[t];e&&e()}setTime(t){t>this.duration?this.yoyo?(this._time=this.duration,this.reverse()):this.finish():t<0?this.yoyo?(this._time=0,this.play()):this.reset():(this._time=t,this.update())}getTime(){return this._time}setPosition(t){this.prevPos=this._pos,this.propFunc(t),this._pos=t}getPosition(t){return void 0===t&&(t=this._time),this.func(t,this.begin,this._change,this.duration)}play(){this.state=2,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onPlay")}reverse(){this.state=3,this._time=this.duration-this._time,this._startTime=this.getTimer()-this._time,this.onEnterFrame(),this.fire("onReverse")}seek(t){this.pause(),this._time=t,this.update(),this.fire("onSeek")}reset(){this.pause(),this._time=0,this.update(),this.fire("onReset")}finish(){this.pause(),this._time=this.duration,this.update(),this.fire("onFinish")}update(){this.setPosition(this.getPosition(this._time)),this.fire("onUpdate")}onEnterFrame(){const t=this.getTimer()-this._startTime;2===this.state?this.setTime(t):3===this.state&&this.setTime(this.duration-t)}pause(){this.state=1,this.fire("onPause")}getTimer(){return(new Date).getTime()}}class Jt{constructor(t){const e=this,n=t.node,s=n._id,r=t.easing||$t.Linear,a=!!t.yoyo;let o,h;o=void 0===t.duration?.3:0===t.duration?.001:t.duration,this.node=n,this._id=Kt++;const l=n.getLayer()||(n instanceof i.Stage?n.getLayers():null);for(h in l||g.error("Tween constructor have `node` that is not in a layer. Please add node into layer first."),this.anim=new qt((function(){e.tween.onEnterFrame()}),l),this.tween=new Qt(h,(function(t){e._tweenFunc(t)}),r,0,1,1e3*o,a),this._addListeners(),Jt.attrs[s]||(Jt.attrs[s]={}),Jt.attrs[s][this._id]||(Jt.attrs[s][this._id]={}),Jt.tweens[s]||(Jt.tweens[s]={}),t)void 0===Ut[h]&&this._addAttr(h,t[h]);this.reset(),this.onFinish=t.onFinish,this.onReset=t.onReset,this.onUpdate=t.onUpdate}_addAttr(t,e){const i=this.node,n=i._id;let s,r,a,o,h;const l=Jt.tweens[n][t];l&&delete Jt.attrs[n][l][t];let d=i.getAttr(t);if(g._isArray(e))if(s=[],r=Math.max(e.length,d.length),"points"===t&&e.length!==d.length&&(e.length>d.length?(o=d,d=g._prepareArrayForTween(d,e,i.closed())):(a=e,e=g._prepareArrayForTween(e,d,i.closed()))),0===t.indexOf("fill"))for(let t=0;t<r;t++)if(t%2==0)s.push(e[t]-d[t]);else{const i=g.colorToRGBA(d[t]);h=g.colorToRGBA(e[t]),d[t]=i,s.push({r:h.r-i.r,g:h.g-i.g,b:h.b-i.b,a:h.a-i.a})}else for(let t=0;t<r;t++)s.push(e[t]-d[t]);else-1!==Vt.indexOf(t)?(d=g.colorToRGBA(d),h=g.colorToRGBA(e),s={r:h.r-d.r,g:h.g-d.g,b:h.b-d.b,a:h.a-d.a}):s=e-d;Jt.attrs[n][this._id][t]={start:d,diff:s,end:e,trueEnd:a,trueStart:o},Jt.tweens[n][t]=this._id}_tweenFunc(t){const e=this.node,i=Jt.attrs[e._id][this._id];let n,s,r,a,o,h,l,d;for(n in i){if(s=i[n],r=s.start,a=s.diff,d=s.end,g._isArray(r))if(o=[],l=Math.max(r.length,d.length),0===n.indexOf("fill"))for(h=0;h<l;h++)h%2==0?o.push((r[h]||0)+a[h]*t):o.push("rgba("+Math.round(r[h].r+a[h].r*t)+","+Math.round(r[h].g+a[h].g*t)+","+Math.round(r[h].b+a[h].b*t)+","+(r[h].a+a[h].a*t)+")");else for(h=0;h<l;h++)o.push((r[h]||0)+a[h]*t);else o=-1!==Vt.indexOf(n)?"rgba("+Math.round(r.r+a.r*t)+","+Math.round(r.g+a.g*t)+","+Math.round(r.b+a.b*t)+","+(r.a+a.a*t)+")":r+a*t;e.setAttr(n,o)}}_addListeners(){this.tween.onPlay=()=>{this.anim.start()},this.tween.onReverse=()=>{this.anim.start()},this.tween.onPause=()=>{this.anim.stop()},this.tween.onFinish=()=>{const t=this.node,e=Jt.attrs[t._id][this._id];e.points&&e.points.trueEnd&&t.setAttr("points",e.points.trueEnd),this.onFinish&&this.onFinish.call(this)},this.tween.onReset=()=>{const t=this.node,e=Jt.attrs[t._id][this._id];e.points&&e.points.trueStart&&t.points(e.points.trueStart),this.onReset&&this.onReset()},this.tween.onUpdate=()=>{this.onUpdate&&this.onUpdate.call(this)}}play(){return this.tween.play(),this}reverse(){return this.tween.reverse(),this}reset(){return this.tween.reset(),this}seek(t){return this.tween.seek(1e3*t),this}pause(){return this.tween.pause(),this}finish(){return this.tween.finish(),this}destroy(){const t=this.node._id,e=this._id,i=Jt.tweens[t];this.pause();for(const e in i)delete Jt.tweens[t][e];delete Jt.attrs[t][e]}}Jt.attrs={},Jt.tweens={},V.prototype.to=function(t){const e=t.onFinish;t.node=this,t.onFinish=function(){this.destroy(),e&&e()};new Jt(t).play()};const $t={BackEaseIn(t,e,i,n){const s=1.70158;return i*(t/=n)*t*((s+1)*t-s)+e},BackEaseOut(t,e,i,n){const s=1.70158;return i*((t=t/n-1)*t*((s+1)*t+s)+1)+e},BackEaseInOut(t,e,i,n){let s=1.70158;return(t/=n/2)<1?i/2*(t*t*((1+(s*=1.525))*t-s))+e:i/2*((t-=2)*t*((1+(s*=1.525))*t+s)+2)+e},ElasticEaseIn(t,e,i,n,s,r){let a=0;return 0===t?e:1==(t/=n)?e+i:(r||(r=.3*n),!s||s<Math.abs(i)?(s=i,a=r/4):a=r/(2*Math.PI)*Math.asin(i/s),-s*Math.pow(2,10*(t-=1))*Math.sin((t*n-a)*(2*Math.PI)/r)+e)},ElasticEaseOut(t,e,i,n,s,r){let a=0;return 0===t?e:1==(t/=n)?e+i:(r||(r=.3*n),!s||s<Math.abs(i)?(s=i,a=r/4):a=r/(2*Math.PI)*Math.asin(i/s),s*Math.pow(2,-10*t)*Math.sin((t*n-a)*(2*Math.PI)/r)+i+e)},ElasticEaseInOut(t,e,i,n,s,r){let a=0;return 0===t?e:2==(t/=n/2)?e+i:(r||(r=n*(.3*1.5)),!s||s<Math.abs(i)?(s=i,a=r/4):a=r/(2*Math.PI)*Math.asin(i/s),t<1?s*Math.pow(2,10*(t-=1))*Math.sin((t*n-a)*(2*Math.PI)/r)*-.5+e:s*Math.pow(2,-10*(t-=1))*Math.sin((t*n-a)*(2*Math.PI)/r)*.5+i+e)},BounceEaseOut:(t,e,i,n)=>(t/=n)<1/2.75?i*(7.5625*t*t)+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e,BounceEaseIn:(t,e,i,n)=>i-$t.BounceEaseOut(n-t,0,i,n)+e,BounceEaseInOut:(t,e,i,n)=>t<n/2?.5*$t.BounceEaseIn(2*t,0,i,n)+e:.5*$t.BounceEaseOut(2*t-n,0,i,n)+.5*i+e,EaseIn:(t,e,i,n)=>i*(t/=n)*t+e,EaseOut:(t,e,i,n)=>-i*(t/=n)*(t-2)+e,EaseInOut:(t,e,i,n)=>(t/=n/2)<1?i/2*t*t+e:-i/2*(--t*(t-2)-1)+e,StrongEaseIn:(t,e,i,n)=>i*(t/=n)*t*t*t*t+e,StrongEaseOut:(t,e,i,n)=>i*((t=t/n-1)*t*t*t*t+1)+e,StrongEaseInOut:(t,e,i,n)=>(t/=n/2)<1?i/2*t*t*t*t*t+e:i/2*((t-=2)*t*t*t*t+2)+e,Linear:(t,e,i,n)=>i*t/n+e},Zt=g._assign(i,{Util:g,Transform:s,Node:V,Container:Q,Stage:Gt,stages:Mt,Layer:Wt,FastLayer:Yt,Group:Xt,DD:E,Shape:Nt,shapes:Bt,Animation:qt,Tween:Jt,Easings:$t,Context:P,Canvas:M});class te extends Nt{_sceneFunc(t){const e=i.getAngle(this.angle()),n=this.clockwise();t.beginPath(),t.arc(0,0,this.outerRadius(),0,e,n),t.arc(0,0,this.innerRadius(),e,0,!n),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}getSelfRect(){const t=this.innerRadius(),e=this.outerRadius(),n=this.clockwise(),s=i.getAngle(n?360-this.angle():this.angle()),r=Math.cos(Math.min(s,Math.PI)),a=Math.sin(Math.min(Math.max(Math.PI,s),3*Math.PI/2)),o=Math.sin(Math.min(s,Math.PI/2)),h=r*(r>0?t:e),l=a*(a>0?t:e),d=o*(o>0?e:t);return{x:h,y:n?-1*d:l,width:1*e-h,height:d-l}}}function ee(t,e,i,n,s,r,a){const o=Math.sqrt(Math.pow(i-t,2)+Math.pow(n-e,2)),h=Math.sqrt(Math.pow(s-i,2)+Math.pow(r-n,2)),l=a*o/(o+h),d=a*h/(o+h);return[i-l*(s-t),n-l*(r-e),i+d*(s-t),n+d*(r-e)]}function ie(t,e){const i=t.length,n=[];for(let s=2;s<i-2;s+=2){const i=ee(t[s-2],t[s-1],t[s],t[s+1],t[s+2],t[s+3],e);isNaN(i[0])||(n.push(i[0]),n.push(i[1]),n.push(t[s]),n.push(t[s+1]),n.push(i[2]),n.push(i[3]))}return n}te.prototype._centroid=!0,te.prototype.className="Arc",te.prototype._attrsAffectingSize=["innerRadius","outerRadius","angle","clockwise"],n(te),w.addGetterSetter(te,"innerRadius",0,p()),w.addGetterSetter(te,"outerRadius",0,p()),w.addGetterSetter(te,"angle",0,p()),w.addGetterSetter(te,"clockwise",!1,x());class ne extends Nt{constructor(t){super(t),this.on("pointsChange.konva tensionChange.konva closedChange.konva bezierChange.konva",(function(){this._clearCache("tensionPoints")}))}_sceneFunc(t){let e,i,n,s=this.points(),r=s.length,a=this.tension(),o=this.closed(),h=this.bezier();if(r){if(t.beginPath(),t.moveTo(s[0],s[1]),0!==a&&r>4){for(e=this.getTensionPoints(),i=e.length,n=o?0:4,o||t.quadraticCurveTo(e[0],e[1],e[2],e[3]);n<i-2;)t.bezierCurveTo(e[n++],e[n++],e[n++],e[n++],e[n++],e[n++]);o||t.quadraticCurveTo(e[i-2],e[i-1],s[r-2],s[r-1])}else if(h)for(n=2;n<r;)t.bezierCurveTo(s[n++],s[n++],s[n++],s[n++],s[n++],s[n++]);else for(n=2;n<r;n+=2)t.lineTo(s[n],s[n+1]);o?(t.closePath(),t.fillStrokeShape(this)):t.strokeShape(this)}}getTensionPoints(){return this._getCache("tensionPoints",this._getTensionPoints)}_getTensionPoints(){return this.closed()?this._getTensionPointsClosed():ie(this.points(),this.tension())}_getTensionPointsClosed(){const t=this.points(),e=t.length,i=this.tension(),n=ee(t[e-2],t[e-1],t[0],t[1],t[2],t[3],i),s=ee(t[e-4],t[e-3],t[e-2],t[e-1],t[0],t[1],i),r=ie(t,i);return[n[2],n[3]].concat(r).concat([s[0],s[1],t[e-2],t[e-1],s[2],s[3],n[0],n[1],t[0],t[1]])}getWidth(){return this.getSelfRect().width}getHeight(){return this.getSelfRect().height}getSelfRect(){let t=this.points();if(t.length<4)return{x:t[0]||0,y:t[1]||0,width:0,height:0};t=0!==this.tension()?[t[0],t[1],...this._getTensionPoints(),t[t.length-2],t[t.length-1]]:this.points();let e,i,n=t[0],s=t[0],r=t[1],a=t[1];for(let o=0;o<t.length/2;o++)e=t[2*o],i=t[2*o+1],n=Math.min(n,e),s=Math.max(s,e),r=Math.min(r,i),a=Math.max(a,i);return{x:n,y:r,width:s-n,height:a-r}}}ne.prototype.className="Line",ne.prototype._attrsAffectingSize=["points","bezier","tension"],n(ne),w.addGetterSetter(ne,"closed",!1),w.addGetterSetter(ne,"bezier",!1),w.addGetterSetter(ne,"tension",0,p()),w.addGetterSetter(ne,"points",[],function(){if(i.isUnminified)return function(t,e){const i=Int8Array?Object.getPrototypeOf(Int8Array):null;return i&&t instanceof i||(g._isArray(t)?t.forEach((function(t){g._isNumber(t)||g.warn('"'+e+'" attribute has non numeric element '+t+". Make sure that all elements are numbers.")})):g.warn(u(t)+' is a not valid value for "'+e+'" attribute. The value should be a array of numbers.')),t}}());const se=[[],[],[-.5773502691896257,.5773502691896257],[0,-.7745966692414834,.7745966692414834],[-.33998104358485626,.33998104358485626,-.8611363115940526,.8611363115940526],[0,-.5384693101056831,.5384693101056831,-.906179845938664,.906179845938664],[.6612093864662645,-.6612093864662645,-.2386191860831969,.2386191860831969,-.932469514203152,.932469514203152],[0,.4058451513773972,-.4058451513773972,-.7415311855993945,.7415311855993945,-.9491079123427585,.9491079123427585],[-.1834346424956498,.1834346424956498,-.525532409916329,.525532409916329,-.7966664774136267,.7966664774136267,-.9602898564975363,.9602898564975363],[0,-.8360311073266358,.8360311073266358,-.9681602395076261,.9681602395076261,-.3242534234038089,.3242534234038089,-.6133714327005904,.6133714327005904],[-.14887433898163122,.14887433898163122,-.4333953941292472,.4333953941292472,-.6794095682990244,.6794095682990244,-.8650633666889845,.8650633666889845,-.9739065285171717,.9739065285171717],[0,-.26954315595234496,.26954315595234496,-.5190961292068118,.5190961292068118,-.7301520055740494,.7301520055740494,-.8870625997680953,.8870625997680953,-.978228658146057,.978228658146057],[-.1252334085114689,.1252334085114689,-.3678314989981802,.3678314989981802,-.5873179542866175,.5873179542866175,-.7699026741943047,.7699026741943047,-.9041172563704749,.9041172563704749,-.9815606342467192,.9815606342467192],[0,-.2304583159551348,.2304583159551348,-.44849275103644687,.44849275103644687,-.6423493394403402,.6423493394403402,-.8015780907333099,.8015780907333099,-.9175983992229779,.9175983992229779,-.9841830547185881,.9841830547185881],[-.10805494870734367,.10805494870734367,-.31911236892788974,.31911236892788974,-.5152486363581541,.5152486363581541,-.6872929048116855,.6872929048116855,-.827201315069765,.827201315069765,-.9284348836635735,.9284348836635735,-.9862838086968123,.9862838086968123],[0,-.20119409399743451,.20119409399743451,-.3941513470775634,.3941513470775634,-.5709721726085388,.5709721726085388,-.7244177313601701,.7244177313601701,-.8482065834104272,.8482065834104272,-.937273392400706,.937273392400706,-.9879925180204854,.9879925180204854],[-.09501250983763744,.09501250983763744,-.2816035507792589,.2816035507792589,-.45801677765722737,.45801677765722737,-.6178762444026438,.6178762444026438,-.755404408355003,.755404408355003,-.8656312023878318,.8656312023878318,-.9445750230732326,.9445750230732326,-.9894009349916499,.9894009349916499],[0,-.17848418149584785,.17848418149584785,-.3512317634538763,.3512317634538763,-.5126905370864769,.5126905370864769,-.6576711592166907,.6576711592166907,-.7815140038968014,.7815140038968014,-.8802391537269859,.8802391537269859,-.9506755217687678,.9506755217687678,-.9905754753144174,.9905754753144174],[-.0847750130417353,.0847750130417353,-.2518862256915055,.2518862256915055,-.41175116146284263,.41175116146284263,-.5597708310739475,.5597708310739475,-.6916870430603532,.6916870430603532,-.8037049589725231,.8037049589725231,-.8926024664975557,.8926024664975557,-.9558239495713977,.9558239495713977,-.9915651684209309,.9915651684209309],[0,-.16035864564022537,.16035864564022537,-.31656409996362983,.31656409996362983,-.46457074137596094,.46457074137596094,-.600545304661681,.600545304661681,-.7209661773352294,.7209661773352294,-.8227146565371428,.8227146565371428,-.9031559036148179,.9031559036148179,-.96020815213483,.96020815213483,-.9924068438435844,.9924068438435844],[-.07652652113349734,.07652652113349734,-.22778585114164507,.22778585114164507,-.37370608871541955,.37370608871541955,-.5108670019508271,.5108670019508271,-.636053680726515,.636053680726515,-.7463319064601508,.7463319064601508,-.8391169718222188,.8391169718222188,-.912234428251326,.912234428251326,-.9639719272779138,.9639719272779138,-.9931285991850949,.9931285991850949],[0,-.1455618541608951,.1455618541608951,-.2880213168024011,.2880213168024011,-.4243421202074388,.4243421202074388,-.5516188358872198,.5516188358872198,-.6671388041974123,.6671388041974123,-.7684399634756779,.7684399634756779,-.8533633645833173,.8533633645833173,-.9200993341504008,.9200993341504008,-.9672268385663063,.9672268385663063,-.9937521706203895,.9937521706203895],[-.06973927331972223,.06973927331972223,-.20786042668822127,.20786042668822127,-.34193582089208424,.34193582089208424,-.469355837986757,.469355837986757,-.5876404035069116,.5876404035069116,-.6944872631866827,.6944872631866827,-.7878168059792081,.7878168059792081,-.8658125777203002,.8658125777203002,-.926956772187174,.926956772187174,-.9700604978354287,.9700604978354287,-.9942945854823992,.9942945854823992],[0,-.1332568242984661,.1332568242984661,-.26413568097034495,.26413568097034495,-.3903010380302908,.3903010380302908,-.5095014778460075,.5095014778460075,-.6196098757636461,.6196098757636461,-.7186613631319502,.7186613631319502,-.8048884016188399,.8048884016188399,-.8767523582704416,.8767523582704416,-.9329710868260161,.9329710868260161,-.9725424712181152,.9725424712181152,-.9947693349975522,.9947693349975522],[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213]],re=[[],[],[1,1],[.8888888888888888,.5555555555555556,.5555555555555556],[.6521451548625461,.6521451548625461,.34785484513745385,.34785484513745385],[.5688888888888889,.47862867049936647,.47862867049936647,.23692688505618908,.23692688505618908],[.3607615730481386,.3607615730481386,.46791393457269104,.46791393457269104,.17132449237917036,.17132449237917036],[.4179591836734694,.3818300505051189,.3818300505051189,.27970539148927664,.27970539148927664,.1294849661688697,.1294849661688697],[.362683783378362,.362683783378362,.31370664587788727,.31370664587788727,.22238103445337448,.22238103445337448,.10122853629037626,.10122853629037626],[.3302393550012598,.1806481606948574,.1806481606948574,.08127438836157441,.08127438836157441,.31234707704000286,.31234707704000286,.26061069640293544,.26061069640293544],[.29552422471475287,.29552422471475287,.26926671930999635,.26926671930999635,.21908636251598204,.21908636251598204,.1494513491505806,.1494513491505806,.06667134430868814,.06667134430868814],[.2729250867779006,.26280454451024665,.26280454451024665,.23319376459199048,.23319376459199048,.18629021092773426,.18629021092773426,.1255803694649046,.1255803694649046,.05566856711617366,.05566856711617366],[.24914704581340277,.24914704581340277,.2334925365383548,.2334925365383548,.20316742672306592,.20316742672306592,.16007832854334622,.16007832854334622,.10693932599531843,.10693932599531843,.04717533638651183,.04717533638651183],[.2325515532308739,.22628318026289723,.22628318026289723,.2078160475368885,.2078160475368885,.17814598076194574,.17814598076194574,.13887351021978725,.13887351021978725,.09212149983772845,.09212149983772845,.04048400476531588,.04048400476531588],[.2152638534631578,.2152638534631578,.2051984637212956,.2051984637212956,.18553839747793782,.18553839747793782,.15720316715819355,.15720316715819355,.12151857068790319,.12151857068790319,.08015808715976021,.08015808715976021,.03511946033175186,.03511946033175186],[.2025782419255613,.19843148532711158,.19843148532711158,.1861610000155622,.1861610000155622,.16626920581699392,.16626920581699392,.13957067792615432,.13957067792615432,.10715922046717194,.10715922046717194,.07036604748810812,.07036604748810812,.03075324199611727,.03075324199611727],[.1894506104550685,.1894506104550685,.18260341504492358,.18260341504492358,.16915651939500254,.16915651939500254,.14959598881657674,.14959598881657674,.12462897125553388,.12462897125553388,.09515851168249279,.09515851168249279,.062253523938647894,.062253523938647894,.027152459411754096,.027152459411754096],[.17944647035620653,.17656270536699264,.17656270536699264,.16800410215645004,.16800410215645004,.15404576107681028,.15404576107681028,.13513636846852548,.13513636846852548,.11188384719340397,.11188384719340397,.08503614831717918,.08503614831717918,.0554595293739872,.0554595293739872,.02414830286854793,.02414830286854793],[.1691423829631436,.1691423829631436,.16427648374583273,.16427648374583273,.15468467512626524,.15468467512626524,.14064291467065065,.14064291467065065,.12255520671147846,.12255520671147846,.10094204410628717,.10094204410628717,.07642573025488905,.07642573025488905,.0497145488949698,.0497145488949698,.02161601352648331,.02161601352648331],[.1610544498487837,.15896884339395434,.15896884339395434,.15276604206585967,.15276604206585967,.1426067021736066,.1426067021736066,.12875396253933621,.12875396253933621,.11156664554733399,.11156664554733399,.09149002162245,.09149002162245,.06904454273764123,.06904454273764123,.0448142267656996,.0448142267656996,.019461788229726478,.019461788229726478],[.15275338713072584,.15275338713072584,.14917298647260374,.14917298647260374,.14209610931838204,.14209610931838204,.13168863844917664,.13168863844917664,.11819453196151841,.11819453196151841,.10193011981724044,.10193011981724044,.08327674157670475,.08327674157670475,.06267204833410907,.06267204833410907,.04060142980038694,.04060142980038694,.017614007139152118,.017614007139152118],[.14608113364969041,.14452440398997005,.14452440398997005,.13988739479107315,.13988739479107315,.13226893863333747,.13226893863333747,.12183141605372853,.12183141605372853,.10879729916714838,.10879729916714838,.09344442345603386,.09344442345603386,.0761001136283793,.0761001136283793,.057134425426857205,.057134425426857205,.036953789770852494,.036953789770852494,.016017228257774335,.016017228257774335],[.13925187285563198,.13925187285563198,.13654149834601517,.13654149834601517,.13117350478706238,.13117350478706238,.12325237681051242,.12325237681051242,.11293229608053922,.11293229608053922,.10041414444288096,.10041414444288096,.08594160621706773,.08594160621706773,.06979646842452049,.06979646842452049,.052293335152683286,.052293335152683286,.03377490158481415,.03377490158481415,.0146279952982722,.0146279952982722],[.13365457218610619,.1324620394046966,.1324620394046966,.12890572218808216,.12890572218808216,.12304908430672953,.12304908430672953,.11499664022241136,.11499664022241136,.10489209146454141,.10489209146454141,.09291576606003515,.09291576606003515,.07928141177671895,.07928141177671895,.06423242140852585,.06423242140852585,.04803767173108467,.04803767173108467,.030988005856979445,.030988005856979445,.013411859487141771,.013411859487141771],[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872]],ae=[[1],[1,1],[1,2,1],[1,3,3,1]],oe=(t,e,i)=>{let n,s;const r=i/2;n=0;for(let i=0;i<20;i++)s=r*se[20][i]+r,n+=re[20][i]*le(t,e,s);return r*n},he=(t,e,i)=>{void 0===i&&(i=1);const n=t[0]-2*t[1]+t[2],s=e[0]-2*e[1]+e[2],r=2*t[1]-2*t[0],a=2*e[1]-2*e[0],o=4*(n*n+s*s),h=4*(n*r+s*a),l=r*r+a*a;if(0===o)return i*Math.sqrt(Math.pow(t[2]-t[0],2)+Math.pow(e[2]-e[0],2));const d=h/(2*o),c=i+d,g=l/o-d*d,u=c*c+g>0?Math.sqrt(c*c+g):0,f=d*d+g>0?Math.sqrt(d*d+g):0,p=d+Math.sqrt(d*d+g)!==0?g*Math.log(Math.abs((c+u)/(d+f))):0;return Math.sqrt(o)/2*(c*u-d*f+p)};function le(t,e,i){const n=de(1,i,t),s=de(1,i,e),r=n*n+s*s;return Math.sqrt(r)}const de=(t,e,i)=>{const n=i.length-1;let s,r;if(0===n)return 0;if(0===t){r=0;for(let t=0;t<=n;t++)r+=ae[n][t]*Math.pow(1-e,n-t)*Math.pow(e,t)*i[t];return r}s=new Array(n);for(let t=0;t<n;t++)s[t]=n*(i[t+1]-i[t]);return de(t-1,e,s)},ce=(t,e,i)=>{let n=1,s=t/e,r=(t-i(s))/e,a=0;for(;n>.001;){const o=i(s+r),h=Math.abs(t-o)/e;if(h<n)n=h,s+=r;else{const a=i(s-r),o=Math.abs(t-a)/e;o<n?(n=o,s-=r):r/=2}if(a++,a>500)break}return s};class ge extends Nt{constructor(t){super(t),this.dataArray=[],this.pathLength=0,this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute()}))}_readDataAttribute(){this.dataArray=ge.parsePathData(this.data()),this.pathLength=ge.getPathLength(this.dataArray)}_sceneFunc(t){const e=this.dataArray;t.beginPath();let i=!1;for(let f=0;f<e.length;f++){const p=e[f].command,m=e[f].points;switch(p){case"L":t.lineTo(m[0],m[1]);break;case"M":t.moveTo(m[0],m[1]);break;case"C":t.bezierCurveTo(m[0],m[1],m[2],m[3],m[4],m[5]);break;case"Q":t.quadraticCurveTo(m[0],m[1],m[2],m[3]);break;case"A":var n=m[0],s=m[1],r=m[2],a=m[3],o=m[4],h=m[5],l=m[6],d=m[7],c=r>a?r:a,g=r>a?1:r/a,u=r>a?a/r:1;t.translate(n,s),t.rotate(l),t.scale(g,u),t.arc(0,0,c,o,o+h,1-d),t.scale(1/g,1/u),t.rotate(-l),t.translate(-n,-s);break;case"z":i=!0,t.closePath()}}i||this.hasFill()?t.fillStrokeShape(this):t.strokeShape(this)}getSelfRect(){let t=[];this.dataArray.forEach((function(e){if("A"===e.command){const i=e.points[4],n=e.points[5],s=e.points[4]+n;let r=Math.PI/180;if(Math.abs(i-s)<r&&(r=Math.abs(i-s)),n<0)for(let n=i-r;n>s;n-=r){const i=ge.getPointOnEllipticalArc(e.points[0],e.points[1],e.points[2],e.points[3],n,0);t.push(i.x,i.y)}else for(let n=i+r;n<s;n+=r){const i=ge.getPointOnEllipticalArc(e.points[0],e.points[1],e.points[2],e.points[3],n,0);t.push(i.x,i.y)}}else if("C"===e.command)for(let i=0;i<=1;i+=.01){const n=ge.getPointOnCubicBezier(i,e.start.x,e.start.y,e.points[0],e.points[1],e.points[2],e.points[3],e.points[4],e.points[5]);t.push(n.x,n.y)}else t=t.concat(e.points)}));let e,i,n=t[0],s=t[0],r=t[1],a=t[1];for(let o=0;o<t.length/2;o++)e=t[2*o],i=t[2*o+1],isNaN(e)||(n=Math.min(n,e),s=Math.max(s,e)),isNaN(i)||(r=Math.min(r,i),a=Math.max(a,i));return{x:n,y:r,width:s-n,height:a-r}}getLength(){return this.pathLength}getPointAtLength(t){return ge.getPointAtLengthOfDataArray(t,this.dataArray)}static getLineLength(t,e,i,n){return Math.sqrt((i-t)*(i-t)+(n-e)*(n-e))}static getPathLength(t){let e=0;for(let i=0;i<t.length;++i)e+=t[i].pathLength;return e}static getPointAtLengthOfDataArray(t,e){let i,n=0,s=e.length;if(!s)return null;for(;n<s&&t>e[n].pathLength;)t-=e[n].pathLength,++n;if(n===s)return i=e[n-1].points.slice(-2),{x:i[0],y:i[1]};if(t<.01)return i=e[n].points.slice(0,2),{x:i[0],y:i[1]};const r=e[n],a=r.points;switch(r.command){case"L":return ge.getPointOnLine(t,r.start.x,r.start.y,a[0],a[1]);case"C":return ge.getPointOnCubicBezier(ce(t,ge.getPathLength(e),(t=>oe([r.start.x,a[0],a[2],a[4]],[r.start.y,a[1],a[3],a[5]],t))),r.start.x,r.start.y,a[0],a[1],a[2],a[3],a[4],a[5]);case"Q":return ge.getPointOnQuadraticBezier(ce(t,ge.getPathLength(e),(t=>he([r.start.x,a[0],a[2]],[r.start.y,a[1],a[3]],t))),r.start.x,r.start.y,a[0],a[1],a[2],a[3]);case"A":var o=a[0],h=a[1],l=a[2],d=a[3],c=a[4],g=a[5],u=a[6];return c+=g*t/r.pathLength,ge.getPointOnEllipticalArc(o,h,l,d,c,u)}return null}static getPointOnLine(t,e,i,n,s,r,a){r=null!=r?r:e,a=null!=a?a:i;const o=this.getLineLength(e,i,n,s);if(o<1e-10)return{x:e,y:i};if(n===e)return{x:r,y:a+(s>i?t:-t)};const h=(s-i)/(n-e),l=Math.sqrt(t*t/(1+h*h))*(n<e?-1:1),d=h*l;if(Math.abs(a-i-h*(r-e))<1e-10)return{x:r+l,y:a+d};const c=((r-e)*(n-e)+(a-i)*(s-i))/(o*o),g=e+c*(n-e),u=i+c*(s-i),f=this.getLineLength(r,a,g,u),p=Math.sqrt(t*t-f*f),m=Math.sqrt(p*p/(1+h*h))*(n<e?-1:1);return{x:g+m,y:u+h*m}}static getPointOnCubicBezier(t,e,i,n,s,r,a,o,h){function l(t){return t*t*t}function d(t){return 3*t*t*(1-t)}function c(t){return 3*t*(1-t)*(1-t)}function g(t){return(1-t)*(1-t)*(1-t)}return{x:o*l(t)+r*d(t)+n*c(t)+e*g(t),y:h*l(t)+a*d(t)+s*c(t)+i*g(t)}}static getPointOnQuadraticBezier(t,e,i,n,s,r,a){function o(t){return t*t}function h(t){return 2*t*(1-t)}function l(t){return(1-t)*(1-t)}return{x:r*o(t)+n*h(t)+e*l(t),y:a*o(t)+s*h(t)+i*l(t)}}static getPointOnEllipticalArc(t,e,i,n,s,r){const a=Math.cos(r),o=Math.sin(r),h=i*Math.cos(s),l=n*Math.sin(s);return{x:t+(h*a-l*o),y:e+(h*o+l*a)}}static parsePathData(t){if(!t)return[];let e=t;const i=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"];e=e.replace(new RegExp(" ","g"),",");for(var n=0;n<i.length;n++)e=e.replace(new RegExp(i[n],"g"),"|"+i[n]);const s=e.split("|"),r=[],a=[];let o=0,h=0;const l=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:e[-+]?\d+)?)/gi;let d;for(n=1;n<s.length;n++){let t=s[n],e=t.charAt(0);for(t=t.slice(1),a.length=0;d=l.exec(t);)a.push(d[0]);const i=[];for(let t=0,e=a.length;t<e;t++){if("00"===a[t]){i.push(0,0);continue}const e=parseFloat(a[t]);isNaN(e)?i.push(0):i.push(e)}for(;i.length>0&&!isNaN(i[0]);){let t="",n=[];const s=o,a=h;var c,g,u,f,p,m,_,y,v,x;switch(e){case"l":o+=i.shift(),h+=i.shift(),t="L",n.push(o,h);break;case"L":o=i.shift(),h=i.shift(),n.push(o,h);break;case"m":var b=i.shift(),S=i.shift();if(o+=b,h+=S,t="M",r.length>2&&"z"===r[r.length-1].command)for(let t=r.length-2;t>=0;t--)if("M"===r[t].command){o=r[t].points[0]+b,h=r[t].points[1]+S;break}n.push(o,h),e="l";break;case"M":o=i.shift(),h=i.shift(),t="M",n.push(o,h),e="L";break;case"h":o+=i.shift(),t="L",n.push(o,h);break;case"H":o=i.shift(),t="L",n.push(o,h);break;case"v":h+=i.shift(),t="L",n.push(o,h);break;case"V":h=i.shift(),t="L",n.push(o,h);break;case"C":n.push(i.shift(),i.shift(),i.shift(),i.shift()),o=i.shift(),h=i.shift(),n.push(o,h);break;case"c":n.push(o+i.shift(),h+i.shift(),o+i.shift(),h+i.shift()),o+=i.shift(),h+=i.shift(),t="C",n.push(o,h);break;case"S":g=o,u=h,"C"===(c=r[r.length-1]).command&&(g=o+(o-c.points[2]),u=h+(h-c.points[3])),n.push(g,u,i.shift(),i.shift()),o=i.shift(),h=i.shift(),t="C",n.push(o,h);break;case"s":g=o,u=h,"C"===(c=r[r.length-1]).command&&(g=o+(o-c.points[2]),u=h+(h-c.points[3])),n.push(g,u,o+i.shift(),h+i.shift()),o+=i.shift(),h+=i.shift(),t="C",n.push(o,h);break;case"Q":n.push(i.shift(),i.shift()),o=i.shift(),h=i.shift(),n.push(o,h);break;case"q":n.push(o+i.shift(),h+i.shift()),o+=i.shift(),h+=i.shift(),t="Q",n.push(o,h);break;case"T":g=o,u=h,"Q"===(c=r[r.length-1]).command&&(g=o+(o-c.points[0]),u=h+(h-c.points[1])),o=i.shift(),h=i.shift(),t="Q",n.push(g,u,o,h);break;case"t":g=o,u=h,"Q"===(c=r[r.length-1]).command&&(g=o+(o-c.points[0]),u=h+(h-c.points[1])),o+=i.shift(),h+=i.shift(),t="Q",n.push(g,u,o,h);break;case"A":f=i.shift(),p=i.shift(),m=i.shift(),_=i.shift(),y=i.shift(),v=o,x=h,o=i.shift(),h=i.shift(),t="A",n=this.convertEndpointToCenterParameterization(v,x,o,h,_,y,f,p,m);break;case"a":f=i.shift(),p=i.shift(),m=i.shift(),_=i.shift(),y=i.shift(),v=o,x=h,o+=i.shift(),h+=i.shift(),t="A",n=this.convertEndpointToCenterParameterization(v,x,o,h,_,y,f,p,m)}r.push({command:t||e,points:n,start:{x:s,y:a},pathLength:this.calcLength(s,a,t||e,n)})}"z"!==e&&"Z"!==e||r.push({command:"z",points:[],start:void 0,pathLength:0})}return r}static calcLength(t,e,i,n){let s,r,a,o;const h=ge;switch(i){case"L":return h.getLineLength(t,e,n[0],n[1]);case"C":return oe([t,n[0],n[2],n[4]],[e,n[1],n[3],n[5]],1);case"Q":return he([t,n[0],n[2]],[e,n[1],n[3]],1);case"A":s=0;var l=n[4],d=n[5],c=n[4]+d,g=Math.PI/180;if(Math.abs(l-c)<g&&(g=Math.abs(l-c)),r=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],l,0),d<0)for(o=l-g;o>c;o-=g)a=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],o,0),s+=h.getLineLength(r.x,r.y,a.x,a.y),r=a;else for(o=l+g;o<c;o+=g)a=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],o,0),s+=h.getLineLength(r.x,r.y,a.x,a.y),r=a;return a=h.getPointOnEllipticalArc(n[0],n[1],n[2],n[3],c,0),s+=h.getLineLength(r.x,r.y,a.x,a.y),s}return 0}static convertEndpointToCenterParameterization(t,e,i,n,s,r,a,o,h){const l=h*(Math.PI/180),d=Math.cos(l)*(t-i)/2+Math.sin(l)*(e-n)/2,c=-1*Math.sin(l)*(t-i)/2+Math.cos(l)*(e-n)/2,g=d*d/(a*a)+c*c/(o*o);g>1&&(a*=Math.sqrt(g),o*=Math.sqrt(g));let u=Math.sqrt((a*a*(o*o)-a*a*(c*c)-o*o*(d*d))/(a*a*(c*c)+o*o*(d*d)));s===r&&(u*=-1),isNaN(u)&&(u=0);const f=u*a*c/o,p=u*-o*d/a,m=(t+i)/2+Math.cos(l)*f-Math.sin(l)*p,_=(e+n)/2+Math.sin(l)*f+Math.cos(l)*p,y=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},v=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(y(t)*y(e))},x=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(v(t,e))},b=x([1,0],[(d-f)/a,(c-p)/o]),S=[(d-f)/a,(c-p)/o],w=[(-1*d-f)/a,(-1*c-p)/o];let C=x(S,w);return v(S,w)<=-1&&(C=Math.PI),v(S,w)>=1&&(C=0),0===r&&C>0&&(C-=2*Math.PI),1===r&&C<0&&(C+=2*Math.PI),[m,_,a,o,b,C,l,r]}}ge.prototype.className="Path",ge.prototype._attrsAffectingSize=["data"],n(ge),w.addGetterSetter(ge,"data");class ue extends ne{_sceneFunc(t){super._sceneFunc(t);const e=2*Math.PI,i=this.points();let n=i;const s=0!==this.tension()&&i.length>4;s&&(n=this.getTensionPoints());const r=this.pointerLength(),a=i.length;let o,h;if(s){const t=[n[n.length-4],n[n.length-3],n[n.length-2],n[n.length-1],i[a-2],i[a-1]],e=ge.calcLength(n[n.length-4],n[n.length-3],"C",t),s=ge.getPointOnQuadraticBezier(Math.min(1,1-r/e),t[0],t[1],t[2],t[3],t[4],t[5]);o=i[a-2]-s.x,h=i[a-1]-s.y}else o=i[a-2]-i[a-4],h=i[a-1]-i[a-3];const l=(Math.atan2(h,o)+e)%e,d=this.pointerWidth();this.pointerAtEnding()&&(t.save(),t.beginPath(),t.translate(i[a-2],i[a-1]),t.rotate(l),t.moveTo(0,0),t.lineTo(-r,d/2),t.lineTo(-r,-d/2),t.closePath(),t.restore(),this.__fillStroke(t)),this.pointerAtBeginning()&&(t.save(),t.beginPath(),t.translate(i[0],i[1]),s?(o=(n[0]+n[2])/2-i[0],h=(n[1]+n[3])/2-i[1]):(o=i[2]-i[0],h=i[3]-i[1]),t.rotate((Math.atan2(-h,-o)+e)%e),t.moveTo(0,0),t.lineTo(-r,d/2),t.lineTo(-r,-d/2),t.closePath(),t.restore(),this.__fillStroke(t))}__fillStroke(t){const e=this.dashEnabled();e&&(this.attrs.dashEnabled=!1,t.setLineDash([])),t.fillStrokeShape(this),e&&(this.attrs.dashEnabled=!0)}getSelfRect(){const t=super.getSelfRect(),e=this.pointerWidth()/2;return{x:t.x,y:t.y-e,width:t.width,height:t.height+2*e}}}ue.prototype.className="Arrow",n(ue),w.addGetterSetter(ue,"pointerLength",10,p()),w.addGetterSetter(ue,"pointerWidth",10,p()),w.addGetterSetter(ue,"pointerAtBeginning",!1),w.addGetterSetter(ue,"pointerAtEnding",!0);class fe extends Nt{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.attrs.radius||0,0,2*Math.PI,!1),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius()!==t/2&&this.radius(t/2)}setHeight(t){this.radius()!==t/2&&this.radius(t/2)}}fe.prototype._centroid=!0,fe.prototype.className="Circle",fe.prototype._attrsAffectingSize=["radius"],n(fe),w.addGetterSetter(fe,"radius",0,p());class pe extends Nt{_sceneFunc(t){const e=this.radiusX(),i=this.radiusY();t.beginPath(),t.save(),e!==i&&t.scale(1,i/e),t.arc(0,0,e,0,2*Math.PI,!1),t.restore(),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radiusX()}getHeight(){return 2*this.radiusY()}setWidth(t){this.radiusX(t/2)}setHeight(t){this.radiusY(t/2)}}pe.prototype.className="Ellipse",pe.prototype._centroid=!0,pe.prototype._attrsAffectingSize=["radiusX","radiusY"],n(pe),w.addComponentsGetterSetter(pe,"radius",["x","y"]),w.addGetterSetter(pe,"radiusX",0,p()),w.addGetterSetter(pe,"radiusY",0,p());class me extends Nt{constructor(t){super(t),this._loadListener=()=>{this._requestDraw()},this.on("imageChange.konva",(t=>{this._removeImageLoad(t.oldVal),this._setImageLoad()})),this._setImageLoad()}_setImageLoad(){const t=this.image();t&&t.complete||t&&4===t.readyState||t&&t.addEventListener&&t.addEventListener("load",this._loadListener)}_removeImageLoad(t){t&&t.removeEventListener&&t.removeEventListener("load",this._loadListener)}destroy(){return this._removeImageLoad(this.image()),super.destroy(),this}_useBufferCanvas(){const t=!!this.cornerRadius(),e=this.hasShadow();return!(!t||!e)||super._useBufferCanvas(!0)}_sceneFunc(t){const e=this.getWidth(),i=this.getHeight(),n=this.cornerRadius(),s=this.attrs.image;let r;if(s){const t=this.attrs.cropWidth,n=this.attrs.cropHeight;r=t&&n?[s,this.cropX(),this.cropY(),t,n,0,0,e,i]:[s,0,0,e,i]}(this.hasFill()||this.hasStroke()||n)&&(t.beginPath(),n?g.drawRoundedRectPath(t,e,i,n):t.rect(0,0,e,i),t.closePath(),t.fillStrokeShape(this)),s&&(n&&t.clip(),t.drawImage.apply(t,r))}_hitFunc(t){const e=this.width(),i=this.height(),n=this.cornerRadius();t.beginPath(),n?g.drawRoundedRectPath(t,e,i,n):t.rect(0,0,e,i),t.closePath(),t.fillStrokeShape(this)}getWidth(){var t,e;return null!==(t=this.attrs.width)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.width}getHeight(){var t,e;return null!==(t=this.attrs.height)&&void 0!==t?t:null===(e=this.image())||void 0===e?void 0:e.height}static fromURL(t,e,i=null){const n=g.createImageElement();n.onload=function(){const t=new me({image:n});e(t)},n.onerror=i,n.crossOrigin="Anonymous",n.src=t}}me.prototype.className="Image",n(me),w.addGetterSetter(me,"cornerRadius",0,m(4)),w.addGetterSetter(me,"image"),w.addComponentsGetterSetter(me,"crop",["x","y","width","height"]),w.addGetterSetter(me,"cropX",0,p()),w.addGetterSetter(me,"cropY",0,p()),w.addGetterSetter(me,"cropWidth",0,p()),w.addGetterSetter(me,"cropHeight",0,p());const _e=["fontFamily","fontSize","fontStyle","padding","lineHeight","text","width","height","pointerDirection","pointerWidth","pointerHeight"],ye="up",ve="right",xe="down",be="left",Se=_e.length;class we extends Xt{constructor(t){super(t),this.on("add.konva",(function(t){this._addListeners(t.child),this._sync()}))}getText(){return this.find("Text")[0]}getTag(){return this.find("Tag")[0]}_addListeners(t){let e,i=this;const n=function(){i._sync()};for(e=0;e<Se;e++)t.on(_e[e]+"Change.konva",n)}getWidth(){return this.getText().width()}getHeight(){return this.getText().height()}_sync(){let t,e,i,n,s,r,a,o=this.getText(),h=this.getTag();if(o&&h){switch(t=o.width(),e=o.height(),i=h.pointerDirection(),n=h.pointerWidth(),a=h.pointerHeight(),s=0,r=0,i){case ye:s=t/2,r=-1*a;break;case ve:s=t+n,r=e/2;break;case xe:s=t/2,r=e+a;break;case be:s=-1*n,r=e/2}h.setAttrs({x:-1*s,y:-1*r,width:t,height:e}),o.setAttrs({x:-1*s,y:-1*r})}}}we.prototype.className="Label",n(we);class Ce extends Nt{_sceneFunc(t){const e=this.width(),i=this.height(),n=this.pointerDirection(),s=this.pointerWidth(),r=this.pointerHeight(),a=this.cornerRadius();let o=0,h=0,l=0,d=0;"number"==typeof a?o=h=l=d=Math.min(a,e/2,i/2):(o=Math.min(a[0]||0,e/2,i/2),h=Math.min(a[1]||0,e/2,i/2),d=Math.min(a[2]||0,e/2,i/2),l=Math.min(a[3]||0,e/2,i/2)),t.beginPath(),t.moveTo(o,0),n===ye&&(t.lineTo((e-s)/2,0),t.lineTo(e/2,-1*r),t.lineTo((e+s)/2,0)),t.lineTo(e-h,0),t.arc(e-h,h,h,3*Math.PI/2,0,!1),n===ve&&(t.lineTo(e,(i-r)/2),t.lineTo(e+s,i/2),t.lineTo(e,(i+r)/2)),t.lineTo(e,i-d),t.arc(e-d,i-d,d,0,Math.PI/2,!1),n===xe&&(t.lineTo((e+s)/2,i),t.lineTo(e/2,i+r),t.lineTo((e-s)/2,i)),t.lineTo(l,i),t.arc(l,i-l,l,Math.PI/2,Math.PI,!1),n===be&&(t.lineTo(0,(i+r)/2),t.lineTo(-1*s,i/2),t.lineTo(0,(i-r)/2)),t.lineTo(0,o),t.arc(o,o,o,Math.PI,3*Math.PI/2,!1),t.closePath(),t.fillStrokeShape(this)}getSelfRect(){let t=0,e=0,i=this.pointerWidth(),n=this.pointerHeight(),s=this.pointerDirection(),r=this.width(),a=this.height();return s===ye?(e-=n,a+=n):s===xe?a+=n:s===be?(t-=1.5*i,r+=i):s===ve&&(r+=1.5*i),{x:t,y:e,width:r,height:a}}}Ce.prototype.className="Tag",n(Ce),w.addGetterSetter(Ce,"pointerDirection","none"),w.addGetterSetter(Ce,"pointerWidth",0,p()),w.addGetterSetter(Ce,"pointerHeight",0,p()),w.addGetterSetter(Ce,"cornerRadius",0,m(4));class Pe extends Nt{_sceneFunc(t){const e=this.cornerRadius(),i=this.width(),n=this.height();t.beginPath(),e?g.drawRoundedRectPath(t,i,n,e):t.rect(0,0,i,n),t.closePath(),t.fillStrokeShape(this)}}Pe.prototype.className="Rect",n(Pe),w.addGetterSetter(Pe,"cornerRadius",0,m(4));class ke extends Nt{_sceneFunc(t){const e=this._getPoints();t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let i=1;i<e.length;i++)t.lineTo(e[i].x,e[i].y);t.closePath(),t.fillStrokeShape(this)}_getPoints(){const t=this.attrs.sides,e=this.attrs.radius||0,i=[];for(let n=0;n<t;n++)i.push({x:e*Math.sin(2*n*Math.PI/t),y:-1*e*Math.cos(2*n*Math.PI/t)});return i}getSelfRect(){const t=this._getPoints();let e=t[0].x,i=t[0].y,n=t[0].x,s=t[0].y;return t.forEach((t=>{e=Math.min(e,t.x),i=Math.max(i,t.x),n=Math.min(n,t.y),s=Math.max(s,t.y)})),{x:e,y:n,width:i-e,height:s-n}}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}}ke.prototype.className="RegularPolygon",ke.prototype._centroid=!0,ke.prototype._attrsAffectingSize=["radius"],n(ke),w.addGetterSetter(ke,"radius",0,p()),w.addGetterSetter(ke,"sides",0,p());const Ae=2*Math.PI;class Te extends Nt{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.innerRadius(),0,Ae,!1),t.moveTo(this.outerRadius(),0),t.arc(0,0,this.outerRadius(),Ae,0,!0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}}Te.prototype.className="Ring",Te.prototype._centroid=!0,Te.prototype._attrsAffectingSize=["innerRadius","outerRadius"],n(Te),w.addGetterSetter(Te,"innerRadius",0,p()),w.addGetterSetter(Te,"outerRadius",0,p());class Me extends Nt{constructor(t){super(t),this._updated=!0,this.anim=new qt((()=>{const t=this._updated;return this._updated=!1,t})),this.on("animationChange.konva",(function(){this.frameIndex(0)})),this.on("frameIndexChange.konva",(function(){this._updated=!0})),this.on("frameRateChange.konva",(function(){this.anim.isRunning()&&(clearInterval(this.interval),this._setInterval())}))}_sceneFunc(t){const e=this.animation(),i=this.frameIndex(),n=4*i,s=this.animations()[e],r=this.frameOffsets(),a=s[n+0],o=s[n+1],h=s[n+2],l=s[n+3],d=this.image();if((this.hasFill()||this.hasStroke())&&(t.beginPath(),t.rect(0,0,h,l),t.closePath(),t.fillStrokeShape(this)),d)if(r){const n=r[e],s=2*i;t.drawImage(d,a,o,h,l,n[s+0],n[s+1],h,l)}else t.drawImage(d,a,o,h,l,0,0,h,l)}_hitFunc(t){const e=this.animation(),i=this.frameIndex(),n=4*i,s=this.animations()[e],r=this.frameOffsets(),a=s[n+2],o=s[n+3];if(t.beginPath(),r){const n=r[e],s=2*i;t.rect(n[s+0],n[s+1],a,o)}else t.rect(0,0,a,o);t.closePath(),t.fillShape(this)}_useBufferCanvas(){return super._useBufferCanvas(!0)}_setInterval(){const t=this;this.interval=setInterval((function(){t._updateIndex()}),1e3/this.frameRate())}start(){if(this.isRunning())return;const t=this.getLayer();this.anim.setLayers(t),this._setInterval(),this.anim.start()}stop(){this.anim.stop(),clearInterval(this.interval)}isRunning(){return this.anim.isRunning()}_updateIndex(){const t=this.frameIndex(),e=this.animation();t<this.animations()[e].length/4-1?this.frameIndex(t+1):this.frameIndex(0)}}Me.prototype.className="Sprite",n(Me),w.addGetterSetter(Me,"animation"),w.addGetterSetter(Me,"animations"),w.addGetterSetter(Me,"frameOffsets"),w.addGetterSetter(Me,"image"),w.addGetterSetter(Me,"frameIndex",0,p()),w.addGetterSetter(Me,"frameRate",17,p()),w.backCompat(Me,{index:"frameIndex",getIndex:"getFrameIndex",setIndex:"setFrameIndex"});class Ge extends Nt{_sceneFunc(t){const e=this.innerRadius(),i=this.outerRadius(),n=this.numPoints();t.beginPath(),t.moveTo(0,0-i);for(let s=1;s<2*n;s++){const r=s%2==0?i:e,a=r*Math.sin(s*Math.PI/n),o=-1*r*Math.cos(s*Math.PI/n);t.lineTo(a,o)}t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.outerRadius()}getHeight(){return 2*this.outerRadius()}setWidth(t){this.outerRadius(t/2)}setHeight(t){this.outerRadius(t/2)}}function Re(t){return[...t].reduce(((t,e,i,n)=>{if(/\p{Emoji}/u.test(e)){const s=n[i+1];s&&/\p{Emoji_Modifier}|\u200D/u.test(s)?(t.push(e+s),n[i+1]=""):t.push(e)}else/\p{Regional_Indicator}{2}/u.test(e+(n[i+1]||""))?t.push(e+n[i+1]):i>0&&/\p{Mn}|\p{Me}|\p{Mc}/u.test(e)?t[t.length-1]+=e:e&&t.push(e);return t}),[])}Ge.prototype.className="Star",Ge.prototype._centroid=!0,Ge.prototype._attrsAffectingSize=["innerRadius","outerRadius"],n(Ge),w.addGetterSetter(Ge,"numPoints",5,p()),w.addGetterSetter(Ge,"innerRadius",0,p()),w.addGetterSetter(Ge,"outerRadius",0,p());const Ee="auto",De="inherit",Le="justify",Ie="left",Oe="middle",Fe="normal",Be=" ",Ne="none",He=["direction","fontFamily","fontSize","fontStyle","fontVariant","padding","align","verticalAlign","lineHeight","text","width","height","wrap","ellipsis","letterSpacing"],ze=He.length;let We;function Ye(){return We||(We=g.createCanvasElement().getContext("2d"),We)}class Xe extends Nt{constructor(t){super(function(t){return(t=t||{}).fillLinearGradientColorStops||t.fillRadialGradientColorStops||t.fillPatternImage||(t.fill=t.fill||"black"),t}(t)),this._partialTextX=0,this._partialTextY=0;for(let t=0;t<ze;t++)this.on(He[t]+"Change.konva",this._setTextData);this._setTextData()}_sceneFunc(t){const e=this.textArr,n=e.length;if(!this.text())return;let s,r=this.padding(),a=this.fontSize(),o=this.lineHeight()*a,h=this.verticalAlign(),l=this.direction(),d=0,c=this.align(),g=this.getWidth(),u=this.letterSpacing(),f=this.fill(),p=this.textDecoration(),m=-1!==p.indexOf("underline"),_=-1!==p.indexOf("line-through");l=l===De?t.direction:l;let y=o/2,v=Oe;if(i._fixTextRendering){const t=this.measureSize("M");v="alphabetic",y=(t.fontBoundingBoxAscent-t.fontBoundingBoxDescent)/2+o/2}var x=0,b=0;for("rtl"===l&&t.setAttr("direction",l),t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",v),t.setAttr("textAlign",Ie),h===Oe?d=(this.getHeight()-n*o-2*r)/2:"bottom"===h&&(d=this.getHeight()-n*o-2*r),t.translate(r,d+r),s=0;s<n;s++){x=0,b=0;var S,w,C=e[s],P=C.text,k=C.width,A=C.lastInParagraph;if(t.save(),"right"===c?x+=g-k-2*r:"center"===c&&(x+=(g-k-2*r)/2),m){t.save(),t.beginPath();const e=x,n=y+b+(i._fixTextRendering?Math.round(a/4):Math.round(a/2));t.moveTo(e,n),S=P.split(" ").length-1,w=c!==Le||A?k:g-2*r,t.lineTo(e+Math.round(w),n),t.lineWidth=a/15;const s=this._getLinearGradient();t.strokeStyle=s||f,t.stroke(),t.restore()}if(_){t.save(),t.beginPath();const e=i._fixTextRendering?-Math.round(a/4):0;t.moveTo(x,y+b+e),S=P.split(" ").length-1,w=c!==Le||A?k:g-2*r,t.lineTo(x+Math.round(w),y+b+e),t.lineWidth=a/15;const n=this._getLinearGradient();t.strokeStyle=n||f,t.stroke(),t.restore()}if("rtl"===l||0===u&&c!==Le)0!==u&&t.setAttr("letterSpacing",`${u}px`),this._partialTextX=x,this._partialTextY=y+b,this._partialText=P,t.fillStrokeShape(this);else{S=P.split(" ").length-1;const e=Re(P);for(let i=0;i<e.length;i++){const n=e[i];" "!==n||A||c!==Le||(x+=(g-2*r-k)/S),this._partialTextX=x,this._partialTextY=y+b,this._partialText=n,t.fillStrokeShape(this),x+=this.measureSize(n).width+u}}t.restore(),n>1&&(y+=o)}}_hitFunc(t){const e=this.getWidth(),i=this.getHeight();t.beginPath(),t.rect(0,0,e,i),t.closePath(),t.fillStrokeShape(this)}setText(t){const e=g._isString(t)?t:null==t?"":t+"";return this._setAttr("text",e),this}getWidth(){return this.attrs.width===Ee||void 0===this.attrs.width?this.getTextWidth()+2*this.padding():this.attrs.width}getHeight(){return this.attrs.height===Ee||void 0===this.attrs.height?this.fontSize()*this.textArr.length*this.lineHeight()+2*this.padding():this.attrs.height}getTextWidth(){return this.textWidth}getTextHeight(){return g.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}measureSize(t){var e,i,n,s,r,a,o,h,l,d,c;let g,u=Ye(),f=this.fontSize();u.save(),u.font=this._getContextFont(),g=u.measureText(t),u.restore();const p=f/100;return{actualBoundingBoxAscent:null!==(e=g.actualBoundingBoxAscent)&&void 0!==e?e:71.58203125*p,actualBoundingBoxDescent:null!==(i=g.actualBoundingBoxDescent)&&void 0!==i?i:0,actualBoundingBoxLeft:null!==(n=g.actualBoundingBoxLeft)&&void 0!==n?n:-7.421875*p,actualBoundingBoxRight:null!==(s=g.actualBoundingBoxRight)&&void 0!==s?s:75.732421875*p,alphabeticBaseline:null!==(r=g.alphabeticBaseline)&&void 0!==r?r:0,emHeightAscent:null!==(a=g.emHeightAscent)&&void 0!==a?a:100*p,emHeightDescent:null!==(o=g.emHeightDescent)&&void 0!==o?o:-20*p,fontBoundingBoxAscent:null!==(h=g.fontBoundingBoxAscent)&&void 0!==h?h:91*p,fontBoundingBoxDescent:null!==(l=g.fontBoundingBoxDescent)&&void 0!==l?l:21*p,hangingBaseline:null!==(d=g.hangingBaseline)&&void 0!==d?d:72.80000305175781*p,ideographicBaseline:null!==(c=g.ideographicBaseline)&&void 0!==c?c:-21*p,width:g.width,height:f}}_getContextFont(){return this.fontStyle()+Be+this.fontVariant()+Be+(this.fontSize()+"px ")+this.fontFamily().split(",").map((t=>{const e=(t=t.trim()).indexOf(" ")>=0,i=t.indexOf('"')>=0||t.indexOf("'")>=0;return e&&!i&&(t=`"${t}"`),t})).join(", ")}_addTextLine(t){this.align()===Le&&(t=t.trim());const e=this._getTextWidth(t);return this.textArr.push({text:t,width:e,lastInParagraph:!1})}_getTextWidth(t){const e=this.letterSpacing(),i=t.length;return Ye().measureText(t).width+e*i}_setTextData(){let t=this.text().split("\n"),e=+this.fontSize(),i=0,n=this.lineHeight()*e,s=this.attrs.width,r=this.attrs.height,a=s!==Ee&&void 0!==s,o=r!==Ee&&void 0!==r,h=this.padding(),l=s-2*h,d=r-2*h,c=0,g=this.wrap(),u="char"!==g&&g!==Ne,f=this.ellipsis();this.textArr=[],Ye().font=this._getContextFont();const p=f?this._getTextWidth("…"):0;for(let e=0,s=t.length;e<s;++e){let r=t[e],h=this._getTextWidth(r);if(a&&h>l)for(;r.length>0;){let t=0,e=Re(r).length,s="",a=0;for(;t<e;){const i=t+e>>>1,h=Re(r).slice(0,i+1).join(""),g=this._getTextWidth(h);(f&&o&&c+n>d?g+p:g)<=l?(t=i+1,s=h,a=g):e=i}if(!s)break;if(u){const e=Re(r),i=Re(s),n=e[i.length];let o;if((n===Be||"-"===n)&&a<=l)o=i.length;else{const t=i.lastIndexOf(Be),e=i.lastIndexOf("-");o=Math.max(t,e)+1}o>0&&(t=o,s=e.slice(0,t).join(""),a=this._getTextWidth(s))}s=s.trimRight(),this._addTextLine(s),i=Math.max(i,a),c+=n;if(this._shouldHandleEllipsis(c)){this._tryToAddEllipsisToLastLine();break}if(r=Re(r).slice(t).join("").trimLeft(),r.length>0&&(h=this._getTextWidth(r),h<=l)){this._addTextLine(r),c+=n,i=Math.max(i,h);break}}else this._addTextLine(r),c+=n,i=Math.max(i,h),this._shouldHandleEllipsis(c)&&e<s-1&&this._tryToAddEllipsisToLastLine();if(this.textArr[this.textArr.length-1]&&(this.textArr[this.textArr.length-1].lastInParagraph=!0),o&&c+n>d)break}this.textHeight=e,this.textWidth=i}_shouldHandleEllipsis(t){const e=+this.fontSize(),i=this.lineHeight()*e,n=this.attrs.height,s=n!==Ee&&void 0!==n,r=n-2*this.padding();return!(this.wrap()!==Ne)||s&&t+i>r}_tryToAddEllipsisToLastLine(){const t=this.attrs.width,e=t!==Ee&&void 0!==t,i=t-2*this.padding(),n=this.ellipsis(),s=this.textArr[this.textArr.length-1];if(s&&n){if(e){this._getTextWidth(s.text+"…")<i||(s.text=s.text.slice(0,s.text.length-3))}this.textArr.splice(this.textArr.length-1,1),this._addTextLine(s.text+"…")}}getStrokeScaleEnabled(){return!0}_useBufferCanvas(){const t=-1!==this.textDecoration().indexOf("underline")||-1!==this.textDecoration().indexOf("line-through"),e=this.hasShadow();return!(!t||!e)||super._useBufferCanvas()}}Xe.prototype._fillFunc=function(t){t.fillText(this._partialText,this._partialTextX,this._partialTextY)},Xe.prototype._strokeFunc=function(t){t.setAttr("miterLimit",2),t.strokeText(this._partialText,this._partialTextX,this._partialTextY)},Xe.prototype.className="Text",Xe.prototype._attrsAffectingSize=["text","fontSize","padding","wrap","lineHeight","letterSpacing"],n(Xe),w.overWriteSetter(Xe,"width",_()),w.overWriteSetter(Xe,"height",_()),w.addGetterSetter(Xe,"direction",De),w.addGetterSetter(Xe,"fontFamily","Arial"),w.addGetterSetter(Xe,"fontSize",12,p()),w.addGetterSetter(Xe,"fontStyle",Fe),w.addGetterSetter(Xe,"fontVariant",Fe),w.addGetterSetter(Xe,"padding",0,p()),w.addGetterSetter(Xe,"align",Ie),w.addGetterSetter(Xe,"verticalAlign","top"),w.addGetterSetter(Xe,"lineHeight",1,p()),w.addGetterSetter(Xe,"wrap","word"),w.addGetterSetter(Xe,"ellipsis",!1,x()),w.addGetterSetter(Xe,"letterSpacing",0,p()),w.addGetterSetter(Xe,"text","",y()),w.addGetterSetter(Xe,"textDecoration","");const je="normal";function qe(t){t.fillText(this.partialText,0,0)}function Ue(t){t.strokeText(this.partialText,0,0)}class Ve extends Nt{constructor(t){super(t),this.dummyCanvas=g.createCanvasElement(),this.dataArray=[],this._readDataAttribute(),this.on("dataChange.konva",(function(){this._readDataAttribute(),this._setTextData()})),this.on("textChange.konva alignChange.konva letterSpacingChange.konva kerningFuncChange.konva fontSizeChange.konva fontFamilyChange.konva",this._setTextData),this._setTextData()}_getTextPathLength(){return ge.getPathLength(this.dataArray)}_getPointAtLength(t){if(!this.attrs.data)return null;return t-1>this.pathLength?null:ge.getPointAtLengthOfDataArray(t,this.dataArray)}_readDataAttribute(){this.dataArray=ge.parsePathData(this.attrs.data),this.pathLength=this._getTextPathLength()}_sceneFunc(t){t.setAttr("font",this._getContextFont()),t.setAttr("textBaseline",this.textBaseline()),t.setAttr("textAlign","left"),t.save();const e=this.textDecoration(),i=this.fill(),n=this.fontSize(),s=this.glyphInfo;"underline"===e&&t.beginPath();for(let i=0;i<s.length;i++){t.save();const r=s[i].p0;t.translate(r.x,r.y),t.rotate(s[i].rotation),this.partialText=s[i].text,t.fillStrokeShape(this),"underline"===e&&(0===i&&t.moveTo(0,n/2+1),t.lineTo(n,n/2+1)),t.restore()}"underline"===e&&(t.strokeStyle=i,t.lineWidth=n/20,t.stroke()),t.restore()}_hitFunc(t){t.beginPath();const e=this.glyphInfo;if(e.length>=1){const i=e[0].p0;t.moveTo(i.x,i.y)}for(let i=0;i<e.length;i++){const n=e[i].p1;t.lineTo(n.x,n.y)}t.setAttr("lineWidth",this.fontSize()),t.setAttr("strokeStyle",this.colorKey),t.stroke()}getTextWidth(){return this.textWidth}getTextHeight(){return g.warn("text.getTextHeight() method is deprecated. Use text.height() - for full height and text.fontSize() - for one line height."),this.textHeight}setText(t){return Xe.prototype.setText.call(this,t)}_getContextFont(){return Xe.prototype._getContextFont.call(this)}_getTextSize(t){const e=this.dummyCanvas.getContext("2d");e.save(),e.font=this._getContextFont();const i=e.measureText(t);return e.restore(),{width:i.width,height:parseInt(`${this.fontSize()}`,10)}}_setTextData(){const{width:t,height:e}=this._getTextSize(this.attrs.text);if(this.textWidth=t,this.textHeight=e,this.glyphInfo=[],!this.attrs.data)return null;const i=this.letterSpacing(),n=this.align(),s=this.kerningFunc(),r=Math.max(this.textWidth+((this.attrs.text||"").length-1)*i,0);let a=0;"center"===n&&(a=Math.max(0,this.pathLength/2-r/2)),"right"===n&&(a=Math.max(0,this.pathLength-r));const o=Re(this.text());let h=a;for(let t=0;t<o.length;t++){const e=this._getPointAtLength(h);if(!e)return;let a=this._getTextSize(o[t]).width+i;if(" "===o[t]&&"justify"===n){const t=this.text().split(" ").length-1;a+=(this.pathLength-r)/t}const l=this._getPointAtLength(h+a);if(!l)return;const d=ge.getLineLength(e.x,e.y,l.x,l.y);let c=0;if(s)try{c=s(o[t-1],o[t])*this.fontSize()}catch(t){c=0}e.x+=c,l.x+=c,this.textWidth+=c;const g=ge.getPointOnLine(c+d/2,e.x,e.y,l.x,l.y),u=Math.atan2(l.y-e.y,l.x-e.x);this.glyphInfo.push({transposeX:g.x,transposeY:g.y,text:o[t],rotation:u,p0:e,p1:l}),h+=a}}getSelfRect(){if(!this.glyphInfo.length)return{x:0,y:0,width:0,height:0};const t=[];this.glyphInfo.forEach((function(e){t.push(e.p0.x),t.push(e.p0.y),t.push(e.p1.x),t.push(e.p1.y)}));let e,i,n=t[0]||0,s=t[0]||0,r=t[1]||0,a=t[1]||0;for(let o=0;o<t.length/2;o++)e=t[2*o],i=t[2*o+1],n=Math.min(n,e),s=Math.max(s,e),r=Math.min(r,i),a=Math.max(a,i);const o=this.fontSize();return{x:n-o/2,y:r-o/2,width:s-n+o,height:a-r+o}}destroy(){return g.releaseCanvas(this.dummyCanvas),super.destroy()}}Ve.prototype._fillFunc=qe,Ve.prototype._strokeFunc=Ue,Ve.prototype._fillFuncHit=qe,Ve.prototype._strokeFuncHit=Ue,Ve.prototype.className="TextPath",Ve.prototype._attrsAffectingSize=["text","fontSize","data"],n(Ve),w.addGetterSetter(Ve,"data"),w.addGetterSetter(Ve,"fontFamily","Arial"),w.addGetterSetter(Ve,"fontSize",12,p()),w.addGetterSetter(Ve,"fontStyle",je),w.addGetterSetter(Ve,"align","left"),w.addGetterSetter(Ve,"letterSpacing",0,p()),w.addGetterSetter(Ve,"textBaseline","middle"),w.addGetterSetter(Ve,"fontVariant",je),w.addGetterSetter(Ve,"text",""),w.addGetterSetter(Ve,"textDecoration",""),w.addGetterSetter(Ve,"kerningFunc",void 0);const Ke="tr-konva",Qe=["resizeEnabledChange","rotateAnchorOffsetChange","rotateEnabledChange","enabledAnchorsChange","anchorSizeChange","borderEnabledChange","borderStrokeChange","borderStrokeWidthChange","borderDashChange","anchorStrokeChange","anchorStrokeWidthChange","anchorFillChange","anchorCornerRadiusChange","ignoreStrokeChange","anchorStyleFuncChange"].map((t=>t+`.${Ke}`)).join(" "),Je="nodesRect",$e=["widthChange","heightChange","scaleXChange","scaleYChange","skewXChange","skewYChange","rotationChange","offsetXChange","offsetYChange","transformsEnabledChange","strokeWidthChange"],Ze={"top-left":-45,"top-center":0,"top-right":45,"middle-right":-90,"middle-left":90,"bottom-left":-135,"bottom-center":180,"bottom-right":135},ti="ontouchstart"in i._global;const ei=["top-left","top-center","top-right","middle-right","middle-left","bottom-left","bottom-center","bottom-right"];function ii(t,e,i){const n=i.x+(t.x-i.x)*Math.cos(e)-(t.y-i.y)*Math.sin(e),s=i.y+(t.x-i.x)*Math.sin(e)+(t.y-i.y)*Math.cos(e);return{...t,rotation:t.rotation+e,x:n,y:s}}function ni(t,e){const i=function(t){return{x:t.x+t.width/2*Math.cos(t.rotation)+t.height/2*Math.sin(-t.rotation),y:t.y+t.height/2*Math.cos(t.rotation)+t.width/2*Math.sin(t.rotation)}}(t);return ii(t,e,i)}let si=0;class ri extends Xt{constructor(t){super(t),this._movingAnchorName=null,this._transforming=!1,this._createElements(),this._handleMouseMove=this._handleMouseMove.bind(this),this._handleMouseUp=this._handleMouseUp.bind(this),this.update=this.update.bind(this),this.on(Qe,this.update),this.getNode()&&this.update()}attachTo(t){return this.setNode(t),this}setNode(t){return g.warn("tr.setNode(shape), tr.node(shape) and tr.attachTo(shape) methods are deprecated. Please use tr.nodes(nodesArray) instead."),this.setNodes([t])}getNode(){return this._nodes&&this._nodes[0]}_getEventNamespace(){return Ke+this._id}setNodes(t=[]){this._nodes&&this._nodes.length&&this.detach();const e=t.filter((t=>!t.isAncestorOf(this)||(g.error("Konva.Transformer cannot be an a child of the node you are trying to attach"),!1)));this._nodes=t=e,1===t.length&&this.useSingleNodeRotation()?this.rotation(t[0].getAbsoluteRotation()):this.rotation(0),this._nodes.forEach((t=>{const e=()=>{1===this.nodes().length&&this.useSingleNodeRotation()&&this.rotation(this.nodes()[0].getAbsoluteRotation()),this._resetTransformCache(),this._transforming||this.isDragging()||this.update()};if(t._attrsAffectingSize.length){const i=t._attrsAffectingSize.map((t=>t+"Change."+this._getEventNamespace())).join(" ");t.on(i,e)}t.on($e.map((t=>t+`.${this._getEventNamespace()}`)).join(" "),e),t.on(`absoluteTransformChange.${this._getEventNamespace()}`,e),this._proxyDrag(t)})),this._resetTransformCache();return!!this.findOne(".top-left")&&this.update(),this}_proxyDrag(t){let e;t.on(`dragstart.${this._getEventNamespace()}`,(i=>{e=t.getAbsolutePosition(),this.isDragging()||t===this.findOne(".back")||this.startDrag(i,!1)})),t.on(`dragmove.${this._getEventNamespace()}`,(i=>{if(!e)return;const n=t.getAbsolutePosition(),s=n.x-e.x,r=n.y-e.y;this.nodes().forEach((e=>{if(e===t)return;if(e.isDragging())return;const n=e.getAbsolutePosition();e.setAbsolutePosition({x:n.x+s,y:n.y+r}),e.startDrag(i)})),e=null}))}getNodes(){return this._nodes||[]}getActiveAnchor(){return this._movingAnchorName}detach(){this._nodes&&this._nodes.forEach((t=>{t.off("."+this._getEventNamespace())})),this._nodes=[],this._resetTransformCache()}_resetTransformCache(){this._clearCache(Je),this._clearCache("transform"),this._clearSelfAndDescendantCache("absoluteTransform")}_getNodeRect(){return this._getCache(Je,this.__getNodeRect)}__getNodeShape(t,e=this.rotation(),n){const s=t.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),r=t.getAbsoluteScale(n),a=t.getAbsolutePosition(n),o=s.x*r.x-t.offsetX()*r.x,h=s.y*r.y-t.offsetY()*r.y,l=(i.getAngle(t.getAbsoluteRotation())+2*Math.PI)%(2*Math.PI);return ii({x:a.x+o*Math.cos(l)+h*Math.sin(-l),y:a.y+h*Math.cos(l)+o*Math.sin(l),width:s.width*r.x,height:s.height*r.y,rotation:l},-i.getAngle(e),{x:0,y:0})}__getNodeRect(){if(!this.getNode())return{x:-1e8,y:-1e8,width:0,height:0,rotation:0};const t=[];this.nodes().map((e=>{const i=e.getClientRect({skipTransform:!0,skipShadow:!0,skipStroke:this.ignoreStroke()}),n=[{x:i.x,y:i.y},{x:i.x+i.width,y:i.y},{x:i.x+i.width,y:i.y+i.height},{x:i.x,y:i.y+i.height}],s=e.getAbsoluteTransform();n.forEach((function(e){const i=s.point(e);t.push(i)}))}));const e=new s;e.rotate(-i.getAngle(this.rotation()));let n=1/0,r=1/0,a=-1/0,o=-1/0;t.forEach((function(t){const i=e.point(t);void 0===n&&(n=a=i.x,r=o=i.y),n=Math.min(n,i.x),r=Math.min(r,i.y),a=Math.max(a,i.x),o=Math.max(o,i.y)})),e.invert();const h=e.point({x:n,y:r});return{x:h.x,y:h.y,width:a-n,height:o-r,rotation:i.getAngle(this.rotation())}}getX(){return this._getNodeRect().x}getY(){return this._getNodeRect().y}getWidth(){return this._getNodeRect().width}getHeight(){return this._getNodeRect().height}_createElements(){this._createBack(),ei.forEach((t=>{this._createAnchor(t)})),this._createAnchor("rotater")}_createAnchor(t){const e=new Pe({stroke:"rgb(0, 161, 255)",fill:"white",strokeWidth:1,name:t+" _anchor",dragDistance:0,draggable:!0,hitStrokeWidth:ti?10:"auto"}),n=this;e.on("mousedown touchstart",(function(t){n._handleMouseDown(t)})),e.on("dragstart",(t=>{e.stopDrag(),t.cancelBubble=!0})),e.on("dragend",(t=>{t.cancelBubble=!0})),e.on("mouseenter",(()=>{const n=i.getAngle(this.rotation()),s=this.rotateAnchorCursor(),r=function(t,e,i){if("rotater"===t)return i;e+=g.degToRad(Ze[t]||0);const n=(g.radToDeg(e)%360+360)%360;return g._inRange(n,337.5,360)||g._inRange(n,0,22.5)?"ns-resize":g._inRange(n,22.5,67.5)?"nesw-resize":g._inRange(n,67.5,112.5)?"ew-resize":g._inRange(n,112.5,157.5)?"nwse-resize":g._inRange(n,157.5,202.5)?"ns-resize":g._inRange(n,202.5,247.5)?"nesw-resize":g._inRange(n,247.5,292.5)?"ew-resize":g._inRange(n,292.5,337.5)?"nwse-resize":(g.error("Transformer has unknown angle for cursor detection: "+n),"pointer")}(t,n,s);e.getStage().content&&(e.getStage().content.style.cursor=r),this._cursorChange=!0})),e.on("mouseout",(()=>{e.getStage().content&&(e.getStage().content.style.cursor=""),this._cursorChange=!1})),this.add(e)}_createBack(){const t=new Nt({name:"back",width:0,height:0,draggable:!0,sceneFunc(t,e){const i=e.getParent(),n=i.padding();t.beginPath(),t.rect(-n,-n,e.width()+2*n,e.height()+2*n),t.moveTo(e.width()/2,-n),i.rotateEnabled()&&i.rotateLineVisible()&&t.lineTo(e.width()/2,-i.rotateAnchorOffset()*g._sign(e.height())-n),t.fillStrokeShape(e)},hitFunc:(t,e)=>{if(!this.shouldOverdrawWholeArea())return;const i=this.padding();t.beginPath(),t.rect(-i,-i,e.width()+2*i,e.height()+2*i),t.fillStrokeShape(e)}});this.add(t),this._proxyDrag(t),t.on("dragstart",(t=>{t.cancelBubble=!0})),t.on("dragmove",(t=>{t.cancelBubble=!0})),t.on("dragend",(t=>{t.cancelBubble=!0})),this.on("dragmove",(t=>{this.update()}))}_handleMouseDown(t){if(this._transforming)return;this._movingAnchorName=t.target.name().split(" ")[0];const e=this._getNodeRect(),i=e.width,n=e.height,s=Math.sqrt(Math.pow(i,2)+Math.pow(n,2));this.sin=Math.abs(n/s),this.cos=Math.abs(i/s),"undefined"!=typeof window&&(window.addEventListener("mousemove",this._handleMouseMove),window.addEventListener("touchmove",this._handleMouseMove),window.addEventListener("mouseup",this._handleMouseUp,!0),window.addEventListener("touchend",this._handleMouseUp,!0)),this._transforming=!0;const r=t.target.getAbsolutePosition(),a=t.target.getStage().getPointerPosition();this._anchorDragOffset={x:a.x-r.x,y:a.y-r.y},si++,this._fire("transformstart",{evt:t.evt,target:this.getNode()}),this._nodes.forEach((e=>{e._fire("transformstart",{evt:t.evt,target:e})}))}_handleMouseMove(t){let e,n,s;const r=this.findOne("."+this._movingAnchorName),a=r.getStage();a.setPointersPositions(t);const o=a.getPointerPosition();let h={x:o.x-this._anchorDragOffset.x,y:o.y-this._anchorDragOffset.y};const l=r.getAbsolutePosition();this.anchorDragBoundFunc()&&(h=this.anchorDragBoundFunc()(l,h,t)),r.setAbsolutePosition(h);const d=r.getAbsolutePosition();if(l.x===d.x&&l.y===d.y)return;if("rotater"===this._movingAnchorName){const s=this._getNodeRect();e=r.x()-s.width/2,n=-r.y()+s.height/2;let a=Math.atan2(-n,e)+Math.PI/2;s.height<0&&(a-=Math.PI);const o=i.getAngle(this.rotation())+a,h=i.getAngle(this.rotationSnapTolerance()),l=function(t,e,n){let s=e;for(let r=0;r<t.length;r++){const a=i.getAngle(t[r]),o=Math.abs(a-e)%(2*Math.PI);Math.min(o,2*Math.PI-o)<n&&(s=a)}return s}(this.rotationSnaps(),o,h),d=ni(s,l-s.rotation);return void this._fitNodesInto(d,t)}const c=this.shiftBehavior();let g;g="inverted"===c?this.keepRatio()&&!t.shiftKey:"none"===c?this.keepRatio():this.keepRatio()||t.shiftKey;var u=this.centeredScaling()||t.altKey;if("top-left"===this._movingAnchorName){if(g){var f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-right").x(),y:this.findOne(".bottom-right").y()};s=Math.sqrt(Math.pow(f.x-r.x(),2)+Math.pow(f.y-r.y(),2));var p=this.findOne(".top-left").x()>f.x?-1:1,m=this.findOne(".top-left").y()>f.y?-1:1;e=s*this.cos*p,n=s*this.sin*m,this.findOne(".top-left").x(f.x-e),this.findOne(".top-left").y(f.y-n)}}else if("top-center"===this._movingAnchorName)this.findOne(".top-left").y(r.y());else if("top-right"===this._movingAnchorName){if(g){f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".bottom-left").x(),y:this.findOne(".bottom-left").y()};s=Math.sqrt(Math.pow(r.x()-f.x,2)+Math.pow(f.y-r.y(),2));p=this.findOne(".top-right").x()<f.x?-1:1,m=this.findOne(".top-right").y()>f.y?-1:1;e=s*this.cos*p,n=s*this.sin*m,this.findOne(".top-right").x(f.x+e),this.findOne(".top-right").y(f.y-n)}var _=r.position();this.findOne(".top-left").y(_.y),this.findOne(".bottom-right").x(_.x)}else if("middle-left"===this._movingAnchorName)this.findOne(".top-left").x(r.x());else if("middle-right"===this._movingAnchorName)this.findOne(".bottom-right").x(r.x());else if("bottom-left"===this._movingAnchorName){if(g){f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-right").x(),y:this.findOne(".top-right").y()};s=Math.sqrt(Math.pow(f.x-r.x(),2)+Math.pow(r.y()-f.y,2));p=f.x<r.x()?-1:1,m=r.y()<f.y?-1:1;e=s*this.cos*p,n=s*this.sin*m,r.x(f.x-e),r.y(f.y+n)}_=r.position(),this.findOne(".top-left").x(_.x),this.findOne(".bottom-right").y(_.y)}else if("bottom-center"===this._movingAnchorName)this.findOne(".bottom-right").y(r.y());else if("bottom-right"===this._movingAnchorName){if(g){f=u?{x:this.width()/2,y:this.height()/2}:{x:this.findOne(".top-left").x(),y:this.findOne(".top-left").y()};s=Math.sqrt(Math.pow(r.x()-f.x,2)+Math.pow(r.y()-f.y,2));p=this.findOne(".bottom-right").x()<f.x?-1:1,m=this.findOne(".bottom-right").y()<f.y?-1:1;e=s*this.cos*p,n=s*this.sin*m,this.findOne(".bottom-right").x(f.x+e),this.findOne(".bottom-right").y(f.y+n)}}else console.error(new Error("Wrong position argument of selection resizer: "+this._movingAnchorName));if(u=this.centeredScaling()||t.altKey){const t=this.findOne(".top-left"),e=this.findOne(".bottom-right"),i=t.x(),n=t.y(),s=this.getWidth()-e.x(),r=this.getHeight()-e.y();e.move({x:-i,y:-n}),t.move({x:s,y:r})}const y=this.findOne(".top-left").getAbsolutePosition();e=y.x,n=y.y;const v=this.findOne(".bottom-right").x()-this.findOne(".top-left").x(),x=this.findOne(".bottom-right").y()-this.findOne(".top-left").y();this._fitNodesInto({x:e,y:n,width:v,height:x,rotation:i.getAngle(this.rotation())},t)}_handleMouseUp(t){this._removeEvents(t)}getAbsoluteTransform(){return this.getTransform()}_removeEvents(t){var e;if(this._transforming){this._transforming=!1,"undefined"!=typeof window&&(window.removeEventListener("mousemove",this._handleMouseMove),window.removeEventListener("touchmove",this._handleMouseMove),window.removeEventListener("mouseup",this._handleMouseUp,!0),window.removeEventListener("touchend",this._handleMouseUp,!0));const i=this.getNode();si--,this._fire("transformend",{evt:t,target:i}),null===(e=this.getLayer())||void 0===e||e.batchDraw(),i&&this._nodes.forEach((e=>{var i;e._fire("transformend",{evt:t,target:e}),null===(i=e.getLayer())||void 0===i||i.batchDraw()})),this._movingAnchorName=null}}_fitNodesInto(t,e){const n=this._getNodeRect();if(g._inRange(t.width,2*-this.padding()-1,1))return void this.update();if(g._inRange(t.height,2*-this.padding()-1,1))return void this.update();const r=new s;if(r.rotate(i.getAngle(this.rotation())),this._movingAnchorName&&t.width<0&&this._movingAnchorName.indexOf("left")>=0){const e=r.point({x:2*-this.padding(),y:0});t.x+=e.x,t.y+=e.y,t.width+=2*this.padding(),this._movingAnchorName=this._movingAnchorName.replace("left","right"),this._anchorDragOffset.x-=e.x,this._anchorDragOffset.y-=e.y}else if(this._movingAnchorName&&t.width<0&&this._movingAnchorName.indexOf("right")>=0){const e=r.point({x:2*this.padding(),y:0});this._movingAnchorName=this._movingAnchorName.replace("right","left"),this._anchorDragOffset.x-=e.x,this._anchorDragOffset.y-=e.y,t.width+=2*this.padding()}if(this._movingAnchorName&&t.height<0&&this._movingAnchorName.indexOf("top")>=0){const e=r.point({x:0,y:2*-this.padding()});t.x+=e.x,t.y+=e.y,this._movingAnchorName=this._movingAnchorName.replace("top","bottom"),this._anchorDragOffset.x-=e.x,this._anchorDragOffset.y-=e.y,t.height+=2*this.padding()}else if(this._movingAnchorName&&t.height<0&&this._movingAnchorName.indexOf("bottom")>=0){const e=r.point({x:0,y:2*this.padding()});this._movingAnchorName=this._movingAnchorName.replace("bottom","top"),this._anchorDragOffset.x-=e.x,this._anchorDragOffset.y-=e.y,t.height+=2*this.padding()}if(this.boundBoxFunc()){const e=this.boundBoxFunc()(n,t);e?t=e:g.warn("boundBoxFunc returned falsy. You should return new bound rect from it!")}const a=1e7,o=new s;o.translate(n.x,n.y),o.rotate(n.rotation),o.scale(n.width/a,n.height/a);const h=new s,l=t.width/a,d=t.height/a;!1===this.flipEnabled()?(h.translate(t.x,t.y),h.rotate(t.rotation),h.translate(t.width<0?t.width:0,t.height<0?t.height:0),h.scale(Math.abs(l),Math.abs(d))):(h.translate(t.x,t.y),h.rotate(t.rotation),h.scale(l,d));const c=h.multiply(o.invert());this._nodes.forEach((t=>{var e;const i=t.getParent().getAbsoluteTransform(),n=t.getTransform().copy();n.translate(t.offsetX(),t.offsetY());const r=new s;r.multiply(i.copy().invert()).multiply(c).multiply(i).multiply(n);const a=r.decompose();t.setAttrs(a),null===(e=t.getLayer())||void 0===e||e.batchDraw()})),this.rotation(g._getRotation(t.rotation)),this._nodes.forEach((t=>{this._fire("transform",{evt:e,target:t}),t._fire("transform",{evt:e,target:t})})),this._resetTransformCache(),this.update(),this.getLayer().batchDraw()}forceUpdate(){this._resetTransformCache(),this.update()}_batchChangeChild(t,e){this.findOne(t).setAttrs(e)}update(){var t;const e=this._getNodeRect();this.rotation(g._getRotation(e.rotation));const i=e.width,n=e.height,s=this.enabledAnchors(),r=this.resizeEnabled(),a=this.padding(),o=this.anchorSize(),h=this.find("._anchor");h.forEach((t=>{t.setAttrs({width:o,height:o,offsetX:o/2,offsetY:o/2,stroke:this.anchorStroke(),strokeWidth:this.anchorStrokeWidth(),fill:this.anchorFill(),cornerRadius:this.anchorCornerRadius()})})),this._batchChangeChild(".top-left",{x:0,y:0,offsetX:o/2+a,offsetY:o/2+a,visible:r&&s.indexOf("top-left")>=0}),this._batchChangeChild(".top-center",{x:i/2,y:0,offsetY:o/2+a,visible:r&&s.indexOf("top-center")>=0}),this._batchChangeChild(".top-right",{x:i,y:0,offsetX:o/2-a,offsetY:o/2+a,visible:r&&s.indexOf("top-right")>=0}),this._batchChangeChild(".middle-left",{x:0,y:n/2,offsetX:o/2+a,visible:r&&s.indexOf("middle-left")>=0}),this._batchChangeChild(".middle-right",{x:i,y:n/2,offsetX:o/2-a,visible:r&&s.indexOf("middle-right")>=0}),this._batchChangeChild(".bottom-left",{x:0,y:n,offsetX:o/2+a,offsetY:o/2-a,visible:r&&s.indexOf("bottom-left")>=0}),this._batchChangeChild(".bottom-center",{x:i/2,y:n,offsetY:o/2-a,visible:r&&s.indexOf("bottom-center")>=0}),this._batchChangeChild(".bottom-right",{x:i,y:n,offsetX:o/2-a,offsetY:o/2-a,visible:r&&s.indexOf("bottom-right")>=0}),this._batchChangeChild(".rotater",{x:i/2,y:-this.rotateAnchorOffset()*g._sign(n)-a,visible:this.rotateEnabled()}),this._batchChangeChild(".back",{width:i,height:n,visible:this.borderEnabled(),stroke:this.borderStroke(),strokeWidth:this.borderStrokeWidth(),dash:this.borderDash(),x:0,y:0});const l=this.anchorStyleFunc();l&&h.forEach((t=>{l(t)})),null===(t=this.getLayer())||void 0===t||t.batchDraw()}isTransforming(){return this._transforming}stopTransform(){if(this._transforming){this._removeEvents();const t=this.findOne("."+this._movingAnchorName);t&&t.stopDrag()}}destroy(){return this.getStage()&&this._cursorChange&&this.getStage().content&&(this.getStage().content.style.cursor=""),Xt.prototype.destroy.call(this),this.detach(),this._removeEvents(),this}toObject(){return V.prototype.toObject.call(this)}clone(t){return V.prototype.clone.call(this,t)}getClientRect(){return this.nodes().length>0?super.getClientRect():{x:0,y:0,width:0,height:0}}}ri.isTransforming=()=>si>0,ri.prototype.className="Transformer",n(ri),w.addGetterSetter(ri,"enabledAnchors",ei,(function(t){return t instanceof Array||g.warn("enabledAnchors value should be an array"),t instanceof Array&&t.forEach((function(t){-1===ei.indexOf(t)&&g.warn("Unknown anchor name: "+t+". Available names are: "+ei.join(", "))})),t||[]})),w.addGetterSetter(ri,"flipEnabled",!0,x()),w.addGetterSetter(ri,"resizeEnabled",!0),w.addGetterSetter(ri,"anchorSize",10,p()),w.addGetterSetter(ri,"rotateEnabled",!0),w.addGetterSetter(ri,"rotateLineVisible",!0),w.addGetterSetter(ri,"rotationSnaps",[]),w.addGetterSetter(ri,"rotateAnchorOffset",50,p()),w.addGetterSetter(ri,"rotateAnchorCursor","crosshair"),w.addGetterSetter(ri,"rotationSnapTolerance",5,p()),w.addGetterSetter(ri,"borderEnabled",!0),w.addGetterSetter(ri,"anchorStroke","rgb(0, 161, 255)"),w.addGetterSetter(ri,"anchorStrokeWidth",1,p()),w.addGetterSetter(ri,"anchorFill","white"),w.addGetterSetter(ri,"anchorCornerRadius",0,p()),w.addGetterSetter(ri,"borderStroke","rgb(0, 161, 255)"),w.addGetterSetter(ri,"borderStrokeWidth",1,p()),w.addGetterSetter(ri,"borderDash"),w.addGetterSetter(ri,"keepRatio",!0),w.addGetterSetter(ri,"shiftBehavior","default"),w.addGetterSetter(ri,"centeredScaling",!1),w.addGetterSetter(ri,"ignoreStroke",!1),w.addGetterSetter(ri,"padding",0,p()),w.addGetterSetter(ri,"nodes"),w.addGetterSetter(ri,"node"),w.addGetterSetter(ri,"boundBoxFunc"),w.addGetterSetter(ri,"anchorDragBoundFunc"),w.addGetterSetter(ri,"anchorStyleFunc"),w.addGetterSetter(ri,"shouldOverdrawWholeArea",!1),w.addGetterSetter(ri,"useSingleNodeRotation",!0),w.backCompat(ri,{lineEnabled:"borderEnabled",rotateHandlerOffset:"rotateAnchorOffset",enabledHandlers:"enabledAnchors"});class ai extends Nt{_sceneFunc(t){t.beginPath(),t.arc(0,0,this.radius(),0,i.getAngle(this.angle()),this.clockwise()),t.lineTo(0,0),t.closePath(),t.fillStrokeShape(this)}getWidth(){return 2*this.radius()}getHeight(){return 2*this.radius()}setWidth(t){this.radius(t/2)}setHeight(t){this.radius(t/2)}}function oi(){this.r=0,this.g=0,this.b=0,this.a=0,this.next=null}ai.prototype.className="Wedge",ai.prototype._centroid=!0,ai.prototype._attrsAffectingSize=["radius"],n(ai),w.addGetterSetter(ai,"radius",0,p()),w.addGetterSetter(ai,"angle",0,p()),w.addGetterSetter(ai,"clockwise",!1),w.backCompat(ai,{angleDeg:"angle",getAngleDeg:"getAngle",setAngleDeg:"setAngle"});const hi=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],li=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];w.addGetterSetter(V,"blurRadius",0,p(),w.afterSetFilter);w.addGetterSetter(V,"brightness",0,p(),w.afterSetFilter);w.addGetterSetter(V,"contrast",0,p(),w.afterSetFilter);function di(t,e,i,n,s){const r=i-e,a=s-n;if(0===r)return n+a/2;if(0===a)return n;let o=(t-e)/r;return o=a*o+n,o}w.addGetterSetter(V,"embossStrength",.5,p(),w.afterSetFilter),w.addGetterSetter(V,"embossWhiteLevel",.5,p(),w.afterSetFilter),w.addGetterSetter(V,"embossDirection","top-left",void 0,w.afterSetFilter),w.addGetterSetter(V,"embossBlend",!1,void 0,w.afterSetFilter);w.addGetterSetter(V,"enhance",0,p(),w.afterSetFilter);w.addGetterSetter(V,"hue",0,p(),w.afterSetFilter),w.addGetterSetter(V,"saturation",0,p(),w.afterSetFilter),w.addGetterSetter(V,"luminance",0,p(),w.afterSetFilter);w.addGetterSetter(V,"hue",0,p(),w.afterSetFilter),w.addGetterSetter(V,"saturation",0,p(),w.afterSetFilter),w.addGetterSetter(V,"value",0,p(),w.afterSetFilter);function ci(t,e,i){let n=4*(i*t.width+e);const s=[];return s.push(t.data[n++],t.data[n++],t.data[n++],t.data[n++]),s}function gi(t,e){return Math.sqrt(Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)+Math.pow(t[2]-e[2],2))}w.addGetterSetter(V,"kaleidoscopePower",2,p(),w.afterSetFilter),w.addGetterSetter(V,"kaleidoscopeAngle",0,p(),w.afterSetFilter);w.addGetterSetter(V,"threshold",0,p(),w.afterSetFilter);w.addGetterSetter(V,"noise",.2,p(),w.afterSetFilter);w.addGetterSetter(V,"pixelSize",8,p(),w.afterSetFilter);w.addGetterSetter(V,"levels",.5,p(),w.afterSetFilter);w.addGetterSetter(V,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),w.addGetterSetter(V,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),w.addGetterSetter(V,"blue",0,f,w.afterSetFilter);w.addGetterSetter(V,"red",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),w.addGetterSetter(V,"green",0,(function(t){return this._filterUpToDate=!1,t>255?255:t<0?0:Math.round(t)})),w.addGetterSetter(V,"blue",0,f,w.afterSetFilter),w.addGetterSetter(V,"alpha",1,(function(t){return this._filterUpToDate=!1,t>1?1:t<0?0:t}));w.addGetterSetter(V,"threshold",.5,p(),w.afterSetFilter);return Zt.Util._assign(Zt,{Arc:te,Arrow:ue,Circle:fe,Ellipse:pe,Image:me,Label:we,Tag:Ce,Line:ne,Path:ge,Rect:Pe,RegularPolygon:ke,Ring:Te,Sprite:Me,Star:Ge,Text:Xe,TextPath:Ve,Transformer:ri,Wedge:ai,Filters:{Blur:function(t){const e=Math.round(this.blurRadius());e>0&&function(t,e){const i=t.data,n=t.width,s=t.height;let r,a,o,h,l,d,c,g,u,f,p,m,_,y,v,x,b,S,w,C,P,k,A,T;const M=e+e+1,G=n-1,R=s-1,E=e+1,D=E*(E+1)/2,L=new oi,I=hi[e],O=li[e];let F=null,B=L,N=null,H=null;for(o=1;o<M;o++)B=B.next=new oi,o===E&&(F=B);for(B.next=L,c=d=0,a=0;a<s;a++){for(x=b=S=w=g=u=f=p=0,m=E*(C=i[d]),_=E*(P=i[d+1]),y=E*(k=i[d+2]),v=E*(A=i[d+3]),g+=D*C,u+=D*P,f+=D*k,p+=D*A,B=L,o=0;o<E;o++)B.r=C,B.g=P,B.b=k,B.a=A,B=B.next;for(o=1;o<E;o++)h=d+((G<o?G:o)<<2),g+=(B.r=C=i[h])*(T=E-o),u+=(B.g=P=i[h+1])*T,f+=(B.b=k=i[h+2])*T,p+=(B.a=A=i[h+3])*T,x+=C,b+=P,S+=k,w+=A,B=B.next;for(N=L,H=F,r=0;r<n;r++)i[d+3]=A=p*I>>O,0!==A?(A=255/A,i[d]=(g*I>>O)*A,i[d+1]=(u*I>>O)*A,i[d+2]=(f*I>>O)*A):i[d]=i[d+1]=i[d+2]=0,g-=m,u-=_,f-=y,p-=v,m-=N.r,_-=N.g,y-=N.b,v-=N.a,h=c+((h=r+e+1)<G?h:G)<<2,x+=N.r=i[h],b+=N.g=i[h+1],S+=N.b=i[h+2],w+=N.a=i[h+3],g+=x,u+=b,f+=S,p+=w,N=N.next,m+=C=H.r,_+=P=H.g,y+=k=H.b,v+=A=H.a,x-=C,b-=P,S-=k,w-=A,H=H.next,d+=4;c+=n}for(r=0;r<n;r++){for(b=S=w=x=u=f=p=g=0,d=r<<2,m=E*(C=i[d]),_=E*(P=i[d+1]),y=E*(k=i[d+2]),v=E*(A=i[d+3]),g+=D*C,u+=D*P,f+=D*k,p+=D*A,B=L,o=0;o<E;o++)B.r=C,B.g=P,B.b=k,B.a=A,B=B.next;for(l=n,o=1;o<=e;o++)d=l+r<<2,g+=(B.r=C=i[d])*(T=E-o),u+=(B.g=P=i[d+1])*T,f+=(B.b=k=i[d+2])*T,p+=(B.a=A=i[d+3])*T,x+=C,b+=P,S+=k,w+=A,B=B.next,o<R&&(l+=n);for(d=r,N=L,H=F,a=0;a<s;a++)h=d<<2,i[h+3]=A=p*I>>O,A>0?(A=255/A,i[h]=(g*I>>O)*A,i[h+1]=(u*I>>O)*A,i[h+2]=(f*I>>O)*A):i[h]=i[h+1]=i[h+2]=0,g-=m,u-=_,f-=y,p-=v,m-=N.r,_-=N.g,y-=N.b,v-=N.a,h=r+((h=a+E)<R?h:R)*n<<2,g+=x+=N.r=i[h],u+=b+=N.g=i[h+1],f+=S+=N.b=i[h+2],p+=w+=N.a=i[h+3],N=N.next,m+=C=H.r,_+=P=H.g,y+=k=H.b,v+=A=H.a,x-=C,b-=P,S-=k,w-=A,H=H.next,d+=n}}(t,e)},Brighten:function(t){const e=255*this.brightness(),i=t.data,n=i.length;for(let t=0;t<n;t+=4)i[t]+=e,i[t+1]+=e,i[t+2]+=e},Contrast:function(t){const e=Math.pow((this.contrast()+100)/100,2),i=t.data,n=i.length;let s=150,r=150,a=150;for(let t=0;t<n;t+=4)s=i[t],r=i[t+1],a=i[t+2],s/=255,s-=.5,s*=e,s+=.5,s*=255,r/=255,r-=.5,r*=e,r+=.5,r*=255,a/=255,a-=.5,a*=e,a+=.5,a*=255,s=s<0?0:s>255?255:s,r=r<0?0:r>255?255:r,a=a<0?0:a>255?255:a,i[t]=s,i[t+1]=r,i[t+2]=a},Emboss:function(t){const e=10*this.embossStrength(),i=255*this.embossWhiteLevel(),n=this.embossDirection(),s=this.embossBlend(),r=t.data,a=t.width,o=t.height,h=4*a;let l=0,d=0,c=o;switch(n){case"top-left":l=-1,d=-1;break;case"top":l=-1,d=0;break;case"top-right":l=-1,d=1;break;case"right":l=0,d=1;break;case"bottom-right":l=1,d=1;break;case"bottom":l=1,d=0;break;case"bottom-left":l=1,d=-1;break;case"left":l=0,d=-1;break;default:g.error("Unknown emboss direction: "+n)}do{const t=(c-1)*h;let n=l;c+n<1&&(n=0),c+n>o&&(n=0);const g=(c-1+n)*a*4;let u=a;do{const n=t+4*(u-1);let o=d;u+o<1&&(o=0),u+o>a&&(o=0);const h=g+4*(u-1+o),l=r[n]-r[h],c=r[n+1]-r[h+1],f=r[n+2]-r[h+2];let p=l;const m=p>0?p:-p;if((c>0?c:-c)>m&&(p=c),(f>0?f:-f)>m&&(p=f),p*=e,s){const t=r[n]+p,e=r[n+1]+p,i=r[n+2]+p;r[n]=t>255?255:t<0?0:t,r[n+1]=e>255?255:e<0?0:e,r[n+2]=i>255?255:i<0?0:i}else{let t=i-p;t<0?t=0:t>255&&(t=255),r[n]=r[n+1]=r[n+2]=t}}while(--u)}while(--c)},Enhance:function(t){const e=t.data,i=e.length;let n,s,r,a=e[0],o=a,h=e[1],l=h,d=e[2],c=d;const g=this.enhance();if(0===g)return;for(let t=0;t<i;t+=4)n=e[t+0],n<a?a=n:n>o&&(o=n),s=e[t+1],s<h?h=s:s>l&&(l=s),r=e[t+2],r<d?d=r:r>c&&(c=r);let u,f,p,m,_,y,v,x,b;o===a&&(o=255,a=0),l===h&&(l=255,h=0),c===d&&(c=255,d=0),g>0?(f=o+g*(255-o),p=a-g*(a-0),_=l+g*(255-l),y=h-g*(h-0),x=c+g*(255-c),b=d-g*(d-0)):(u=.5*(o+a),f=o+g*(o-u),p=a+g*(a-u),m=.5*(l+h),_=l+g*(l-m),y=h+g*(h-m),v=.5*(c+d),x=c+g*(c-v),b=d+g*(d-v));for(let t=0;t<i;t+=4)e[t+0]=di(e[t+0],a,o,p,f),e[t+1]=di(e[t+1],h,l,y,_),e[t+2]=di(e[t+2],d,c,b,x)},Grayscale:function(t){const e=t.data,i=e.length;for(let t=0;t<i;t+=4){const i=.34*e[t]+.5*e[t+1]+.16*e[t+2];e[t]=i,e[t+1]=i,e[t+2]=i}},HSL:function(t){const e=t.data,i=e.length,n=Math.pow(2,this.saturation()),s=Math.abs(this.hue()+360)%360,r=127*this.luminance(),a=1*n*Math.cos(s*Math.PI/180),o=1*n*Math.sin(s*Math.PI/180),h=.299+.701*a+.167*o,l=.587-.587*a+.33*o,d=.114-.114*a-.497*o,c=.299-.299*a-.328*o,g=.587+.413*a+.035*o,u=.114-.114*a+.293*o,f=.299-.3*a+1.25*o,p=.587-.586*a-1.05*o,m=.114+.886*a-.2*o;let _,y,v,x;for(let t=0;t<i;t+=4)_=e[t+0],y=e[t+1],v=e[t+2],x=e[t+3],e[t+0]=h*_+l*y+d*v+r,e[t+1]=c*_+g*y+u*v+r,e[t+2]=f*_+p*y+m*v+r,e[t+3]=x},HSV:function(t){const e=t.data,i=e.length,n=Math.pow(2,this.value()),s=Math.pow(2,this.saturation()),r=Math.abs(this.hue()+360)%360,a=n*s*Math.cos(r*Math.PI/180),o=n*s*Math.sin(r*Math.PI/180),h=.299*n+.701*a+.167*o,l=.587*n-.587*a+.33*o,d=.114*n-.114*a-.497*o,c=.299*n-.299*a-.328*o,g=.587*n+.413*a+.035*o,u=.114*n-.114*a+.293*o,f=.299*n-.3*a+1.25*o,p=.587*n-.586*a-1.05*o,m=.114*n+.886*a-.2*o;let _,y,v,x;for(let t=0;t<i;t+=4)_=e[t+0],y=e[t+1],v=e[t+2],x=e[t+3],e[t+0]=h*_+l*y+d*v,e[t+1]=c*_+g*y+u*v,e[t+2]=f*_+p*y+m*v,e[t+3]=x},Invert:function(t){const e=t.data,i=e.length;for(let t=0;t<i;t+=4)e[t]=255-e[t],e[t+1]=255-e[t+1],e[t+2]=255-e[t+2]},Kaleidoscope:function(t){const e=t.width,i=t.height;let n,s,r,a,o,h,l,d,c,u,f=Math.round(this.kaleidoscopePower());const p=Math.round(this.kaleidoscopeAngle()),m=Math.floor(e*(p%360)/360);if(f<1)return;const _=g.createCanvasElement();_.width=e,_.height=i;const y=_.getContext("2d").getImageData(0,0,e,i);g.releaseCanvas(_),function(t,e,i){const n=t.data,s=e.data,r=t.width,a=t.height,o=i.polarCenterX||r/2,h=i.polarCenterY||a/2;let l=Math.sqrt(o*o+h*h),d=r-o,c=a-h;const g=Math.sqrt(d*d+c*c);l=g>l?g:l;const u=a,f=r,p=360/f*Math.PI/180;for(let t=0;t<f;t+=1){const e=Math.sin(t*p),i=Math.cos(t*p);for(let a=0;a<u;a+=1){d=Math.floor(o+l*a/u*i),c=Math.floor(h+l*a/u*e);let g=4*(c*r+d);const f=n[g+0],p=n[g+1],m=n[g+2],_=n[g+3];g=4*(t+a*r),s[g+0]=f,s[g+1]=p,s[g+2]=m,s[g+3]=_}}}(t,y,{polarCenterX:e/2,polarCenterY:i/2});let v=e/Math.pow(2,f);for(;v<=8;)v*=2,f-=1;v=Math.ceil(v);let x=v,b=0,S=x,w=1;for(m+v>e&&(b=x,S=0,w=-1),s=0;s<i;s+=1)for(n=b;n!==S;n+=w)r=Math.round(n+m)%e,c=4*(e*s+r),o=y.data[c+0],h=y.data[c+1],l=y.data[c+2],d=y.data[c+3],u=4*(e*s+n),y.data[u+0]=o,y.data[u+1]=h,y.data[u+2]=l,y.data[u+3]=d;for(s=0;s<i;s+=1)for(x=Math.floor(v),a=0;a<f;a+=1){for(n=0;n<x+1;n+=1)c=4*(e*s+n),o=y.data[c+0],h=y.data[c+1],l=y.data[c+2],d=y.data[c+3],u=4*(e*s+2*x-n-1),y.data[u+0]=o,y.data[u+1]=h,y.data[u+2]=l,y.data[u+3]=d;x*=2}!function(t,e,i){const n=t.data,s=e.data,r=t.width,a=t.height,o=i.polarCenterX||r/2,h=i.polarCenterY||a/2;let l=Math.sqrt(o*o+h*h),d=r-o,c=a-h;const g=Math.sqrt(d*d+c*c);l=g>l?g:l;const u=a,f=r;let p,m;for(d=0;d<r;d+=1)for(c=0;c<a;c+=1){const t=d-o,e=c-h,i=Math.sqrt(t*t+e*e)*u/l;let a=(180*Math.atan2(e,t)/Math.PI+360+0)%360;a=a*f/360,p=Math.floor(a),m=Math.floor(i);let g=4*(m*r+p);const _=n[g+0],y=n[g+1],v=n[g+2],x=n[g+3];g=4*(c*r+d),s[g+0]=_,s[g+1]=y,s[g+2]=v,s[g+3]=x}}(y,t,{polarRotation:0})},Mask:function(t){let e=function(t,e){const i=ci(t,0,0),n=ci(t,t.width-1,0),s=ci(t,0,t.height-1),r=ci(t,t.width-1,t.height-1),a=e||10;if(gi(i,n)<a&&gi(n,r)<a&&gi(r,s)<a&&gi(s,i)<a){const e=function(t){const e=[0,0,0];for(let i=0;i<t.length;i++)e[0]+=t[i][0],e[1]+=t[i][1],e[2]+=t[i][2];return e[0]/=t.length,e[1]/=t.length,e[2]/=t.length,e}([n,i,r,s]),o=[];for(let i=0;i<t.width*t.height;i++){const n=gi(e,[t.data[4*i],t.data[4*i+1],t.data[4*i+2]]);o[i]=n<a?0:255}return o}}(t,this.threshold());return e&&(e=function(t,e,i){const n=[1,1,1,1,0,1,1,1,1],s=Math.round(Math.sqrt(n.length)),r=Math.floor(s/2),a=[];for(let o=0;o<i;o++)for(let h=0;h<e;h++){const l=o*e+h;let d=0;for(let a=0;a<s;a++)for(let l=0;l<s;l++){const c=o+a-r,g=h+l-r;if(c>=0&&c<i&&g>=0&&g<e){const i=n[a*s+l];d+=t[c*e+g]*i}}a[l]=2040===d?255:0}return a}(e,t.width,t.height),e=function(t,e,i){const n=[1,1,1,1,1,1,1,1,1],s=Math.round(Math.sqrt(n.length)),r=Math.floor(s/2),a=[];for(let o=0;o<i;o++)for(let h=0;h<e;h++){const l=o*e+h;let d=0;for(let a=0;a<s;a++)for(let l=0;l<s;l++){const c=o+a-r,g=h+l-r;if(c>=0&&c<i&&g>=0&&g<e){const i=n[a*s+l];d+=t[c*e+g]*i}}a[l]=d>=1020?255:0}return a}(e,t.width,t.height),e=function(t,e,i){const n=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],s=Math.round(Math.sqrt(n.length)),r=Math.floor(s/2),a=[];for(let o=0;o<i;o++)for(let h=0;h<e;h++){const l=o*e+h;let d=0;for(let a=0;a<s;a++)for(let l=0;l<s;l++){const c=o+a-r,g=h+l-r;if(c>=0&&c<i&&g>=0&&g<e){const i=n[a*s+l];d+=t[c*e+g]*i}}a[l]=d}return a}(e,t.width,t.height),function(t,e){for(let i=0;i<t.width*t.height;i++)t.data[4*i+3]=e[i]}(t,e)),t},Noise:function(t){const e=255*this.noise(),i=t.data,n=i.length,s=e/2;for(let t=0;t<n;t+=4)i[t+0]+=s-2*s*Math.random(),i[t+1]+=s-2*s*Math.random(),i[t+2]+=s-2*s*Math.random()},Pixelate:function(t){let e,i,n,s,r,a,o,h,l,d,c,u,f,p,m=Math.ceil(this.pixelSize()),_=t.width,y=t.height,v=Math.ceil(_/m),x=Math.ceil(y/m),b=t.data;if(m<=0)g.error("pixelSize value can not be <= 0");else for(u=0;u<v;u+=1)for(f=0;f<x;f+=1){for(s=0,r=0,a=0,o=0,h=u*m,l=h+m,d=f*m,c=d+m,p=0,e=h;e<l;e+=1)if(!(e>=_))for(i=d;i<c;i+=1)i>=y||(n=4*(_*i+e),s+=b[n+0],r+=b[n+1],a+=b[n+2],o+=b[n+3],p+=1);for(s/=p,r/=p,a/=p,o/=p,e=h;e<l;e+=1)if(!(e>=_))for(i=d;i<c;i+=1)i>=y||(n=4*(_*i+e),b[n+0]=s,b[n+1]=r,b[n+2]=a,b[n+3]=o)}},Posterize:function(t){const e=Math.round(254*this.levels())+1,i=t.data,n=i.length,s=255/e;for(let t=0;t<n;t+=1)i[t]=Math.floor(i[t]/s)*s},RGB:function(t){const e=t.data,i=e.length,n=this.red(),s=this.green(),r=this.blue();for(let t=0;t<i;t+=4){const i=(.34*e[t]+.5*e[t+1]+.16*e[t+2])/255;e[t]=i*n,e[t+1]=i*s,e[t+2]=i*r,e[t+3]=e[t+3]}},RGBA:function(t){const e=t.data,i=e.length,n=this.red(),s=this.green(),r=this.blue(),a=this.alpha();for(let t=0;t<i;t+=4){const i=1-a;e[t]=n*a+e[t]*i,e[t+1]=s*a+e[t+1]*i,e[t+2]=r*a+e[t+2]*i}},Sepia:function(t){const e=t.data,i=e.length;for(let t=0;t<i;t+=4){const i=e[t+0],n=e[t+1],s=e[t+2];e[t+0]=Math.min(255,.393*i+.769*n+.189*s),e[t+1]=Math.min(255,.349*i+.686*n+.168*s),e[t+2]=Math.min(255,.272*i+.534*n+.131*s)}},Solarize:function(t){const e=t.data,i=t.width,n=4*i;let s=t.height;do{const t=(s-1)*n;let r=i;do{const i=t+4*(r-1);let n=e[i],s=e[i+1],a=e[i+2];n>127&&(n=255-n),s>127&&(s=255-s),a>127&&(a=255-a),e[i]=n,e[i+1]=s,e[i+2]=a}while(--r)}while(--s)},Threshold:function(t){const e=255*this.threshold(),i=t.data,n=i.length;for(let t=0;t<n;t+=1)i[t]=i[t]<e?0:255}}})}));