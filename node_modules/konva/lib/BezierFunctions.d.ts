export declare const tValues: number[][];
export declare const cValues: number[][];
export declare const binomialCoefficients: number[][];
export declare const getCubicArcLength: (xs: number[], ys: number[], t: number) => number;
export declare const getQuadraticArcLength: (xs: number[], ys: number[], t: number) => number;
export declare const t2length: (length: number, totalLength: number, func: (t: number) => number) => number;
