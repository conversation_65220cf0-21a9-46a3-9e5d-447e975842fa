import { _EventEmitter } from "./internal/events/events.mjs";
import { notImplemented } from "../_internal/utils.mjs";
export { _EventEmitter as EventEmitter, EventEmitterAsyncResource, addAbortListener, getEventListeners, getMaxListeners, on, once } from "./internal/events/events.mjs";
export const usingDomains = false;
export const captureRejectionSymbol = /* @__PURE__ */ Symbol.for("nodejs.rejection");
export const captureRejections = false;
export const errorMonitor = /* @__PURE__ */ Symbol.for("events.errorMonitor");
export const defaultMaxListeners = 10;
export const setMaxListeners = /* @__PURE__ */ notImplemented("node:events.setMaxListeners");
export const listenerCount = /* @__PURE__ */ notImplemented("node:events.listenerCount");
export const init = /* @__PURE__ */ notImplemented("node:events.init");
export default _EventEmitter;
