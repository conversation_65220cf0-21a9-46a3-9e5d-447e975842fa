/*!
* The buffer module from node.js, for the browser.
*
* <AUTHOR> <https://feross.org>
* @license  MIT
*/
import * as base64 from "./base64.mjs";
import * as ieee754 from "./ieee754.mjs";
const customInspectSymbol = typeof Symbol === "function" && typeof Symbol["for"] === "function" ? Symbol["for"]("nodejs.util.inspect.custom") : null;
export const INSPECT_MAX_BYTES = 50;
const K_MAX_LENGTH = **********;
export const kMaxLength = K_MAX_LENGTH;
/**
* If `Buffer.TYPED_ARRAY_SUPPORT`:
*   === true    Use Uint8Array implementation (fastest)
*   === false   Print warning and recommend using `buffer` v4.x which has an Object
*               implementation (most compatible, even IE6)
*
* Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
* Opera 11.6+, iOS 4.2+.
*
* We report that the browser does not support typed arrays if the are not subclassable
* using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`
* (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support
* for __proto__ and has a buggy typed array implementation.
*/
Buffer.TYPED_ARRAY_SUPPORT = typedArraySupport();
if (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== "undefined" && typeof console.error === "function") {
	console.error("This environment lacks typed array (Uint8Array) support which is required by " + "`buffer` v5.x. Use `buffer` v4.x if you require old browser support.");
}
function typedArraySupport() {
	try {
		const arr = new Uint8Array(1);
		const proto = { foo: function() {
			return 42;
		} };
		Object.setPrototypeOf(proto, Uint8Array.prototype);
		Object.setPrototypeOf(arr, proto);
		return arr.foo() === 42;
	} catch {
		return false;
	}
}
Object.defineProperty(Buffer.prototype, "parent", {
	enumerable: true,
	get: function() {
		if (!Buffer.isBuffer(this)) {
			return;
		}
		return this.buffer;
	}
});
Object.defineProperty(Buffer.prototype, "offset", {
	enumerable: true,
	get: function() {
		if (!Buffer.isBuffer(this)) {
			return;
		}
		return this.byteOffset;
	}
});
function createBuffer(length) {
	if (length > K_MAX_LENGTH) {
		throw new RangeError("The value \"" + length + "\" is invalid for option \"size\"");
	}
	const buf = new Uint8Array(length);
	Object.setPrototypeOf(buf, Buffer.prototype);
	return buf;
}
/**
* The Buffer constructor returns instances of `Uint8Array` that have their
* prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
* `Uint8Array`, so the returned instances will have all the node `Buffer` methods
* and the `Uint8Array` methods. Square bracket notation works as expected -- it
* returns a single octet.
*
* The `Uint8Array` prototype remains unmodified.
*/
export function Buffer(arg, encodingOrOffset, length) {
	if (typeof arg === "number") {
		if (typeof encodingOrOffset === "string") {
			throw new TypeError("The \"string\" argument must be of type string. Received type number");
		}
		return allocUnsafe(arg);
	}
	return from(arg, encodingOrOffset, length);
}
Buffer.poolSize = 8192;
function from(value, encodingOrOffset, length) {
	if (typeof value === "string") {
		return fromString(value, encodingOrOffset);
	}
	if (ArrayBuffer.isView(value)) {
		return fromArrayView(value);
	}
	if (value == null) {
		throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, " + "or Array-like Object. Received type " + typeof value);
	}
	if (isInstance(value, ArrayBuffer) || value && isInstance(value.buffer, ArrayBuffer)) {
		return fromArrayBuffer(value, encodingOrOffset, length);
	}
	if (typeof SharedArrayBuffer !== "undefined" && (isInstance(value, SharedArrayBuffer) || value && isInstance(value.buffer, SharedArrayBuffer))) {
		return fromArrayBuffer(value, encodingOrOffset, length);
	}
	if (typeof value === "number") {
		throw new TypeError("The \"value\" argument must not be of type number. Received type number");
	}
	const valueOf = value.valueOf && value.valueOf();
	if (valueOf != null && valueOf !== value) {
		return Buffer.from(valueOf, encodingOrOffset, length);
	}
	const b = fromObject(value);
	if (b) {
		return b;
	}
	if (typeof Symbol !== "undefined" && Symbol.toPrimitive != null && typeof value[Symbol.toPrimitive] === "function") {
		return Buffer.from(value[Symbol.toPrimitive]("string"), encodingOrOffset, length);
	}
	throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, " + "or Array-like Object. Received type " + typeof value);
}
/**
* Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
* if value is a number.
* Buffer.from(str[, encoding])
* Buffer.from(array)
* Buffer.from(buffer)
* Buffer.from(arrayBuffer[, byteOffset[, length]])
**/
Buffer.from = function(value, encodingOrOffset, length) {
	return from(value, encodingOrOffset, length);
};
Object.setPrototypeOf(Buffer.prototype, Uint8Array.prototype);
Object.setPrototypeOf(Buffer, Uint8Array);
function assertSize(size) {
	if (typeof size !== "number") {
		throw new TypeError("\"size\" argument must be of type number");
	} else if (size < 0) {
		throw new RangeError("The value \"" + size + "\" is invalid for option \"size\"");
	}
}
function alloc(size, fill, encoding) {
	assertSize(size);
	if (size <= 0) {
		return createBuffer(size);
	}
	if (fill !== undefined) {
		return typeof encoding === "string" ? createBuffer(size).fill(fill, encoding) : createBuffer(size).fill(fill);
	}
	return createBuffer(size);
}
/**
* Creates a new filled Buffer instance.
* alloc(size[, fill[, encoding]])
**/
Buffer.alloc = function(size, fill, encoding) {
	return alloc(size, fill, encoding);
};
function allocUnsafe(size) {
	assertSize(size);
	return createBuffer(size < 0 ? 0 : checked(size) | 0);
}
/**
* Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
* */
Buffer.allocUnsafe = function(size) {
	return allocUnsafe(size);
};
/**
* Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
*/
Buffer.allocUnsafeSlow = function(size) {
	return allocUnsafe(size);
};
function fromString(string, encoding) {
	if (typeof encoding !== "string" || encoding === "") {
		encoding = "utf8";
	}
	if (!Buffer.isEncoding(encoding)) {
		throw new TypeError("Unknown encoding: " + encoding);
	}
	const length = byteLength(string, encoding) | 0;
	let buf = createBuffer(length);
	const actual = buf.write(string, encoding);
	if (actual !== length) {
		buf = buf.slice(0, actual);
	}
	return buf;
}
function fromArrayLike(array) {
	const length = array.length < 0 ? 0 : checked(array.length) | 0;
	const buf = createBuffer(length);
	for (let i = 0; i < length; i += 1) {
		buf[i] = array[i] & 255;
	}
	return buf;
}
function fromArrayView(arrayView) {
	if (isInstance(arrayView, Uint8Array)) {
		const copy = new Uint8Array(arrayView);
		return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength);
	}
	return fromArrayLike(arrayView);
}
function fromArrayBuffer(array, byteOffset, length) {
	if (byteOffset < 0 || array.byteLength < byteOffset) {
		throw new RangeError("\"offset\" is outside of buffer bounds");
	}
	if (array.byteLength < byteOffset + (length || 0)) {
		throw new RangeError("\"length\" is outside of buffer bounds");
	}
	let buf;
	if (byteOffset === undefined && length === undefined) {
		buf = new Uint8Array(array);
	} else if (length === undefined) {
		buf = new Uint8Array(array, byteOffset);
	} else {
		buf = new Uint8Array(array, byteOffset, length);
	}
	Object.setPrototypeOf(buf, Buffer.prototype);
	return buf;
}
function fromObject(obj) {
	if (Buffer.isBuffer(obj)) {
		const len = checked(obj.length) | 0;
		const buf = createBuffer(len);
		if (buf.length === 0) {
			return buf;
		}
		obj.copy(buf, 0, 0, len);
		return buf;
	}
	if (obj.length !== undefined) {
		if (typeof obj.length !== "number" || numberIsNaN(obj.length)) {
			return createBuffer(0);
		}
		return fromArrayLike(obj);
	}
	if (obj.type === "Buffer" && Array.isArray(obj.data)) {
		return fromArrayLike(obj.data);
	}
}
function checked(length) {
	if (length >= K_MAX_LENGTH) {
		throw new RangeError("Attempt to allocate Buffer larger than maximum " + "size: 0x" + K_MAX_LENGTH.toString(16) + " bytes");
	}
	return length | 0;
}
export function SlowBuffer(length) {
	if (+length != length) {
		length = 0;
	}
	return Buffer.alloc(+length);
}
Buffer.isBuffer = function isBuffer(b) {
	return b != null && b._isBuffer === true && b !== Buffer.prototype;
};
Buffer.compare = function compare(a, b) {
	if (isInstance(a, Uint8Array)) {
		a = Buffer.from(a, a.offset, a.byteLength);
	}
	if (isInstance(b, Uint8Array)) {
		b = Buffer.from(b, b.offset, b.byteLength);
	}
	if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
		throw new TypeError("The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array");
	}
	if (a === b) {
		return 0;
	}
	let x = a.length;
	let y = b.length;
	for (let i = 0, len = Math.min(x, y); i < len; ++i) {
		if (a[i] !== b[i]) {
			x = a[i];
			y = b[i];
			break;
		}
	}
	if (x < y) {
		return -1;
	}
	if (y < x) {
		return 1;
	}
	return 0;
};
Buffer.isEncoding = function isEncoding(encoding) {
	switch (String(encoding).toLowerCase()) {
		case "hex":
		case "utf8":
		case "utf-8":
		case "ascii":
		case "latin1":
		case "binary":
		case "base64":
		case "ucs2":
		case "ucs-2":
		case "utf16le":
		case "utf-16le": return true;
		default: return false;
	}
};
Buffer.concat = function concat(list, length) {
	if (!Array.isArray(list)) {
		throw new TypeError("\"list\" argument must be an Array of Buffers");
	}
	if (list.length === 0) {
		return Buffer.alloc(0);
	}
	let i;
	if (length === undefined) {
		length = 0;
		for (i = 0; i < list.length; ++i) {
			length += list[i].length;
		}
	}
	const buffer = Buffer.allocUnsafe(length);
	let pos = 0;
	for (i = 0; i < list.length; ++i) {
		let buf = list[i];
		if (isInstance(buf, Uint8Array)) {
			if (pos + buf.length > buffer.length) {
				if (!Buffer.isBuffer(buf)) {
					buf = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);
				}
				buf.copy(buffer, pos);
			} else {
				Uint8Array.prototype.set.call(buffer, buf, pos);
			}
		} else if (Buffer.isBuffer(buf)) {
			buf.copy(buffer, pos);
		} else {
			throw new TypeError("\"list\" argument must be an Array of Buffers");
		}
		pos += buf.length;
	}
	return buffer;
};
function byteLength(string, encoding) {
	if (Buffer.isBuffer(string)) {
		return string.length;
	}
	if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {
		return string.byteLength;
	}
	if (typeof string !== "string") {
		throw new TypeError("The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. " + "Received type " + typeof string);
	}
	const len = string.length;
	const mustMatch = arguments.length > 2 && arguments[2] === true;
	if (!mustMatch && len === 0) {
		return 0;
	}
	let loweredCase = false;
	for (;;) {
		switch (encoding) {
			case "ascii":
			case "latin1":
			case "binary": return len;
			case "utf8":
			case "utf-8": return utf8ToBytes(string).length;
			case "ucs2":
			case "ucs-2":
			case "utf16le":
			case "utf-16le": return len * 2;
			case "hex": return len >>> 1;
			case "base64": return base64ToBytes(string).length;
			default:
				if (loweredCase) {
					return mustMatch ? -1 : utf8ToBytes(string).length;
				}
				encoding = ("" + encoding).toLowerCase();
				loweredCase = true;
		}
	}
}
Buffer.byteLength = byteLength;
function slowToString(encoding, start, end) {
	let loweredCase = false;
	if (start === undefined || start < 0) {
		start = 0;
	}
	if (start > this.length) {
		return "";
	}
	if (end === undefined || end > this.length) {
		end = this.length;
	}
	if (end <= 0) {
		return "";
	}
	end >>>= 0;
	start >>>= 0;
	if (end <= start) {
		return "";
	}
	if (!encoding) {
		encoding = "utf8";
	}
	while (true) {
		switch (encoding) {
			case "hex": return hexSlice(this, start, end);
			case "utf8":
			case "utf-8": return utf8Slice(this, start, end);
			case "ascii": return asciiSlice(this, start, end);
			case "latin1":
			case "binary": return latin1Slice(this, start, end);
			case "base64": return base64Slice(this, start, end);
			case "ucs2":
			case "ucs-2":
			case "utf16le":
			case "utf-16le": return utf16leSlice(this, start, end);
			default:
				if (loweredCase) {
					throw new TypeError("Unknown encoding: " + encoding);
				}
				encoding = (encoding + "").toLowerCase();
				loweredCase = true;
		}
	}
}
Buffer.prototype._isBuffer = true;
function swap(b, n, m) {
	const i = b[n];
	b[n] = b[m];
	b[m] = i;
}
Buffer.prototype.swap16 = function swap16() {
	const len = this.length;
	if (len % 2 !== 0) {
		throw new RangeError("Buffer size must be a multiple of 16-bits");
	}
	for (let i = 0; i < len; i += 2) {
		swap(this, i, i + 1);
	}
	return this;
};
Buffer.prototype.swap32 = function swap32() {
	const len = this.length;
	if (len % 4 !== 0) {
		throw new RangeError("Buffer size must be a multiple of 32-bits");
	}
	for (let i = 0; i < len; i += 4) {
		swap(this, i, i + 3);
		swap(this, i + 1, i + 2);
	}
	return this;
};
Buffer.prototype.swap64 = function swap64() {
	const len = this.length;
	if (len % 8 !== 0) {
		throw new RangeError("Buffer size must be a multiple of 64-bits");
	}
	for (let i = 0; i < len; i += 8) {
		swap(this, i, i + 7);
		swap(this, i + 1, i + 6);
		swap(this, i + 2, i + 5);
		swap(this, i + 3, i + 4);
	}
	return this;
};
Buffer.prototype.toString = function toString() {
	const length = this.length;
	if (length === 0) {
		return "";
	}
	if (arguments.length === 0) {
		return utf8Slice(this, 0, length);
	}
	return Reflect.apply(slowToString, this, arguments);
};
Buffer.prototype.toLocaleString = Buffer.prototype.toString;
Buffer.prototype.equals = function equals(b) {
	if (!Buffer.isBuffer(b)) {
		throw new TypeError("Argument must be a Buffer");
	}
	if (this === b) {
		return true;
	}
	return Buffer.compare(this, b) === 0;
};
Buffer.prototype.inspect = function inspect() {
	let str = "";
	const max = INSPECT_MAX_BYTES;
	str = this.toString("hex", 0, max).replace(/(.{2})/g, "$1 ").trim();
	if (this.length > max) {
		str += " ... ";
	}
	return "<Buffer " + str + ">";
};
if (customInspectSymbol) {
	Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect;
}
Buffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {
	if (isInstance(target, Uint8Array)) {
		target = Buffer.from(target, target.offset, target.byteLength);
	}
	if (!Buffer.isBuffer(target)) {
		throw new TypeError("The \"target\" argument must be one of type Buffer or Uint8Array. " + "Received type " + typeof target);
	}
	if (start === undefined) {
		start = 0;
	}
	if (end === undefined) {
		end = target ? target.length : 0;
	}
	if (thisStart === undefined) {
		thisStart = 0;
	}
	if (thisEnd === undefined) {
		thisEnd = this.length;
	}
	if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
		throw new RangeError("out of range index");
	}
	if (thisStart >= thisEnd && start >= end) {
		return 0;
	}
	if (thisStart >= thisEnd) {
		return -1;
	}
	if (start >= end) {
		return 1;
	}
	start >>>= 0;
	end >>>= 0;
	thisStart >>>= 0;
	thisEnd >>>= 0;
	if (this === target) {
		return 0;
	}
	let x = thisEnd - thisStart;
	let y = end - start;
	const len = Math.min(x, y);
	const thisCopy = this.slice(thisStart, thisEnd);
	const targetCopy = target.slice(start, end);
	for (let i = 0; i < len; ++i) {
		if (thisCopy[i] !== targetCopy[i]) {
			x = thisCopy[i];
			y = targetCopy[i];
			break;
		}
	}
	if (x < y) {
		return -1;
	}
	if (y < x) {
		return 1;
	}
	return 0;
};
function bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {
	if (buffer.length === 0) {
		return -1;
	}
	if (typeof byteOffset === "string") {
		encoding = byteOffset;
		byteOffset = 0;
	} else if (byteOffset > **********) {
		byteOffset = **********;
	} else if (byteOffset < -2147483648) {
		byteOffset = -2147483648;
	}
	byteOffset = +byteOffset;
	if (numberIsNaN(byteOffset)) {
		byteOffset = dir ? 0 : buffer.length - 1;
	}
	if (byteOffset < 0) {
		byteOffset = buffer.length + byteOffset;
	}
	if (byteOffset >= buffer.length) {
		if (dir) {
			return -1;
		} else {
			byteOffset = buffer.length - 1;
		}
	} else if (byteOffset < 0) {
		if (dir) {
			byteOffset = 0;
		} else {
			return -1;
		}
	}
	if (typeof val === "string") {
		val = Buffer.from(val, encoding);
	}
	if (Buffer.isBuffer(val)) {
		if (val.length === 0) {
			return -1;
		}
		return arrayIndexOf(buffer, val, byteOffset, encoding, dir);
	} else if (typeof val === "number") {
		val = val & 255;
		if (typeof Uint8Array.prototype.indexOf === "function") {
			return dir ? Uint8Array.prototype.indexOf.call(buffer, val, byteOffset) : Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);
		}
		return arrayIndexOf(buffer, [val], byteOffset, encoding, dir);
	}
	throw new TypeError("val must be string, number or Buffer");
}
function arrayIndexOf(arr, val, byteOffset, encoding, dir) {
	let indexSize = 1;
	let arrLength = arr.length;
	let valLength = val.length;
	if (encoding !== undefined) {
		encoding = String(encoding).toLowerCase();
		if (encoding === "ucs2" || encoding === "ucs-2" || encoding === "utf16le" || encoding === "utf-16le") {
			if (arr.length < 2 || val.length < 2) {
				return -1;
			}
			indexSize = 2;
			arrLength /= 2;
			valLength /= 2;
			byteOffset /= 2;
		}
	}
	function read(buf, i) {
		return indexSize === 1 ? buf[i] : buf.readUInt16BE(i * indexSize);
	}
	let i;
	if (dir) {
		let foundIndex = -1;
		for (i = byteOffset; i < arrLength; i++) {
			if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
				if (foundIndex === -1) {
					foundIndex = i;
				}
				if (i - foundIndex + 1 === valLength) {
					return foundIndex * indexSize;
				}
			} else {
				if (foundIndex !== -1) {
					i -= i - foundIndex;
				}
				foundIndex = -1;
			}
		}
	} else {
		if (byteOffset + valLength > arrLength) {
			byteOffset = arrLength - valLength;
		}
		for (i = byteOffset; i >= 0; i--) {
			let found = true;
			for (let j = 0; j < valLength; j++) {
				if (read(arr, i + j) !== read(val, j)) {
					found = false;
					break;
				}
			}
			if (found) {
				return i;
			}
		}
	}
	return -1;
}
Buffer.prototype.includes = function includes(val, byteOffset, encoding) {
	return this.indexOf(val, byteOffset, encoding) !== -1;
};
Buffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {
	return bidirectionalIndexOf(this, val, byteOffset, encoding, true);
};
Buffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {
	return bidirectionalIndexOf(this, val, byteOffset, encoding, false);
};
function hexWrite(buf, string, offset, length) {
	offset = Number(offset) || 0;
	const remaining = buf.length - offset;
	if (length) {
		length = Number(length);
		if (length > remaining) {
			length = remaining;
		}
	} else {
		length = remaining;
	}
	const strLen = string.length;
	if (length > strLen / 2) {
		length = strLen / 2;
	}
	let i;
	for (i = 0; i < length; ++i) {
		const parsed = Number.parseInt(string.slice(i * 2, i * 2 + 2), 16);
		if (numberIsNaN(parsed)) {
			return i;
		}
		buf[offset + i] = parsed;
	}
	return i;
}
function utf8Write(buf, string, offset, length) {
	return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);
}
function asciiWrite(buf, string, offset, length) {
	return blitBuffer(asciiToBytes(string), buf, offset, length);
}
function base64Write(buf, string, offset, length) {
	return blitBuffer(base64ToBytes(string), buf, offset, length);
}
function ucs2Write(buf, string, offset, length) {
	return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);
}
Buffer.prototype.write = function write(string, offset, length, encoding) {
	if (offset === undefined) {
		encoding = "utf8";
		length = this.length;
		offset = 0;
	} else if (length === undefined && typeof offset === "string") {
		encoding = offset;
		length = this.length;
		offset = 0;
	} else if (Number.isFinite(offset)) {
		offset = offset >>> 0;
		if (Number.isFinite(length)) {
			length = length >>> 0;
			if (encoding === undefined) {
				encoding = "utf8";
			}
		} else {
			encoding = length;
			length = undefined;
		}
	} else {
		throw new TypeError("Buffer.write(string, encoding, offset[, length]) is no longer supported");
	}
	const remaining = this.length - offset;
	if (length === undefined || length > remaining) {
		length = remaining;
	}
	if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {
		throw new RangeError("Attempt to write outside buffer bounds");
	}
	if (!encoding) {
		encoding = "utf8";
	}
	let loweredCase = false;
	for (;;) {
		switch (encoding) {
			case "hex": return hexWrite(this, string, offset, length);
			case "utf8":
			case "utf-8": return utf8Write(this, string, offset, length);
			case "ascii":
			case "latin1":
			case "binary": return asciiWrite(this, string, offset, length);
			case "base64": return base64Write(this, string, offset, length);
			case "ucs2":
			case "ucs-2":
			case "utf16le":
			case "utf-16le": return ucs2Write(this, string, offset, length);
			default:
				if (loweredCase) {
					throw new TypeError("Unknown encoding: " + encoding);
				}
				encoding = ("" + encoding).toLowerCase();
				loweredCase = true;
		}
	}
};
Buffer.prototype.toJSON = function toJSON() {
	return {
		type: "Buffer",
		data: Array.prototype.slice.call(this._arr || this, 0)
	};
};
function base64Slice(buf, start, end) {
	return start === 0 && end === buf.length ? base64.fromByteArray(buf) : base64.fromByteArray(buf.slice(start, end));
}
function utf8Slice(buf, start, end) {
	end = Math.min(buf.length, end);
	const res = [];
	let i = start;
	while (i < end) {
		const firstByte = buf[i];
		let codePoint = null;
		let bytesPerSequence = firstByte > 239 ? 4 : firstByte > 223 ? 3 : firstByte > 191 ? 2 : 1;
		if (i + bytesPerSequence <= end) {
			let secondByte, thirdByte, fourthByte, tempCodePoint;
			switch (bytesPerSequence) {
				case 1:
					if (firstByte < 128) {
						codePoint = firstByte;
					}
					break;
				case 2:
					secondByte = buf[i + 1];
					if ((secondByte & 192) === 128) {
						tempCodePoint = (firstByte & 31) << 6 | secondByte & 63;
						if (tempCodePoint > 127) {
							codePoint = tempCodePoint;
						}
					}
					break;
				case 3:
					secondByte = buf[i + 1];
					thirdByte = buf[i + 2];
					if ((secondByte & 192) === 128 && (thirdByte & 192) === 128) {
						tempCodePoint = (firstByte & 15) << 12 | (secondByte & 63) << 6 | thirdByte & 63;
						if (tempCodePoint > 2047 && (tempCodePoint < 55296 || tempCodePoint > 57343)) {
							codePoint = tempCodePoint;
						}
					}
					break;
				case 4:
					secondByte = buf[i + 1];
					thirdByte = buf[i + 2];
					fourthByte = buf[i + 3];
					if ((secondByte & 192) === 128 && (thirdByte & 192) === 128 && (fourthByte & 192) === 128) {
						tempCodePoint = (firstByte & 15) << 18 | (secondByte & 63) << 12 | (thirdByte & 63) << 6 | fourthByte & 63;
						if (tempCodePoint > 65535 && tempCodePoint < 1114112) {
							codePoint = tempCodePoint;
						}
					}
			}
		}
		if (codePoint === null) {
			codePoint = 65533;
			bytesPerSequence = 1;
		} else if (codePoint > 65535) {
			codePoint -= 65536;
			res.push(codePoint >>> 10 & 1023 | 55296);
			codePoint = 56320 | codePoint & 1023;
		}
		res.push(codePoint);
		i += bytesPerSequence;
	}
	return decodeCodePointsArray(res);
}
const MAX_ARGUMENTS_LENGTH = 4096;
function decodeCodePointsArray(codePoints) {
	const len = codePoints.length;
	if (len <= MAX_ARGUMENTS_LENGTH) {
		return String.fromCharCode.apply(String, codePoints);
	}
	let res = "";
	let i = 0;
	while (i < len) {
		res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));
	}
	return res;
}
function asciiSlice(buf, start, end) {
	let ret = "";
	end = Math.min(buf.length, end);
	for (let i = start; i < end; ++i) {
		ret += String.fromCharCode(buf[i] & 127);
	}
	return ret;
}
function latin1Slice(buf, start, end) {
	let ret = "";
	end = Math.min(buf.length, end);
	for (let i = start; i < end; ++i) {
		ret += String.fromCharCode(buf[i]);
	}
	return ret;
}
function hexSlice(buf, start, end) {
	const len = buf.length;
	if (!start || start < 0) {
		start = 0;
	}
	if (!end || end < 0 || end > len) {
		end = len;
	}
	let out = "";
	for (let i = start; i < end; ++i) {
		out += hexSliceLookupTable[buf[i]];
	}
	return out;
}
function utf16leSlice(buf, start, end) {
	const bytes = buf.slice(start, end);
	let res = "";
	for (let i = 0; i < bytes.length - 1; i += 2) {
		res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);
	}
	return res;
}
Buffer.prototype.slice = function slice(start, end) {
	const len = this.length;
	start = Math.trunc(start);
	end = end === undefined ? len : Math.trunc(end);
	if (start < 0) {
		start += len;
		if (start < 0) {
			start = 0;
		}
	} else if (start > len) {
		start = len;
	}
	if (end < 0) {
		end += len;
		if (end < 0) {
			end = 0;
		}
	} else if (end > len) {
		end = len;
	}
	if (end < start) {
		end = start;
	}
	const newBuf = this.subarray(start, end);
	Object.setPrototypeOf(newBuf, Buffer.prototype);
	return newBuf;
};
function checkOffset(offset, ext, length) {
	if (offset % 1 !== 0 || offset < 0) {
		throw new RangeError("offset is not uint");
	}
	if (offset + ext > length) {
		throw new RangeError("Trying to access beyond buffer length");
	}
}
Buffer.prototype.readUintLE = Buffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {
	offset = offset >>> 0;
	byteLength = byteLength >>> 0;
	if (!noAssert) {
		checkOffset(offset, byteLength, this.length);
	}
	let val = this[offset];
	let mul = 1;
	let i = 0;
	while (++i < byteLength && (mul *= 256)) {
		val += this[offset + i] * mul;
	}
	return val;
};
Buffer.prototype.readUintBE = Buffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {
	offset = offset >>> 0;
	byteLength = byteLength >>> 0;
	if (!noAssert) {
		checkOffset(offset, byteLength, this.length);
	}
	let val = this[offset + --byteLength];
	let mul = 1;
	while (byteLength > 0 && (mul *= 256)) {
		val += this[offset + --byteLength] * mul;
	}
	return val;
};
Buffer.prototype.readUint8 = Buffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 1, this.length);
	}
	return this[offset];
};
Buffer.prototype.readUint16LE = Buffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 2, this.length);
	}
	return this[offset] | this[offset + 1] << 8;
};
Buffer.prototype.readUint16BE = Buffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 2, this.length);
	}
	return this[offset] << 8 | this[offset + 1];
};
Buffer.prototype.readUint32LE = Buffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 4, this.length);
	}
	return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 16777216;
};
Buffer.prototype.readUint32BE = Buffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 4, this.length);
	}
	return this[offset] * 16777216 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);
};
Buffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE(offset) {
	offset = offset >>> 0;
	validateNumber(offset, "offset");
	const first = this[offset];
	const last = this[offset + 7];
	if (first === undefined || last === undefined) {
		boundsError(offset, this.length - 8);
	}
	const lo = first + this[++offset] * 2 ** 8 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 24;
	const hi = this[++offset] + this[++offset] * 2 ** 8 + this[++offset] * 2 ** 16 + last * 2 ** 24;
	return BigInt(lo) + (BigInt(hi) << BigInt(32));
});
Buffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE(offset) {
	offset = offset >>> 0;
	validateNumber(offset, "offset");
	const first = this[offset];
	const last = this[offset + 7];
	if (first === undefined || last === undefined) {
		boundsError(offset, this.length - 8);
	}
	const hi = first * 2 ** 24 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + this[++offset];
	const lo = this[++offset] * 2 ** 24 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + last;
	return (BigInt(hi) << BigInt(32)) + BigInt(lo);
});
Buffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {
	offset = offset >>> 0;
	byteLength = byteLength >>> 0;
	if (!noAssert) {
		checkOffset(offset, byteLength, this.length);
	}
	let val = this[offset];
	let mul = 1;
	let i = 0;
	while (++i < byteLength && (mul *= 256)) {
		val += this[offset + i] * mul;
	}
	mul *= 128;
	if (val >= mul) {
		val -= Math.pow(2, 8 * byteLength);
	}
	return val;
};
Buffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {
	offset = offset >>> 0;
	byteLength = byteLength >>> 0;
	if (!noAssert) {
		checkOffset(offset, byteLength, this.length);
	}
	let i = byteLength;
	let mul = 1;
	let val = this[offset + --i];
	while (i > 0 && (mul *= 256)) {
		val += this[offset + --i] * mul;
	}
	mul *= 128;
	if (val >= mul) {
		val -= Math.pow(2, 8 * byteLength);
	}
	return val;
};
Buffer.prototype.readInt8 = function readInt8(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 1, this.length);
	}
	if (!(this[offset] & 128)) {
		return this[offset];
	}
	return (255 - this[offset] + 1) * -1;
};
Buffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 2, this.length);
	}
	const val = this[offset] | this[offset + 1] << 8;
	return val & 32768 ? val | 4294901760 : val;
};
Buffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 2, this.length);
	}
	const val = this[offset + 1] | this[offset] << 8;
	return val & 32768 ? val | 4294901760 : val;
};
Buffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 4, this.length);
	}
	return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;
};
Buffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 4, this.length);
	}
	return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];
};
Buffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE(offset) {
	offset = offset >>> 0;
	validateNumber(offset, "offset");
	const first = this[offset];
	const last = this[offset + 7];
	if (first === undefined || last === undefined) {
		boundsError(offset, this.length - 8);
	}
	const val = this[offset + 4] + this[offset + 5] * 2 ** 8 + this[offset + 6] * 2 ** 16 + (last << 24);
	return (BigInt(val) << BigInt(32)) + BigInt(first + this[++offset] * 2 ** 8 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 24);
});
Buffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE(offset) {
	offset = offset >>> 0;
	validateNumber(offset, "offset");
	const first = this[offset];
	const last = this[offset + 7];
	if (first === undefined || last === undefined) {
		boundsError(offset, this.length - 8);
	}
	const val = (first << 24) + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + this[++offset];
	return (BigInt(val) << BigInt(32)) + BigInt(this[++offset] * 2 ** 24 + this[++offset] * 2 ** 16 + this[++offset] * 2 ** 8 + last);
});
Buffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 4, this.length);
	}
	return ieee754.read(this, offset, true, 23, 4);
};
Buffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 4, this.length);
	}
	return ieee754.read(this, offset, false, 23, 4);
};
Buffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 8, this.length);
	}
	return ieee754.read(this, offset, true, 52, 8);
};
Buffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {
	offset = offset >>> 0;
	if (!noAssert) {
		checkOffset(offset, 8, this.length);
	}
	return ieee754.read(this, offset, false, 52, 8);
};
function checkInt(buf, value, offset, ext, max, min) {
	if (!Buffer.isBuffer(buf)) {
		throw new TypeError("\"buffer\" argument must be a Buffer instance");
	}
	if (value > max || value < min) {
		throw new RangeError("\"value\" argument is out of bounds");
	}
	if (offset + ext > buf.length) {
		throw new RangeError("Index out of range");
	}
}
Buffer.prototype.writeUintLE = Buffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {
	value = +value;
	offset = offset >>> 0;
	byteLength = byteLength >>> 0;
	if (!noAssert) {
		const maxBytes = Math.pow(2, 8 * byteLength) - 1;
		checkInt(this, value, offset, byteLength, maxBytes, 0);
	}
	let mul = 1;
	let i = 0;
	this[offset] = value & 255;
	while (++i < byteLength && (mul *= 256)) {
		this[offset + i] = value / mul & 255;
	}
	return offset + byteLength;
};
Buffer.prototype.writeUintBE = Buffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {
	value = +value;
	offset = offset >>> 0;
	byteLength = byteLength >>> 0;
	if (!noAssert) {
		const maxBytes = Math.pow(2, 8 * byteLength) - 1;
		checkInt(this, value, offset, byteLength, maxBytes, 0);
	}
	let i = byteLength - 1;
	let mul = 1;
	this[offset + i] = value & 255;
	while (--i >= 0 && (mul *= 256)) {
		this[offset + i] = value / mul & 255;
	}
	return offset + byteLength;
};
Buffer.prototype.writeUint8 = Buffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 1, 255, 0);
	}
	this[offset] = value & 255;
	return offset + 1;
};
Buffer.prototype.writeUint16LE = Buffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 2, 65535, 0);
	}
	this[offset] = value & 255;
	this[offset + 1] = value >>> 8;
	return offset + 2;
};
Buffer.prototype.writeUint16BE = Buffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 2, 65535, 0);
	}
	this[offset] = value >>> 8;
	this[offset + 1] = value & 255;
	return offset + 2;
};
Buffer.prototype.writeUint32LE = Buffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 4, 4294967295, 0);
	}
	this[offset + 3] = value >>> 24;
	this[offset + 2] = value >>> 16;
	this[offset + 1] = value >>> 8;
	this[offset] = value & 255;
	return offset + 4;
};
Buffer.prototype.writeUint32BE = Buffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 4, 4294967295, 0);
	}
	this[offset] = value >>> 24;
	this[offset + 1] = value >>> 16;
	this[offset + 2] = value >>> 8;
	this[offset + 3] = value & 255;
	return offset + 4;
};
function wrtBigUInt64LE(buf, value, offset, min, max) {
	checkIntBI(value, min, max, buf, offset, 7);
	let lo = Number(value & BigInt(4294967295));
	buf[offset++] = lo;
	lo = lo >> 8;
	buf[offset++] = lo;
	lo = lo >> 8;
	buf[offset++] = lo;
	lo = lo >> 8;
	buf[offset++] = lo;
	let hi = Number(value >> BigInt(32) & BigInt(4294967295));
	buf[offset++] = hi;
	hi = hi >> 8;
	buf[offset++] = hi;
	hi = hi >> 8;
	buf[offset++] = hi;
	hi = hi >> 8;
	buf[offset++] = hi;
	return offset;
}
function wrtBigUInt64BE(buf, value, offset, min, max) {
	checkIntBI(value, min, max, buf, offset, 7);
	let lo = Number(value & BigInt(4294967295));
	buf[offset + 7] = lo;
	lo = lo >> 8;
	buf[offset + 6] = lo;
	lo = lo >> 8;
	buf[offset + 5] = lo;
	lo = lo >> 8;
	buf[offset + 4] = lo;
	let hi = Number(value >> BigInt(32) & BigInt(4294967295));
	buf[offset + 3] = hi;
	hi = hi >> 8;
	buf[offset + 2] = hi;
	hi = hi >> 8;
	buf[offset + 1] = hi;
	hi = hi >> 8;
	buf[offset] = hi;
	return offset + 8;
}
Buffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE(value, offset = 0) {
	return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt("0xffffffffffffffff"));
});
Buffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE(value, offset = 0) {
	return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt("0xffffffffffffffff"));
});
Buffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		const limit = Math.pow(2, 8 * byteLength - 1);
		checkInt(this, value, offset, byteLength, limit - 1, -limit);
	}
	let i = 0;
	let mul = 1;
	let sub = 0;
	this[offset] = value & 255;
	while (++i < byteLength && (mul *= 256)) {
		if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
			sub = 1;
		}
		this[offset + i] = Math.trunc(value / mul) - sub & 255;
	}
	return offset + byteLength;
};
Buffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		const limit = Math.pow(2, 8 * byteLength - 1);
		checkInt(this, value, offset, byteLength, limit - 1, -limit);
	}
	let i = byteLength - 1;
	let mul = 1;
	let sub = 0;
	this[offset + i] = value & 255;
	while (--i >= 0 && (mul *= 256)) {
		if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
			sub = 1;
		}
		this[offset + i] = Math.trunc(value / mul) - sub & 255;
	}
	return offset + byteLength;
};
Buffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 1, 127, -128);
	}
	if (value < 0) {
		value = 255 + value + 1;
	}
	this[offset] = value & 255;
	return offset + 1;
};
Buffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 2, 32767, -32768);
	}
	this[offset] = value & 255;
	this[offset + 1] = value >>> 8;
	return offset + 2;
};
Buffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 2, 32767, -32768);
	}
	this[offset] = value >>> 8;
	this[offset + 1] = value & 255;
	return offset + 2;
};
Buffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 4, **********, -2147483648);
	}
	this[offset] = value & 255;
	this[offset + 1] = value >>> 8;
	this[offset + 2] = value >>> 16;
	this[offset + 3] = value >>> 24;
	return offset + 4;
};
Buffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkInt(this, value, offset, 4, **********, -2147483648);
	}
	if (value < 0) {
		value = 4294967295 + value + 1;
	}
	this[offset] = value >>> 24;
	this[offset + 1] = value >>> 16;
	this[offset + 2] = value >>> 8;
	this[offset + 3] = value & 255;
	return offset + 4;
};
Buffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE(value, offset = 0) {
	return wrtBigUInt64LE(this, value, offset, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
});
Buffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE(value, offset = 0) {
	return wrtBigUInt64BE(this, value, offset, -BigInt("0x8000000000000000"), BigInt("0x7fffffffffffffff"));
});
function checkIEEE754(buf, value, offset, ext, max, min) {
	if (offset + ext > buf.length) {
		throw new RangeError("Index out of range");
	}
	if (offset < 0) {
		throw new RangeError("Index out of range");
	}
}
function writeFloat(buf, value, offset, littleEndian, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkIEEE754(buf, value, offset, 4, 34028234663852886e22, -34028234663852886e22);
	}
	ieee754.write(buf, value, offset, littleEndian, 23, 4);
	return offset + 4;
}
Buffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {
	return writeFloat(this, value, offset, true, noAssert);
};
Buffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {
	return writeFloat(this, value, offset, false, noAssert);
};
function writeDouble(buf, value, offset, littleEndian, noAssert) {
	value = +value;
	offset = offset >>> 0;
	if (!noAssert) {
		checkIEEE754(buf, value, offset, 8, 17976931348623157e292, -17976931348623157e292);
	}
	ieee754.write(buf, value, offset, littleEndian, 52, 8);
	return offset + 8;
}
Buffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {
	return writeDouble(this, value, offset, true, noAssert);
};
Buffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {
	return writeDouble(this, value, offset, false, noAssert);
};
Buffer.prototype.copy = function copy(target, targetStart, start, end) {
	if (!Buffer.isBuffer(target)) {
		throw new TypeError("argument should be a Buffer");
	}
	if (!start) {
		start = 0;
	}
	if (!end && end !== 0) {
		end = this.length;
	}
	if (targetStart >= target.length) {
		targetStart = target.length;
	}
	if (!targetStart) {
		targetStart = 0;
	}
	if (end > 0 && end < start) {
		end = start;
	}
	if (end === start) {
		return 0;
	}
	if (target.length === 0 || this.length === 0) {
		return 0;
	}
	if (targetStart < 0) {
		throw new RangeError("targetStart out of bounds");
	}
	if (start < 0 || start >= this.length) {
		throw new RangeError("Index out of range");
	}
	if (end < 0) {
		throw new RangeError("sourceEnd out of bounds");
	}
	if (end > this.length) {
		end = this.length;
	}
	if (target.length - targetStart < end - start) {
		end = target.length - targetStart + start;
	}
	const len = end - start;
	if (this === target && typeof Uint8Array.prototype.copyWithin === "function") {
		this.copyWithin(targetStart, start, end);
	} else {
		Uint8Array.prototype.set.call(target, this.subarray(start, end), targetStart);
	}
	return len;
};
Buffer.prototype.fill = function fill(val, start, end, encoding) {
	if (typeof val === "string") {
		if (typeof start === "string") {
			encoding = start;
			start = 0;
			end = this.length;
		} else if (typeof end === "string") {
			encoding = end;
			end = this.length;
		}
		if (encoding !== undefined && typeof encoding !== "string") {
			throw new TypeError("encoding must be a string");
		}
		if (typeof encoding === "string" && !Buffer.isEncoding(encoding)) {
			throw new TypeError("Unknown encoding: " + encoding);
		}
		if (val.length === 1) {
			const code = val.charCodeAt(0);
			if (encoding === "utf8" && code < 128 || encoding === "latin1") {
				val = code;
			}
		}
	} else if (typeof val === "number") {
		val = val & 255;
	} else if (typeof val === "boolean") {
		val = Number(val);
	}
	if (start < 0 || this.length < start || this.length < end) {
		throw new RangeError("Out of range index");
	}
	if (end <= start) {
		return this;
	}
	start = start >>> 0;
	end = end === undefined ? this.length : end >>> 0;
	if (!val) {
		val = 0;
	}
	let i;
	if (typeof val === "number") {
		for (i = start; i < end; ++i) {
			this[i] = val;
		}
	} else {
		const bytes = Buffer.isBuffer(val) ? val : Buffer.from(val, encoding);
		const len = bytes.length;
		if (len === 0) {
			throw new TypeError("The value \"" + val + "\" is invalid for argument \"value\"");
		}
		for (i = 0; i < end - start; ++i) {
			this[i + start] = bytes[i % len];
		}
	}
	return this;
};
const errors = {};
function E(sym, getMessage, Base) {
	errors[sym] = class NodeError extends Base {
		constructor() {
			super();
			Object.defineProperty(this, "message", {
				value: Reflect.apply(getMessage, this, arguments),
				writable: true,
				configurable: true
			});
			this.name = `${this.name} [${sym}]`;
			this.stack;
			delete this.name;
		}
		get code() {
			return sym;
		}
		set code(value) {
			Object.defineProperty(this, "code", {
				configurable: true,
				enumerable: true,
				value,
				writable: true
			});
		}
		toString() {
			return `${this.name} [${sym}]: ${this.message}`;
		}
	};
}
E("ERR_BUFFER_OUT_OF_BOUNDS", function(name) {
	if (name) {
		return `${name} is outside of buffer bounds`;
	}
	return "Attempt to access memory outside buffer bounds";
}, RangeError);
E("ERR_INVALID_ARG_TYPE", function(name, actual) {
	return `The "${name}" argument must be of type number. Received type ${typeof actual}`;
}, TypeError);
E("ERR_OUT_OF_RANGE", function(str, range, input) {
	let msg = `The value of "${str}" is out of range.`;
	let received = input;
	if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {
		received = addNumericalSeparator(String(input));
	} else if (typeof input === "bigint") {
		received = String(input);
		if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {
			received = addNumericalSeparator(received);
		}
		received += "n";
	}
	msg += ` It must be ${range}. Received ${received}`;
	return msg;
}, RangeError);
function addNumericalSeparator(val) {
	let res = "";
	let i = val.length;
	const start = val[0] === "-" ? 1 : 0;
	for (; i >= start + 4; i -= 3) {
		res = `_${val.slice(i - 3, i)}${res}`;
	}
	return `${val.slice(0, i)}${res}`;
}
function checkBounds(buf, offset, byteLength) {
	validateNumber(offset, "offset");
	if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {
		boundsError(offset, buf.length - (byteLength + 1));
	}
}
function checkIntBI(value, min, max, buf, offset, byteLength) {
	if (value > max || value < min) {
		const n = typeof min === "bigint" ? "n" : "";
		let range;
		if (byteLength > 3) {
			range = min === 0 || min === BigInt(0) ? `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}` : `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` + `${(byteLength + 1) * 8 - 1}${n}`;
		} else {
			range = `>= ${min}${n} and <= ${max}${n}`;
		}
		throw new errors.ERR_OUT_OF_RANGE("value", range, value);
	}
	checkBounds(buf, offset, byteLength);
}
function validateNumber(value, name) {
	if (typeof value !== "number") {
		throw new errors.ERR_INVALID_ARG_TYPE(name, "number", value);
	}
}
function boundsError(value, length, type) {
	if (Math.floor(value) !== value) {
		validateNumber(value, type);
		throw new errors.ERR_OUT_OF_RANGE(type || "offset", "an integer", value);
	}
	if (length < 0) {
		throw new errors.ERR_BUFFER_OUT_OF_BOUNDS();
	}
	throw new errors.ERR_OUT_OF_RANGE(type || "offset", `>= ${type ? 1 : 0} and <= ${length}`, value);
}
const INVALID_BASE64_RE = /[^\w+/-]/g;
function base64clean(str) {
	str = str.split("=")[0];
	str = str.trim().replace(INVALID_BASE64_RE, "");
	if (str.length < 2) {
		return "";
	}
	while (str.length % 4 !== 0) {
		str = str + "=";
	}
	return str;
}
function utf8ToBytes(string, units) {
	units = units || Number.POSITIVE_INFINITY;
	let codePoint;
	const length = string.length;
	let leadSurrogate = null;
	const bytes = [];
	for (let i = 0; i < length; ++i) {
		codePoint = string.charCodeAt(i);
		if (codePoint > 55295 && codePoint < 57344) {
			if (!leadSurrogate) {
				if (codePoint > 56319) {
					if ((units -= 3) > -1) {
						bytes.push(239, 191, 189);
					}
					continue;
				} else if (i + 1 === length) {
					if ((units -= 3) > -1) {
						bytes.push(239, 191, 189);
					}
					continue;
				}
				leadSurrogate = codePoint;
				continue;
			}
			if (codePoint < 56320) {
				if ((units -= 3) > -1) {
					bytes.push(239, 191, 189);
				}
				leadSurrogate = codePoint;
				continue;
			}
			codePoint = (leadSurrogate - 55296 << 10 | codePoint - 56320) + 65536;
		} else if (leadSurrogate && (units -= 3) > -1) {
			bytes.push(239, 191, 189);
		}
		leadSurrogate = null;
		if (codePoint < 128) {
			if ((units -= 1) < 0) {
				break;
			}
			bytes.push(codePoint);
		} else if (codePoint < 2048) {
			if ((units -= 2) < 0) {
				break;
			}
			bytes.push(codePoint >> 6 | 192, codePoint & 63 | 128);
		} else if (codePoint < 65536) {
			if ((units -= 3) < 0) {
				break;
			}
			bytes.push(codePoint >> 12 | 224, codePoint >> 6 & 63 | 128, codePoint & 63 | 128);
		} else if (codePoint < 1114112) {
			if ((units -= 4) < 0) {
				break;
			}
			bytes.push(codePoint >> 18 | 240, codePoint >> 12 & 63 | 128, codePoint >> 6 & 63 | 128, codePoint & 63 | 128);
		} else {
			throw new Error("Invalid code point");
		}
	}
	return bytes;
}
function asciiToBytes(str) {
	const byteArray = [];
	for (let i = 0; i < str.length; ++i) {
		byteArray.push(str.charCodeAt(i) & 255);
	}
	return byteArray;
}
function utf16leToBytes(str, units) {
	let c, hi, lo;
	const byteArray = [];
	for (let i = 0; i < str.length; ++i) {
		if ((units -= 2) < 0) {
			break;
		}
		c = str.charCodeAt(i);
		hi = c >> 8;
		lo = c % 256;
		byteArray.push(lo, hi);
	}
	return byteArray;
}
function base64ToBytes(str) {
	return base64.toByteArray(base64clean(str));
}
function blitBuffer(src, dst, offset, length) {
	let i;
	for (i = 0; i < length; ++i) {
		if (i + offset >= dst.length || i >= src.length) {
			break;
		}
		dst[i + offset] = src[i];
	}
	return i;
}
function isInstance(obj, type) {
	return obj instanceof type || obj != null && obj.constructor != null && obj.constructor.name != null && obj.constructor.name === type.name;
}
function numberIsNaN(obj) {
	return obj !== obj;
}
const hexSliceLookupTable = function() {
	const alphabet = "0123456789abcdef";
	const table = Array.from({ length: 256 });
	for (let i = 0; i < 16; ++i) {
		const i16 = i * 16;
		for (let j = 0; j < 16; ++j) {
			table[i16 + j] = alphabet[i] + alphabet[j];
		}
	}
	return table;
}();
function defineBigIntMethod(fn) {
	return typeof BigInt === "undefined" ? BufferBigIntNotDefined : fn;
}
function BufferBigIntNotDefined() {
	throw new Error("BigInt not supported");
}
