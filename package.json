{"name": "kidzplayai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy": "npm run build && git add . && git commit -m \"Deploy update\" && git push origin main", "deploy-quick": "git add . && git commit -m \"Quick update\" && git push origin main", "deploy-force": "npm run build && git add . && git commit -m \"Force deploy\" --allow-empty && git push origin main", "check-build": "npm run build && echo 'Build successful! Ready to deploy.'", "status": "git status && echo '--- Build Info ---' && ls -la dist/ 2>/dev/null || echo 'No dist folder found. Run npm run build first.'"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"@fal-ai/serverless-client": "^0.13.0", "konva": "^9.3.20", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "terser": "^5.43.1", "vite": "^4.4.5"}}