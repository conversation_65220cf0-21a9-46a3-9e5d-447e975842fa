import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import LandingPage from './pages/LandingPage'
import Dashboard from './pages/Dashboard'
import MemoryCardGame from './pages/games/MemoryCardGame'
import PuzzleGame from './pages/games/PuzzleGame'
import SlidingPuzzleGame from './pages/games/SlidingPuzzleGame'
import WelcomeModal from './components/WelcomeModal'
import CardPackModal from './components/CardPackModal'
import AudioManager from './components/AudioManager'
import { LanguageProvider } from './contexts/LanguageContext'
import { getChildName, hasOpenedStarterPack } from './utils/localStorage'

function App() {
  const [childName, setChildName] = useState('')
  const [showWelcomeModal, setShowWelcomeModal] = useState(false)
  const [showCardPackModal, setShowCardPackModal] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const savedChildName = getChildName()
    if (savedChildName) {
      setChildName(savedChildName)
      setIsAuthenticated(true)
      // Check if user has opened starter pack
      if (!hasOpenedStarterPack(savedChildName)) {
        setShowCardPackModal(true)
      }
    }


  }, [])

  const handleStartAdventure = () => {
    const savedChildName = getChildName()
    if (savedChildName) {
      setChildName(savedChildName)
      setIsAuthenticated(true)
      if (!hasOpenedStarterPack(savedChildName)) {
        setShowCardPackModal(true)
      }
    } else {
      setShowWelcomeModal(true)
    }
  }

  const handleWelcomeComplete = (name) => {
    setChildName(name)
    setShowWelcomeModal(false)
    setShowCardPackModal(true)
    setIsAuthenticated(true)
  }

  const handleCardPackComplete = () => {
    setShowCardPackModal(false)
  }

  return (
    <Router>
      <LanguageProvider>
        <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500">
          <Routes>
            <Route
              path="/"
              element={
                isAuthenticated ?
                <Navigate to="/dashboard" replace /> :
                <LandingPage onStartAdventure={handleStartAdventure} />
              }
            />
            <Route
              path="/dashboard"
              element={
                isAuthenticated ?
                <Dashboard childName={childName} /> :
                <Navigate to="/" replace />
              }
            />
            <Route
              path="/game/memory"
              element={
                isAuthenticated ?
                <MemoryCardGame childName={childName} /> :
                <Navigate to="/" replace />
              }
            />
            <Route
              path="/game/puzzle"
              element={
                isAuthenticated ?
                <PuzzleGame childName={childName} /> :
                <Navigate to="/" replace />
              }
            />
            <Route
              path="/game/sliding"
              element={
                isAuthenticated ?
                <SlidingPuzzleGame childName={childName} /> :
                <Navigate to="/" replace />
              }
            />
          </Routes>

          {showWelcomeModal && (
            <WelcomeModal
              onComplete={handleWelcomeComplete}
              onClose={() => setShowWelcomeModal(false)}
            />
          )}

          {showCardPackModal && (
            <CardPackModal
              childName={childName}
              onComplete={handleCardPackComplete}
              onClose={() => setShowCardPackModal(false)}
            />
          )}

          {/* Audio Manager */}
          <AudioManager />
        </div>
      </LanguageProvider>
    </Router>
  )
}

export default App
