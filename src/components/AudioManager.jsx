import { useEffect, useState } from 'react'
import { Volume2, VolumeX } from 'lucide-react'

const AudioManager = () => {
  const [isMuted, setIsMuted] = useState(false)
  const [bgMusic, setBgMusic] = useState(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasInteracted, setHasInteracted] = useState(false)

  useEffect(() => {
    // Initialize audio
    const initAudio = () => {
      try {
        const audio = new Audio('/music/music-bgm.mp3')
        audio.loop = true
        audio.volume = 0.3
        audio.preload = 'auto'
        
        audio.addEventListener('canplaythrough', () => {
          console.log('Background music loaded successfully')
          setIsLoaded(true)
        })
        
        audio.addEventListener('error', (e) => {
          console.log('Background music failed to load:', e)
          setIsLoaded(false)
        })
        
        setBgMusic(audio)
      } catch (error) {
        console.log('Error initializing audio:', error)
      }
    }

    initAudio()

    // Listen for first user interaction
    const handleFirstInteraction = () => {
      setHasInteracted(true)
      document.removeEventListener('click', handleFirstInteraction)
      document.removeEventListener('touchstart', handleFirstInteraction)
      document.removeEventListener('keydown', handleFirstInteraction)
    }

    document.addEventListener('click', handleFirstInteraction)
    document.addEventListener('touchstart', handleFirstInteraction)
    document.addEventListener('keydown', handleFirstInteraction)

    return () => {
      document.removeEventListener('click', handleFirstInteraction)
      document.removeEventListener('touchstart', handleFirstInteraction)
      document.removeEventListener('keydown', handleFirstInteraction)
      if (bgMusic) {
        bgMusic.pause()
        bgMusic.src = ''
      }
    }
  }, [])

  // Auto-play when conditions are met
  useEffect(() => {
    if (bgMusic && isLoaded && hasInteracted && !isMuted) {
      const playAudio = async () => {
        try {
          await bgMusic.play()
          console.log('Background music started')
        } catch (error) {
          console.log('Failed to play background music:', error)
        }
      }
      playAudio()
    }
  }, [bgMusic, isLoaded, hasInteracted, isMuted])

  const toggleMute = () => {
    if (bgMusic) {
      if (isMuted) {
        bgMusic.play().catch(console.log)
      } else {
        bgMusic.pause()
      }
    }
    setIsMuted(!isMuted)
  }

  return (
    <button
      onClick={toggleMute}
      className="fixed top-4 right-4 z-50 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
      title={isMuted ? 'Unmute' : 'Mute'}
    >
      {isMuted ? <VolumeX size={24} /> : <Volume2 size={24} />}
    </button>
  )
}

export default AudioManager
