import { useState } from 'react'
import { initializeStarterPack, getStarterImages } from '../utils/localStorage'
import { playClickSound, playPackOpenSound, playCardFlipSound, playAchievementSound } from '../utils/audio'
import RobustImage from './RobustImage'

const CardPackModal = ({ childName, onComplete, onClose }) => {
  const [stage, setStage] = useState('unopened') // unopened, opening, revealing, complete
  const [revealedCards, setRevealedCards] = useState([])
  const starterImages = getStarterImages()

  const handleOpenPack = () => {
    playPackOpenSound()
    setStage('opening')

    // Simulate pack opening animation
    setTimeout(() => {
      setStage('revealing')
      revealCards()
    }, 2000)
  }

  const revealCards = () => {
    // Reveal cards one by one with animation
    starterImages.forEach((image, index) => {
      setTimeout(() => {
        playCardFlipSound()
        setRevealedCards(prev => [...prev, image])
      }, index * 500)
    })

    // After all cards are revealed, show complete stage
    setTimeout(() => {
      playAchievementSound()
      setStage('complete')
      // Initialize starter pack in localStorage
      initializeStarterPack(childName)
    }, starterImages.length * 500 + 1000)
  }

  const handleComplete = () => {
    playClickSound()
    onComplete()
  }

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-8 max-w-4xl w-full shadow-2xl">

        {/* Unopened Pack */}
        {stage === 'unopened' && (
          <div className="text-center">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              🎁 {childName ? `Welcome ${childName}!` : 'Welcome!'} 🎁
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Here's your FREE starter pack with 6 amazing pictures!
            </p>

            <div className="mb-8">
              <div className="w-48 h-64 mx-auto bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 cursor-pointer flex flex-col items-center justify-center text-white"
                   onClick={handleOpenPack}>
                <div className="text-6xl mb-4">📦</div>
                <div className="text-2xl font-bold mb-2">MYSTERY</div>
                <div className="text-xl font-bold mb-4">PACK</div>
                <div className="bg-white/20 rounded-full px-4 py-2 text-sm font-bold">
                  CLICK ME!
                </div>
              </div>
            </div>

            <p className="text-lg text-gray-500">
              ✨ 6 Amazing Pictures Inside! ✨
            </p>
          </div>
        )}

        {/* Opening Animation */}
        {stage === 'opening' && (
          <div className="text-center">
            <h2 className="text-4xl font-bold text-gray-800 mb-8">
              🎉 Opening Your Pack! 🎉
            </h2>

            <div className="mb-8">
              <div className="w-48 h-64 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-2xl animate-pulse flex flex-col items-center justify-center text-white">
                <div className="text-6xl mb-4 animate-bounce">✨</div>
                <div className="text-xl font-bold">OPENING...</div>
              </div>
            </div>

            <div className="flex justify-center gap-2">
              <div className="w-3 h-3 bg-primary-500 rounded-full animate-bounce"></div>
              <div className="w-3 h-3 bg-primary-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-3 h-3 bg-primary-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        )}

        {/* Revealing Cards */}
        {stage === 'revealing' && (
          <div className="text-center">
            <h2 className="text-4xl font-bold text-gray-800 mb-8">
              🌟 Your New Pictures! 🌟
            </h2>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
              {starterImages.map((image, index) => (
                <div key={image.id} className="relative">
                  {revealedCards.includes(image) ? (
                    <div className="bg-white rounded-2xl shadow-lg p-4 transform animate-bounce-slow">
                      <RobustImage
                        src={image.url}
                        dataUrl={image.dataUrl}
                        fallback={image.fallback}
                        alt={image.name}
                        className="w-full h-32 object-cover rounded-xl mb-2"
                      />
                      <p className="text-sm font-bold text-gray-700">{image.name}</p>
                    </div>
                  ) : (
                    <div className="bg-gray-200 rounded-2xl shadow-lg p-4 h-44 flex items-center justify-center">
                      <div className="text-4xl text-gray-400">?</div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <p className="text-lg text-gray-600">
              Revealing your amazing pictures...
            </p>
          </div>
        )}

        {/* Complete */}
        {stage === 'complete' && (
          <div className="text-center">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              🎉 AWESOME! 🎉
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              ⭐ These pictures are now yours! ⭐<br/>
              You can use them in all your games!
            </p>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
              {starterImages.map((image) => (
                <div key={image.id} className="bg-white rounded-2xl shadow-lg p-4 transform hover:scale-105 transition-all duration-200">
                  <RobustImage
                    src={image.url}
                    dataUrl={image.dataUrl}
                    fallback={image.fallback}
                    alt={image.name}
                    className="w-full h-32 object-cover rounded-xl mb-2"
                  />
                  <p className="text-sm font-bold text-gray-700">{image.name}</p>
                </div>
              ))}
            </div>

            <button
              onClick={handleComplete}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold text-2xl py-4 px-8 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
            >
              🎮 AWESOME! LET'S PLAY! 🎮
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default CardPackModal
