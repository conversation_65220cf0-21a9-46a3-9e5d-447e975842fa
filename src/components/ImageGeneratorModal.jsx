import { useState, useEffect } from 'react'
import { Wand2, <PERSON><PERSON><PERSON>, X, Plus, Palette } from 'lucide-react'
import { 
  getImageCollection, 
  addGeneratedImage, 
  createNewTheme,
  addImageToTheme 
} from '../utils/localStorage'
import { playClickSound, playSuccessSound } from '../utils/audio'
import { useLanguage } from '../contexts/LanguageContext'

const ImageGeneratorModal = ({ 
  childName, 
  onClose, 
  onImageGenerated,
  selectedThemeId = null,
  selectedThemeName = null 
}) => {
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedImage, setGeneratedImage] = useState(null)
  const [selectedTheme, setSelectedTheme] = useState(selectedThemeId || 'generated')
  const [newThemeName, setNewThemeName] = useState('')
  const [showCreateTheme, setShowCreateTheme] = useState(false)
  const [collection, setCollection] = useState({ themes: [] })
  const { t } = useLanguage()

  useEffect(() => {
    if (childName) {
      setCollection(getImageCollection(childName))
    }
    
    // If a theme was pre-selected, set the prompt based on theme name
    if (selectedThemeName && selectedThemeName !== 'My Creations') {
      setPrompt(`${selectedThemeName} themed picture for kids`)
    }
  }, [childName, selectedThemeName])

  const generateImage = async () => {
    if (!prompt.trim()) return

    setIsGenerating(true)
    playClickSound()

    try {
      // Simulate API call to fal.ai
      // In real implementation, you would call the actual API
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: `${prompt}, child-friendly, colorful, cartoon style, safe for kids aged 3-9`,
          model: 'fal-ai/minimax/image-01'
        })
      })

      if (response.ok) {
        const data = await response.json()
        setGeneratedImage({
          dataUrl: data.imageUrl,
          prompt: prompt
        })
        playSuccessSound()
      } else {
        // Fallback for demo - use a placeholder
        setTimeout(() => {
          setGeneratedImage({
            dataUrl: `https://picsum.photos/512/512?random=${Date.now()}`,
            prompt: prompt
          })
          playSuccessSound()
        }, 2000)
      }
    } catch (error) {
      console.error('Error generating image:', error)
      // Fallback for demo
      setTimeout(() => {
        setGeneratedImage({
          dataUrl: `https://picsum.photos/512/512?random=${Date.now()}`,
          prompt: prompt
        })
        playSuccessSound()
      }, 2000)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleCreateNewTheme = () => {
    if (newThemeName.trim()) {
      playClickSound()
      const themeId = createNewTheme(childName, newThemeName.trim(), `Pictures about ${newThemeName}`, prompt)
      setCollection(getImageCollection(childName))
      setSelectedTheme(themeId)
      setNewThemeName('')
      setShowCreateTheme(false)
    }
  }

  const saveImage = () => {
    if (!generatedImage) return

    playClickSound()
    
    const imageData = {
      name: `${prompt.slice(0, 30)}...`,
      dataUrl: generatedImage.dataUrl,
      prompt: generatedImage.prompt
    }

    if (selectedTheme === 'new') {
      // This shouldn't happen as we handle new theme creation separately
      return
    }

    addGeneratedImage(childName, imageData, selectedTheme)
    onImageGenerated && onImageGenerated()
    onClose()
  }

  const availableThemes = collection.themes || []

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-8 max-w-2xl w-full shadow-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
              <Wand2 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-800">{t('generateNewImage')}</h2>
              <p className="text-gray-600">{t('createAmazingPictures')}</p>
            </div>
          </div>
          <button
            onClick={() => {
              playClickSound()
              onClose()
            }}
            className="p-2 rounded-xl hover:bg-gray-100 transition-colors"
          >
            <X className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Theme Selection */}
        <div className="mb-6">
          <label className="block text-sm font-bold text-gray-700 mb-3">
            {t('selectTheme')}
          </label>
          <div className="grid grid-cols-2 gap-3">
            {availableThemes.map((theme) => (
              <button
                key={theme.id}
                onClick={() => {
                  playClickSound()
                  setSelectedTheme(theme.id)
                }}
                className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                  selectedTheme === theme.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${theme.color}`}></div>
                  <div>
                    <div className="font-bold text-gray-800">{theme.name}</div>
                    <div className="text-xs text-gray-500">{theme.images.length} {t('pictures')}</div>
                  </div>
                </div>
              </button>
            ))}
            
            {/* Create New Theme Button */}
            <button
              onClick={() => {
                playClickSound()
                setShowCreateTheme(true)
              }}
              className="p-4 rounded-xl border-2 border-dashed border-gray-300 hover:border-blue-500 transition-all duration-200 flex items-center justify-center gap-2 text-gray-600 hover:text-blue-600"
            >
              <Plus className="w-5 h-5" />
              <span className="font-bold">{t('createNewTheme')}</span>
            </button>
          </div>
        </div>

        {/* Create New Theme Modal */}
        {showCreateTheme && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-xl">
              <h3 className="text-xl font-bold text-gray-800 mb-4">{t('createNewTheme')}</h3>
              <input
                type="text"
                value={newThemeName}
                onChange={(e) => setNewThemeName(e.target.value)}
                placeholder={t('enterThemeName')}
                className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none mb-4"
                maxLength={30}
              />
              <div className="flex gap-3">
                <button
                  onClick={() => {
                    playClickSound()
                    setShowCreateTheme(false)
                    setNewThemeName('')
                  }}
                  className="flex-1 px-4 py-2 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('cancel')}
                </button>
                <button
                  onClick={handleCreateNewTheme}
                  disabled={!newThemeName.trim()}
                  className="flex-1 px-4 py-2 rounded-xl bg-blue-500 text-white font-bold hover:bg-blue-600 transition-colors disabled:opacity-50"
                >
                  {t('create')}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Prompt Input */}
        <div className="mb-6">
          <label className="block text-sm font-bold text-gray-700 mb-3">
            {t('describeYourPicture')}
          </label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder={t('promptPlaceholder')}
            className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none"
            rows={4}
            maxLength={200}
          />
          <div className="text-right text-xs text-gray-500 mt-1">
            {prompt.length}/200
          </div>
        </div>

        {/* Generate Button */}
        <button
          onClick={generateImage}
          disabled={!prompt.trim() || isGenerating}
          className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-4 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 mb-6"
        >
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              {t('generating')}...
            </>
          ) : (
            <>
              <Sparkles className="w-6 h-6" />
              {t('generateImage')}
            </>
          )}
        </button>

        {/* Generated Image */}
        {generatedImage && (
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-2xl p-6 text-center">
              <img
                src={generatedImage.dataUrl}
                alt="Generated"
                className="w-full max-w-md mx-auto rounded-xl shadow-lg"
              />
              <p className="text-sm text-gray-600 mt-3 italic">"{generatedImage.prompt}"</p>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={() => {
                  playClickSound()
                  setGeneratedImage(null)
                }}
                className="flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors"
              >
                {t('generateAnother')}
              </button>
              <button
                onClick={saveImage}
                className="flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold hover:shadow-lg transition-all duration-200"
              >
                {t('saveToCollection')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ImageGeneratorModal
