import { Globe } from 'lucide-react'
import { useLanguage } from '../contexts/LanguageContext'
import { playClickSound } from '../utils/audio'

const LanguageToggle = () => {
  const { language, changeLanguage, t } = useLanguage()

  const handleLanguageChange = (newLanguage) => {
    playClickSound()
    changeLanguage(newLanguage)
  }

  return (
    <div className="relative group">
      <button className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 text-white hover:bg-white/30 transition-colors">
        <Globe className="w-4 h-4" />
        <span className="text-sm font-medium">
          {language === 'id' ? 'ID' : 'EN'}
        </span>
      </button>
      
      {/* Dropdown */}
      <div className="absolute right-0 top-full mt-2 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50">
        <button
          onClick={() => handleLanguageChange('id')}
          className={`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${
            language === 'id' ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
          }`}
        >
          <span className="text-lg">🇮🇩</span>
          <span className="font-medium">{t('indonesian')}</span>
          {language === 'id' && <span className="ml-auto text-blue-600">✓</span>}
        </button>
        <button
          onClick={() => handleLanguageChange('en')}
          className={`w-full px-4 py-3 text-left hover:bg-white/50 transition-colors flex items-center gap-3 ${
            language === 'en' ? 'bg-blue-100 text-blue-800' : 'text-gray-700'
          }`}
        >
          <span className="text-lg">🇺🇸</span>
          <span className="font-medium">{t('english')}</span>
          {language === 'en' && <span className="ml-auto text-blue-600">✓</span>}
        </button>
      </div>
    </div>
  )
}

export default LanguageToggle
