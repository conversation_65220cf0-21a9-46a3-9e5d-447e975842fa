import { useState, useEffect } from 'react'

const RobustImage = ({ 
  src, 
  fallback, 
  dataUrl, 
  alt, 
  className, 
  onLoad, 
  onError,
  ...props 
}) => {
  const [currentSrc, setCurrentSrc] = useState(src)
  const [hasError, setHasError] = useState(false)
  const [attempts, setAttempts] = useState(0)

  // Reset when src changes
  useEffect(() => {
    setCurrentSrc(src)
    setHasError(false)
    setAttempts(0)
  }, [src])

  const handleError = (e) => {
    setHasError(true)
    setAttempts(prev => prev + 1)

    // Try different fallbacks in order
    if (attempts === 0 && dataUrl && currentSrc !== dataUrl) {
      setCurrentSrc(dataUrl)
    } else if (attempts === 1 && fallback && currentSrc !== fallback) {
      setCurrentSrc(fallback)
    } else if (attempts === 2 && !currentSrc.includes('picsum')) {
      // Last resort - use picsum with random seed
      const randomSeed = Math.floor(Math.random() * 1000)
      setCurrentSrc(`https://picsum.photos/400/400?random=${randomSeed}`)
    }

    // Call original onError if provided
    if (onError) {
      onError(e)
    }
  }

  const handleLoad = (e) => {
    setHasError(false)
    if (onLoad) {
      onLoad(e)
    }
  }

  return (
    <img
      src={currentSrc}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
      {...props}
    />
  )
}

export default RobustImage
