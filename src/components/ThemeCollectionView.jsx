import { useState, useEffect } from 'react'
import { Edit3, Plus, Trash2, Image, Palette } from 'lucide-react'
import { 
  getImageCollection, 
  createNewTheme, 
  updateThemeName, 
  updateThemeDescription,
  deleteTheme,
  addImageToTheme 
} from '../utils/localStorage'
import { playClickSound } from '../utils/audio'
import { useLanguage } from '../contexts/LanguageContext'

const ThemeCollectionView = ({ childName, onGenerateImage, onImageSelect }) => {
  const [collection, setCollection] = useState({ themes: [] })
  const [editingTheme, setEditingTheme] = useState(null)
  const [newThemeName, setNewThemeName] = useState('')
  const [newThemeDescription, setNewThemeDescription] = useState('')
  const [showCreateTheme, setShowCreateTheme] = useState(false)
  const { t } = useLanguage()

  useEffect(() => {
    if (childName) {
      setCollection(getImageCollection(childName))
    }
  }, [childName])

  const handleCreateTheme = () => {
    if (newThemeName.trim()) {
      playClickSound()
      const themeId = createNewTheme(childName, newThemeName.trim(), newThemeDescription.trim())
      setCollection(getImageCollection(childName))
      setNewThemeName('')
      setNewThemeDescription('')
      setShowCreateTheme(false)
    }
  }

  const handleEditTheme = (themeId, newName) => {
    if (newName.trim()) {
      playClickSound()
      updateThemeName(childName, themeId, newName.trim())
      setCollection(getImageCollection(childName))
      setEditingTheme(null)
    }
  }

  const handleDeleteTheme = (themeId) => {
    playClickSound()
    deleteTheme(childName, themeId)
    setCollection(getImageCollection(childName))
  }

  const handleGenerateForTheme = (themeId, themeName) => {
    playClickSound()
    onGenerateImage && onGenerateImage(themeId, themeName)
  }

  const totalImages = collection.themes.reduce((total, theme) => total + theme.images.length, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-white flex items-center gap-2">
            <Palette className="w-8 h-8" />
            {t('myThemeCollections')} ({totalImages})
          </h2>
          <p className="text-white/80 mt-1">{t('organizeYourPictures')}</p>
        </div>
        
        <button
          onClick={() => {
            playClickSound()
            setShowCreateTheme(true)
          }}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-6 py-3 rounded-2xl font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          {t('createNewTheme')}
        </button>
      </div>

      {/* Create New Theme Modal */}
      {showCreateTheme && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl">
            <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
              {t('createNewTheme')}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-bold text-gray-700 mb-2">
                  {t('themeName')}
                </label>
                <input
                  type="text"
                  value={newThemeName}
                  onChange={(e) => setNewThemeName(e.target.value)}
                  placeholder={t('enterThemeName')}
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none"
                  maxLength={30}
                />
              </div>
              
              <div>
                <label className="block text-sm font-bold text-gray-700 mb-2">
                  {t('themeDescription')} ({t('optional')})
                </label>
                <textarea
                  value={newThemeDescription}
                  onChange={(e) => setNewThemeDescription(e.target.value)}
                  placeholder={t('enterThemeDescription')}
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none"
                  rows={3}
                  maxLength={100}
                />
              </div>
            </div>
            
            <div className="flex gap-3 mt-6">
              <button
                onClick={() => {
                  playClickSound()
                  setShowCreateTheme(false)
                  setNewThemeName('')
                  setNewThemeDescription('')
                }}
                className="flex-1 px-6 py-3 rounded-xl border-2 border-gray-300 text-gray-700 font-bold hover:bg-gray-50 transition-colors"
              >
                {t('cancel')}
              </button>
              <button
                onClick={handleCreateTheme}
                disabled={!newThemeName.trim()}
                className="flex-1 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {t('create')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Theme Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {collection.themes.map((theme) => (
          <div
            key={theme.id}
            className={`bg-gradient-to-br ${theme.color} rounded-3xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 overflow-hidden`}
          >
            {/* Theme Header */}
            <div className="p-6 text-white">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  {editingTheme === theme.id ? (
                    <input
                      type="text"
                      defaultValue={theme.name}
                      onBlur={(e) => handleEditTheme(theme.id, e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          handleEditTheme(theme.id, e.target.value)
                        }
                      }}
                      className="bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 rounded-lg px-3 py-1 text-xl font-bold focus:outline-none focus:ring-2 focus:ring-white/50"
                      autoFocus
                    />
                  ) : (
                    <h3 className="text-xl font-bold truncate">{theme.name}</h3>
                  )}
                  <p className="text-white/80 text-sm mt-1 line-clamp-2">{theme.description}</p>
                </div>
                
                <div className="flex gap-2 ml-3">
                  {!theme.isDefault && (
                    <>
                      <button
                        onClick={() => {
                          playClickSound()
                          setEditingTheme(editingTheme === theme.id ? null : theme.id)
                        }}
                        className="p-2 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteTheme(theme.id)}
                        className="p-2 rounded-lg bg-white/20 hover:bg-red-500/50 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white/90 text-sm font-medium">
                  {theme.images.length} {t('pictures')}
                </span>
                <button
                  onClick={() => handleGenerateForTheme(theme.id, theme.name)}
                  className="bg-white/20 hover:bg-white/30 backdrop-blur-sm px-3 py-1 rounded-lg text-sm font-bold transition-colors flex items-center gap-1"
                >
                  <Plus className="w-4 h-4" />
                  {t('generate')}
                </button>
              </div>
            </div>

            {/* Theme Images Preview */}
            <div className="bg-white/10 backdrop-blur-sm p-4">
              {theme.images.length > 0 ? (
                <div className="grid grid-cols-3 gap-2">
                  {theme.images.slice(0, 6).map((image, index) => (
                    <div
                      key={image.id}
                      className="aspect-square rounded-lg overflow-hidden cursor-pointer hover:scale-105 transition-transform"
                      onClick={() => {
                        playClickSound()
                        onImageSelect && onImageSelect(image)
                      }}
                    >
                      <img
                        src={image.url || image.dataUrl}
                        alt={image.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                  {theme.images.length > 6 && (
                    <div className="aspect-square rounded-lg bg-white/20 flex items-center justify-center text-white font-bold text-sm">
                      +{theme.images.length - 6}
                    </div>
                  )}
                </div>
              ) : (
                <div className="aspect-square rounded-lg border-2 border-dashed border-white/30 flex flex-col items-center justify-center text-white/60">
                  <Image className="w-8 h-8 mb-2" />
                  <span className="text-sm font-medium">{t('noImagesYet')}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default ThemeCollectionView
