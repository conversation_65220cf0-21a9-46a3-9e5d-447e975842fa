import { createContext, useContext, useState, useEffect } from 'react'

const LanguageContext = createContext()

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

const translations = {
  id: {
    // Header
    appTitle: 'KidzPlay AI',
    welcomeBack: 'Selamat datang kembali, {name}! 🌟',
    searchGames: 'Cari permainan...',

    // Picture Collection
    myPictureCollection: '📸 KOLEKSI GAMBARKU ({count})',
    myThemeCollections: '🎨 KOLEKSI TEMA GAMBARKU',
    organizeYourPictures: 'Atur gambar-gambarmu berdasarkan tema',
    generateNewImage: 'BUAT GAMBAR BARU',
    viewByThemes: 'Lihat berdasarkan tema',
    seeAll: 'Lihat semua →',

    // Theme Management
    createNewTheme: 'Buat Tema Baru',
    themeName: '<PERSON><PERSON> Tema',
    themeDescription: '<PERSON><PERSON><PERSON><PERSON> Tema',
    enterThemeName: 'Masukkan nama tema...',
    enterThemeDescription: 'Ceritakan tentang tema ini...',
    optional: 'opsional',
    cancel: 'Batal',
    create: 'Buat',
    pictures: 'gambar',
    generate: 'Buat',
    noImagesYet: 'Belum ada gambar',
    selectTheme: 'Pilih Tema',

    // Image Generation
    createAmazingPictures: 'Buat gambar menakjub dengan AI',
    describeYourPicture: 'Ceritakan gambar yang kamu inginkan',
    promptPlaceholder: 'Contoh: Seekor kucing lucu bermain di taman yang penuh bunga warna-warni...',
    generating: 'Sedang membuat',
    generateImage: 'Buat Gambar',
    generateAnother: 'Buat Lagi',
    saveToCollection: 'Simpan ke Koleksi',

    // Game Common
    backToDashboard: 'Kembali ke Dashboard',
    backToMenu: 'Kembali ke Menu',
    chooseDifficulty: 'Pilih Tingkat Kesulitan',
    easy: 'Mudah',
    medium: 'Sedang',
    hard: 'Sulit',
    level: 'Level',
    bestScore: 'Skor Terbaik',
    gamesPlayed: 'Permainan Dimainkan',
    startGame: 'Mulai Permainan',
    playAgain: 'Main Lagi',
    restart: 'Mulai Ulang',
    moves: 'Langkah',
    time: 'Waktu',
    score: 'Skor',
    finalScore: 'Skor Akhir',
    congratulations: 'Selamat!',

    // Memory Card Game
    needMoreImages: 'Butuh lebih banyak gambar',
    pairs: 'pasang',
    memoryGameCompleted: 'Kamu berhasil menyelesaikan permainan memori!',

    // Puzzle Game
    puzzlesCompleted: 'Puzzle Selesai',
    bestTime: 'Waktu Terbaik',
    availableImages: 'Gambar Tersedia',
    pieces: 'potongan',
    chooseImage: 'Pilih Gambar',
    noImagesAvailable: 'Tidak ada gambar tersedia',
    generateSomeImages: 'Buat beberapa gambar terlebih dahulu!',
    puzzleBoard: 'Papan Puzzle',
    puzzlePieces: 'Potongan Puzzle',
    workspace: 'Area Kerja',
    shuffle: 'Acak',
    puzzleCompleted: 'Puzzle Selesai!',
    greatJob: 'Kerja bagus! Kamu berhasil menyelesaikan puzzle!',
    completionTime: 'Waktu Selesai',
    selectImageForPuzzle: 'Pilih gambar untuk puzzle',
    selectYourImage: 'Pilih Gambarmu',

    // Sliding Puzzle Game
    puzzlesSolved: 'Puzzle Terpecahkan',
    lastDifficulty: 'Kesulitan Terakhir',
    grid: 'kotak',
    howToPlay: 'Cara Bermain',
    slidingRule1: 'Klik angka yang bersebelahan dengan kotak kosong untuk memindahkannya',
    slidingRule2: 'Susun angka dari 1 hingga terakhir secara berurutan',
    slidingRule3: 'Kotak kosong harus berada di pojok kanan bawah',
    puzzleSolved: 'Puzzle Terpecahkan!',
    excellentWork: 'Kerja yang luar biasa! Kamu berhasil menyelesaikan sliding puzzle!',

    // Games Section
    startPlaying: '🎮 MULAI BERMAIN',
    memoryCardGame: 'Permainan Kartu Memori',
    memoryCardDesc: 'Cocokkan pasangan kartu',
    puzzleGame: 'Permainan Puzzle',
    puzzleDesc: 'Seret & lepas potongan puzzle',
    slidingPuzzle: 'Puzzle Geser',
    slidingDesc: 'Geser angka untuk menyelesaikan',
    start: 'MULAI',
    continue: 'LANJUTKAN',
    new: 'BARU!',
    levelShort: 'LV',

    // Achievements
    yourAchievements: '🏆 PENCAPAIANMU',
    starterTitle: '🎁 Pemula',
    starterDesc: 'Membuka paket kartu pertama!',
    artistTitle: '🎨 Seniman',
    artistDesc: 'Buat 5 gambar',
    geniusTitle: '🧠 Jenius',
    geniusDesc: 'Selesaikan 10 permainan',
    unlocked: '✅ TERBUKA',
    locked: '⏳ TERKUNCI',
    progress: 'Progres',

    // Language Toggle
    language: 'Bahasa',
    indonesian: 'Indonesia',
    english: 'English'
  },
  en: {
    // Header
    appTitle: 'KidzPlay AI',
    welcomeBack: 'Welcome back, {name}! 🌟',
    searchGames: 'Search games...',

    // Picture Collection
    myPictureCollection: '📸 MY PICTURE COLLECTION ({count})',
    myThemeCollections: '🎨 MY THEME COLLECTIONS',
    organizeYourPictures: 'Organize your pictures by themes',
    generateNewImage: 'GENERATE NEW IMAGE',
    viewByThemes: 'View by themes',
    seeAll: 'See all →',

    // Theme Management
    createNewTheme: 'Create New Theme',
    themeName: 'Theme Name',
    themeDescription: 'Theme Description',
    enterThemeName: 'Enter theme name...',
    enterThemeDescription: 'Tell us about this theme...',
    optional: 'optional',
    cancel: 'Cancel',
    create: 'Create',
    pictures: 'pictures',
    generate: 'Generate',
    noImagesYet: 'No images yet',
    selectTheme: 'Select Theme',

    // Image Generation
    createAmazingPictures: 'Create amazing pictures with AI',
    describeYourPicture: 'Describe the picture you want',
    promptPlaceholder: 'Example: A cute cat playing in a garden full of colorful flowers...',
    generating: 'Generating',
    generateImage: 'Generate Image',
    generateAnother: 'Generate Another',
    saveToCollection: 'Save to Collection',

    // Game Common
    backToDashboard: 'Back to Dashboard',
    backToMenu: 'Back to Menu',
    chooseDifficulty: 'Choose Difficulty',
    easy: 'Easy',
    medium: 'Medium',
    hard: 'Hard',
    level: 'Level',
    bestScore: 'Best Score',
    gamesPlayed: 'Games Played',
    startGame: 'Start Game',
    playAgain: 'Play Again',
    restart: 'Restart',
    moves: 'Moves',
    time: 'Time',
    score: 'Score',
    finalScore: 'Final Score',
    congratulations: 'Congratulations!',

    // Memory Card Game
    needMoreImages: 'Need more images',
    pairs: 'pairs',
    memoryGameCompleted: 'You completed the memory game!',

    // Puzzle Game
    puzzlesCompleted: 'Puzzles Completed',
    bestTime: 'Best Time',
    availableImages: 'Available Images',
    pieces: 'pieces',
    chooseImage: 'Choose Image',
    noImagesAvailable: 'No images available',
    generateSomeImages: 'Generate some images first!',
    puzzleBoard: 'Puzzle Board',
    puzzlePieces: 'Puzzle Pieces',
    workspace: 'Workspace',
    shuffle: 'Shuffle',
    puzzleCompleted: 'Puzzle Completed!',
    greatJob: 'Great job! You completed the puzzle!',
    completionTime: 'Completion Time',
    selectImageForPuzzle: 'Select image for puzzle',
    selectYourImage: 'Select Your Image',

    // Sliding Puzzle Game
    puzzlesSolved: 'Puzzles Solved',
    lastDifficulty: 'Last Difficulty',
    grid: 'grid',
    howToPlay: 'How to Play',
    slidingRule1: 'Click numbers adjacent to the empty space to move them',
    slidingRule2: 'Arrange numbers from 1 to last in order',
    slidingRule3: 'Empty space should be at bottom right corner',
    puzzleSolved: 'Puzzle Solved!',
    excellentWork: 'Excellent work! You solved the sliding puzzle!',

    // Games Section
    startPlaying: '🎮 START PLAYING',
    memoryCardGame: 'Memory Card Game',
    memoryCardDesc: 'Match pairs of cards',
    puzzleGame: 'Puzzle Game',
    puzzleDesc: 'Drag & drop puzzle pieces',
    slidingPuzzle: 'Sliding Puzzle',
    slidingDesc: 'Slide numbers to solve',
    start: 'START',
    continue: 'CONTINUE',
    new: 'NEW!',
    levelShort: 'LV',

    // Achievements
    yourAchievements: '🏆 YOUR ACHIEVEMENTS',
    starterTitle: '🎁 Starter',
    starterDesc: 'Opened first card pack!',
    artistTitle: '🎨 Artist',
    artistDesc: 'Generate 5 images',
    geniusTitle: '🧠 Genius',
    geniusDesc: 'Complete 10 games',
    unlocked: '✅ UNLOCKED',
    locked: '⏳ LOCKED',
    progress: 'Progress',

    // Language Toggle
    language: 'Language',
    indonesian: 'Indonesia',
    english: 'English'
  }
}

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('id') // Default to Indonesian

  useEffect(() => {
    const savedLanguage = localStorage.getItem('kidzplay-language')
    if (savedLanguage && translations[savedLanguage]) {
      setLanguage(savedLanguage)
    }
  }, [])

  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setLanguage(newLanguage)
      localStorage.setItem('kidzplay-language', newLanguage)
    }
  }

  const t = (key, params = {}) => {
    let text = translations[language][key] || translations['id'][key] || key

    // Replace parameters in text
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param])
    })

    return text
  }

  return (
    <LanguageContext.Provider value={{ language, changeLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}
