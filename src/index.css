@tailwind base;
@tailwind components;
@tailwind utilities;

/* 3D Flip Card Effects */
.rotate-y-180 {
  transform: rotateY(180deg);
}

.backface-hidden {
  backface-visibility: hidden;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.perspective-1000 {
  perspective: 1000px;
}

/* 3D Card Flip Effects */
.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Custom animations */
@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-bounce-slow {
  animation: bounce-slow 2s infinite;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@layer base {
  body {
    font-family: 'Nunito', 'Comic Sans MS', cursive, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-bold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-bold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
  }

  .game-card {
    @apply bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-2 border-transparent hover:border-primary-200;
  }
}

/* Landscape Mode Optimization for Mobile */
@media screen and (max-width: 768px) and (orientation: landscape) {
  /* Encourage landscape mode for better gaming experience */
  .landscape-optimized {
    min-height: 100vh;
    padding: 0.5rem;
  }

  /* Compact header for landscape */
  .landscape-header {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
  }

  /* Optimize game grid for landscape */
  .landscape-games-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  /* Compact cards for landscape */
  .landscape-game-card {
    aspect-ratio: 1.2/1;
    padding: 0.75rem;
  }

  /* Smaller text for landscape */
  .landscape-text-sm {
    font-size: 0.75rem;
  }

  .landscape-text-base {
    font-size: 0.875rem;
  }

  .landscape-text-lg {
    font-size: 1rem;
  }
}

/* Portrait Mode Optimization for Mobile */
@media screen and (max-width: 768px) and (orientation: portrait) {
  .portrait-optimized {
    padding: 1rem;
  }

  /* Stack games vertically in portrait */
  .portrait-games-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Wider cards for portrait */
  .portrait-game-card {
    aspect-ratio: 2/1;
    padding: 1.5rem;
  }
}

/* Responsive Dashboard Layout */
.dashboard-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

@media (min-width: 640px) {
  .dashboard-container {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .dashboard-container {
    max-width: 1200px;
    padding: 2rem;
  }
}

/* Mobile-first responsive grid */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Landscape notification for better UX */
.landscape-hint {
  display: none;
}

@media screen and (max-width: 768px) and (orientation: portrait) {
  .landscape-hint {
    display: block;
    position: fixed;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    z-index: 1000;
    animation: fadeInOut 3s ease-in-out;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}
