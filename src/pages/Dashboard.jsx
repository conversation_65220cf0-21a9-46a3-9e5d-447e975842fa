import { useState, useEffect } from 'react'
import { Search, Plus, Trophy, Star, Palette } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { getImageCollection, getGameProgress, getAchievements } from '../utils/localStorage'
import { playClickSound } from '../utils/audio'
import { useLanguage } from '../contexts/LanguageContext'
import LanguageToggle from '../components/LanguageToggle'
import ThemeCollectionView from '../components/ThemeCollectionView'
import ImageGeneratorModal from '../components/ImageGeneratorModal'

const Dashboard = ({ childName }) => {
  const [imageCollection, setImageCollection] = useState({ themes: [] })
  const [gameProgress, setGameProgress] = useState({})
  const [achievements, setAchievements] = useState({})
  const [showThemeView, setShowThemeView] = useState(false)
  const [showImageGenerator, setShowImageGenerator] = useState(false)
  const [selectedThemeId, setSelectedThemeId] = useState(null)
  const [selectedThemeName, setSelectedThemeName] = useState(null)
  const { t } = useLanguage()
  const navigate = useNavigate()

  useEffect(() => {
    if (childName) {
      setImageCollection(getImageCollection(childName))
      setGameProgress(getGameProgress(childName))
      setAchievements(getAchievements(childName))
    }
  }, [childName])

  // Get all images from all themes
  const allImages = imageCollection.themes?.reduce((acc, theme) => {
    return [...acc, ...theme.images]
  }, []) || []
  const totalImages = allImages.length

  const handleGenerateImage = (themeId = null, themeName = null) => {
    setSelectedThemeId(themeId)
    setSelectedThemeName(themeName)
    setShowImageGenerator(true)
  }

  const handleImageGenerated = () => {
    setImageCollection(getImageCollection(childName))
  }

  const games = [
    {
      id: 'memory',
      title: t('memoryCardGame'),
      icon: '🃏',
      description: t('memoryCardDesc'),
      progress: gameProgress.memoryCard,
      color: 'from-blue-500 to-purple-600',
      thumbnail: '/images/memory-card.jpg'
    },
    {
      id: 'puzzle',
      title: t('puzzleGame'),
      icon: '🧩',
      description: t('puzzleDesc'),
      progress: gameProgress.puzzle,
      color: 'from-green-500 to-teal-600',
      thumbnail: '/images/puzzle.jpg'
    },
    {
      id: 'sliding',
      title: t('slidingPuzzle'),
      icon: '🔄',
      description: t('slidingDesc'),
      progress: gameProgress.slidingPuzzle,
      color: 'from-orange-500 to-red-600',
      thumbnail: '/images/sliding-puzzle.jpg'
    }
  ]

  const achievementsList = [
    {
      key: 'starterPack',
      title: t('starterTitle'),
      description: t('starterDesc'),
      unlocked: achievements.starterPack?.unlocked
    },
    {
      key: 'artist',
      title: t('artistTitle'),
      description: t('artistDesc'),
      unlocked: achievements.artist?.unlocked,
      progress: achievements.artist?.progress,
      target: achievements.artist?.target
    },
    {
      key: 'genius',
      title: t('geniusTitle'),
      description: t('geniusDesc'),
      unlocked: achievements.genius?.unlocked,
      progress: achievements.genius?.progress,
      target: achievements.genius?.target
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="flex items-center justify-between mb-8 bg-white/10 backdrop-blur-sm rounded-2xl p-6">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center gap-2">
              🏠 {t('appTitle')}
            </h1>
            <p className="text-white/80 text-lg">
              {t('welcomeBack', { name: childName })}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <LanguageToggle />
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={t('searchGames')}
                className="pl-10 pr-4 py-2 rounded-full bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 focus:outline-none focus:ring-2 focus:ring-white/50"
              />
            </div>
          </div>
        </header>

        {/* Picture Collection */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              {t('myPictureCollection', { count: totalImages })}
            </h2>
            <div className="flex gap-3">
              <button
                onClick={() => {
                  playClickSound()
                  setShowThemeView(true)
                }}
                className="text-white/80 hover:text-white transition-colors flex items-center gap-2"
              >
                <Palette className="w-5 h-5" />
                {t('viewByThemes')}
              </button>
              <button className="text-white/80 hover:text-white transition-colors">
                {t('seeAll')}
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {allImages.slice(0, 5).map((image) => (
              <div
                key={image.id}
                className="bg-white rounded-2xl shadow-lg p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer"
                onClick={() => playClickSound()}
              >
                <img
                  src={image.url || image.dataUrl}
                  alt={image.name}
                  className="w-full aspect-square object-cover rounded-xl mb-2"
                />
                <p className="text-xs font-bold text-gray-700 text-center truncate">{image.name}</p>
              </div>
            ))}

            {/* Generate New Image Button */}
            <div
              className="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-lg p-3 hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer flex flex-col items-center justify-center text-white min-h-[100px]"
              onClick={() => handleGenerateImage()}
            >
              <Plus className="w-8 h-8 mb-1" />
              <p className="text-xs font-bold text-center">{t('generateNewImage')}</p>
            </div>
          </div>
        </section>

        {/* Games Section */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              {t('startPlaying')}
            </h2>
            <button className="text-white/80 hover:text-white transition-colors">
              {t('seeAll')}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {games.map((game) => (
              <div
                key={game.id}
                className={`relative aspect-square bg-gradient-to-br ${game.color} rounded-2xl shadow-lg text-white hover:shadow-xl transition-all duration-200 transform hover:scale-105 cursor-pointer overflow-hidden`}
                onClick={(e) => {
                  e.stopPropagation()
                  playClickSound()
                  navigate(`/game/${game.id}`)
                }}
              >
                {/* Background Thumbnail */}
                <div className="absolute inset-0 opacity-20">
                  <img
                    src={game.thumbnail}
                    alt={game.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Content */}
                <div className="relative z-10 h-full p-6 flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-3xl">{game.icon}</div>
                    <div className="text-xs bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full font-bold">
                      {game.progress?.gamesPlayed > 0 ? `${t('level')} ${game.progress.level || 1}` : t('new')}
                    </div>
                  </div>

                  <div className="flex-1 flex flex-col justify-center text-center">
                    <h3 className="text-lg font-bold mb-2">{game.title}</h3>
                    <p className="text-white/80 text-sm mb-4">{game.description}</p>
                  </div>

                  <div className="flex flex-col items-center space-y-2">
                    {game.progress?.gamesPlayed > 0 && (
                      <div className="flex justify-center">
                        {[...Array(3)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-300 fill-current" />
                        ))}
                      </div>
                    )}

                    <button
                      className="bg-white/30 backdrop-blur-sm hover:bg-white/40 rounded-full px-6 py-2 text-sm font-bold transition-colors"
                      onClick={(e) => {
                        e.stopPropagation()
                        playClickSound()
                        navigate(`/game/${game.id}`)
                      }}
                    >
                      {t('start')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Achievements Section */}
        <section className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              {t('yourAchievements')}
            </h2>
            <button className="text-white/80 hover:text-white transition-colors">
              {t('seeAll')}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {achievementsList.map((achievement) => (
              <div key={achievement.key} className={`bg-white/10 backdrop-blur-sm rounded-2xl shadow-lg p-6 text-white border-2 ${achievement.unlocked ? 'border-yellow-400' : 'border-white/20'}`}>
                <div className="text-center">
                  <div className="text-3xl mb-3">{achievement.title.split(' ')[0]}</div>
                  <h3 className="text-lg font-bold mb-2">{achievement.title.split(' ').slice(1).join(' ')}</h3>
                  <p className="text-white/80 mb-4 text-sm">{achievement.description}</p>

                  {achievement.unlocked ? (
                    <div className="bg-yellow-400 text-yellow-900 rounded-full px-4 py-2 text-sm font-bold">
                      {t('unlocked')}
                    </div>
                  ) : achievement.progress !== undefined ? (
                    <div className="space-y-2">
                      <div className="text-sm">
                        {t('progress')}: {achievement.progress}/{achievement.target}
                      </div>
                      <div className="bg-white/20 rounded-full h-2">
                        <div
                          className="bg-yellow-400 rounded-full h-2 transition-all duration-300"
                          style={{ width: `${(achievement.progress / achievement.target) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-white/20 rounded-full px-4 py-2 text-sm font-bold">
                      {t('locked')}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Theme Collection Modal */}
        {showThemeView && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-gradient-to-br from-purple-600 via-blue-600 to-teal-600 rounded-3xl p-8 max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-3xl font-bold text-white">{t('myThemeCollections')}</h2>
                <button
                  onClick={() => {
                    playClickSound()
                    setShowThemeView(false)
                  }}
                  className="text-white/80 hover:text-white text-2xl font-bold"
                >
                  ✕
                </button>
              </div>

              <ThemeCollectionView
                childName={childName}
                onGenerateImage={handleGenerateImage}
                onImageSelect={() => {}}
              />
            </div>
          </div>
        )}

        {/* Image Generator Modal */}
        {showImageGenerator && (
          <ImageGeneratorModal
            childName={childName}
            selectedThemeId={selectedThemeId}
            selectedThemeName={selectedThemeName}
            onClose={() => {
              setShowImageGenerator(false)
              setSelectedThemeId(null)
              setSelectedThemeName(null)
            }}
            onImageGenerated={handleImageGenerated}
          />
        )}
      </div>
    </div>
  )
}

export default Dashboard
