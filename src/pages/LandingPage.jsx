import { Gamepad2, Pa<PERSON>, <PERSON>, <PERSON>, <PERSON>, Sparkles } from 'lucide-react'
import { playClickSound } from '../utils/audio'

const LandingPage = ({ onStartAdventure }) => {
  const features = [
    {
      icon: <Gamepad2 className="w-8 h-8" />,
      title: "Permainan Memori",
      description: "Permainan mencocokkan kartu yang seru untuk melatih ingatan!"
    },
    {
      icon: <Palette className="w-8 h-8" />,
      title: "Puzzle Seru",
      description: "Seret & lepas potongan puzzle dengan gambar favoritmu!"
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "Puzzle Geser",
      description: "Tantang dirimu dengan permainan geser angka!"
    },
    {
      icon: <Camera className="w-8 h-8" />,
      title: "Pembuat Gambar AI",
      description: "Buat gambar menakjubkan dengan keajaiban AI!"
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "<PERSON><PERSON> P<PERSON>badi",
      description: "Kumpulkan dan gunakan gambarmu di semua permainan!"
    },
    {
      icon: <Trophy className="w-8 h-8" />,
      title: "Pelacakan Progres",
      description: "Lihat pencapaianmu dan terus berkembang!"
    }
  ]

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-white">
      {/* Hero Section */}
      <div className="text-center mb-8 sm:mb-12 max-w-4xl">
        <div className="mb-4 sm:mb-6">
          <h1 className="text-4xl sm:text-6xl md:text-8xl font-bold mb-4 animate-bounce-slow">
            🎮 <span className="bg-gradient-to-r from-yellow-300 to-orange-400 bg-clip-text text-transparent">
              KidzPlay AI
            </span> 🎨
          </h1>
        </div>

        <p className="text-lg sm:text-2xl md:text-3xl font-semibold mb-6 sm:mb-8 text-yellow-100 px-4">
          "Di Mana Imajinasi Bertemu Kesenangan Interaktif!"
        </p>

        <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8 text-sm sm:text-lg">
          <span className="bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2">
            👶 Ramah Anak
          </span>
          <span className="bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2">
            🎯 Usia 3+
          </span>
          <span className="bg-white/20 backdrop-blur-sm rounded-full px-3 sm:px-4 py-2 flex items-center gap-2">
            🆓 Gratis Sepenuhnya
          </span>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12 max-w-6xl px-4">
        {features.map((feature, index) => (
          <div
            key={index}
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 sm:p-6 text-center hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <div className="text-yellow-300 mb-3 sm:mb-4 flex justify-center">
              {feature.icon}
            </div>
            <h3 className="text-lg sm:text-xl font-bold mb-2">{feature.title}</h3>
            <p className="text-white/80 text-sm sm:text-base">{feature.description}</p>
          </div>
        ))}
      </div>

      {/* CTA Button */}
      <div className="text-center px-4">
        <button
          onClick={() => {
            playClickSound()
            onStartAdventure()
          }}
          className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold text-lg sm:text-2xl py-3 sm:py-4 px-8 sm:px-12 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 animate-pulse-slow"
        >
          🚀 MULAI PETUALANGANMU! 🚀
        </button>

        <p className="mt-4 text-white/70 text-base sm:text-lg">
          Dapatkan 6 gambar menakjubkan gratis saat memulai!
        </p>
      </div>

      {/* Floating Elements */}
      <div className="fixed top-10 left-10 text-4xl animate-bounce">🌟</div>
      <div className="fixed top-20 right-20 text-3xl animate-pulse">🎈</div>
      <div className="fixed bottom-20 left-20 text-3xl animate-wiggle">🦄</div>
      <div className="fixed bottom-10 right-10 text-4xl animate-bounce-slow">✨</div>
    </div>
  )
}

export default LandingPage
