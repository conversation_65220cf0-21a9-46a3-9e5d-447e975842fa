import { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, RotateCcw, Trophy, Star, Clock, Target } from 'lucide-react'
import {
  getImageCollection,
  getGameProgress,
  updateGameProgress,
  unlockAchievement
} from '../../utils/localStorage'
import { playClickSound, playCardMatchSound, playSparkleSound, playCardFlipSound } from '../../utils/audio'
import { useLanguage } from '../../contexts/LanguageContext'

// Sparkle component for celebration effect
const Sparkle = ({ delay = 0 }) => {
  return (
    <div
      className="absolute pointer-events-none"
      style={{
        animation: `sparkle 2s ease-in-out ${delay}s infinite`,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
      }}
    >
      <div className="text-yellow-400 text-2xl animate-pulse">✨</div>
    </div>
  )
}

const MemoryCardGame = ({ childName }) => {
  const [gameState, setGameState] = useState('menu') // menu, playing, paused, completed
  const [cards, setCards] = useState([])
  const [flippedCards, setFlippedCards] = useState([])
  const [matchedCards, setMatchedCards] = useState([])
  const [sparklingCards, setSparklingCards] = useState([]) // For sparkling animation
  const [moves, setMoves] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [gameStartTime, setGameStartTime] = useState(null)
  const [difficulty, setDifficulty] = useState('easy') // easy: 4x3, medium: 4x4, hard: 6x4
  const [availableImages, setAvailableImages] = useState([])
  const [gameProgress, setGameProgress] = useState({})
  const [score, setScore] = useState(0)
  const [scoreBreakdown, setScoreBreakdown] = useState(null)
  const { t } = useLanguage()
  const navigate = useNavigate()

  // Difficulty settings
  const difficultySettings = {
    easy: { pairs: 6, gridCols: 4, gridRows: 3, baseScore: 1000, perfectTime: 60 },
    medium: { pairs: 8, gridCols: 4, gridRows: 4, baseScore: 1500, perfectTime: 90 },
    hard: { pairs: 12, gridCols: 6, gridRows: 4, baseScore: 2000, perfectTime: 120 }
  }

  useEffect(() => {
    if (childName) {
      // Load available images
      const collection = getImageCollection(childName)
      const allImages = collection.themes?.reduce((acc, theme) => {
        return [...acc, ...theme.images]
      }, []) || []
      setAvailableImages(allImages)

      // Load game progress
      const progress = getGameProgress(childName)
      setGameProgress(progress.memoryCard || { level: 1, bestScore: null, gamesPlayed: 0 })
    }
  }, [childName])

  // Timer effect
  useEffect(() => {
    let interval = null
    if (gameState === 'playing' && gameStartTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - gameStartTime) / 1000))
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [gameState, gameStartTime])

  // Check for matches
  useEffect(() => {
    if (flippedCards.length === 2) {
      const [first, second] = flippedCards
      if (cards[first].imageId === cards[second].imageId) {
        // Match found
        playCardMatchSound()

        // Add sparkling effect
        setSparklingCards([first, second])

        // Play sparkle sound with slight delay
        setTimeout(() => {
          playSparkleSound()
        }, 200)

        // Remove sparkling effect after animation
        setTimeout(() => {
          setSparklingCards([])
        }, 2000)

        setMatchedCards(prev => [...prev, first, second])
        setFlippedCards([])
        setScore(prev => prev + 100) // Base points per match
      } else {
        // No match
        setTimeout(() => {
          setFlippedCards([])
        }, 1000)
      }
      setMoves(prev => prev + 1)
    }
  }, [flippedCards, cards, timeElapsed])

  // Check for game completion
  useEffect(() => {
    if (matchedCards.length === cards.length && cards.length > 0 && gameState === 'playing') {
      // Calculate final score with bonuses ONCE
      const settings = difficultySettings[difficulty]
      const baseScore = score // Base score from matches

      // Time bonus (faster = more points)
      const timeBonus = Math.max(0, Math.floor((settings.perfectTime - timeElapsed) * 10))

      // Efficiency bonus (fewer moves = more points)
      const minMoves = settings.pairs // Perfect game = number of pairs
      const moveBonus = Math.max(0, (minMoves * 2 - moves) * 20)

      // Difficulty bonus
      const difficultyBonus = settings.baseScore

      // Calculate final score
      let finalScore = baseScore + timeBonus + moveBonus + difficultyBonus

      // Ensure minimum score
      finalScore = Math.max(finalScore, 100)

      // Store score breakdown for display
      setScoreBreakdown({
        baseScore,
        timeBonus,
        moveBonus,
        difficultyBonus,
        finalScore
      })

      // Set game completed and final score
      setGameState('completed')
      setScore(finalScore)

      // Update progress
      const newProgress = {
        ...gameProgress,
        gamesPlayed: (gameProgress.gamesPlayed || 0) + 1,
        bestScore: Math.max(gameProgress.bestScore || 0, finalScore),
        level: Math.min((gameProgress.level || 1) + (finalScore > (gameProgress.bestScore || 0) ? 1 : 0), 10)
      }

      updateGameProgress(childName, 'memoryCard', newProgress)
      setGameProgress(newProgress)

      // Check for achievements
      if (newProgress.gamesPlayed >= 5) {
        unlockAchievement(childName, 'memoryMaster')
      }
    }
  }, [matchedCards.length, cards.length, gameState])

  const shuffleArray = (array) => {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  const startGame = useCallback(() => {
    if (availableImages.length < difficultySettings[difficulty].pairs) {
      alert(t('needMoreImages'))
      return
    }

    playClickSound()

    // Select random images for the game
    const selectedImages = shuffleArray(availableImages)
      .slice(0, difficultySettings[difficulty].pairs)

    // Create card pairs
    const cardPairs = selectedImages.flatMap((image, index) => [
      { id: index * 2, imageId: image.id, image: image },
      { id: index * 2 + 1, imageId: image.id, image: image }
    ])

    // Shuffle cards
    const shuffledCards = shuffleArray(cardPairs)

    setCards(shuffledCards)
    setFlippedCards([])
    setMatchedCards([])
    setSparklingCards([])
    setMoves(0)
    setTimeElapsed(0)
    setScore(0)
    setScoreBreakdown(null)
    setGameStartTime(Date.now())
    setGameState('playing')
  }, [availableImages, difficulty, t])

  const flipCard = (index) => {
    if (
      gameState !== 'playing' ||
      flippedCards.length >= 2 ||
      flippedCards.includes(index) ||
      matchedCards.includes(index)
    ) {
      return
    }

    playCardFlipSound()
    setFlippedCards(prev => [...prev, index])
  }

  const resetGame = () => {
    playClickSound()
    setGameState('menu')
    setCards([])
    setFlippedCards([])
    setMatchedCards([])
    setSparklingCards([])
    setMoves(0)
    setTimeElapsed(0)
    setScore(0)
    setScoreBreakdown(null)
    setGameStartTime(null)
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (gameState === 'menu') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-2 sm:p-4 landscape-optimized memory-game-portrait">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-center justify-between mb-4 sm:mb-8 gap-4 sm:gap-0">
            <button
              onClick={() => {
                playClickSound()
                navigate('/dashboard')
              }}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <ArrowLeft className="w-4 h-4 sm:w-6 sm:h-6" />
              <span className="font-bold text-sm sm:text-base">{t('backToDashboard')}</span>
            </button>

            <div className="text-center">
              <h1 className="text-xl sm:text-3xl font-bold text-white mb-1 sm:mb-2 landscape-text-lg">🃏 {t('memoryCardGame')}</h1>
              <p className="text-white/80 text-sm sm:text-base landscape-text-sm hidden sm:block">{t('memoryCardDesc')}</p>
            </div>

            <div className="w-8 sm:w-32"></div>
          </div>

          {/* Game Stats */}
          <div className="bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-4 sm:mb-8">
            <div className="grid grid-cols-3 gap-3 sm:gap-6 text-center text-white">
              <div>
                <div className="text-lg sm:text-2xl font-bold landscape-text-base">{gameProgress.level || 1}</div>
                <div className="text-xs sm:text-sm opacity-80 landscape-text-sm">{t('level')}</div>
              </div>
              <div>
                <div className="text-lg sm:text-2xl font-bold landscape-text-base">{gameProgress.bestScore || 0}</div>
                <div className="text-xs sm:text-sm opacity-80 landscape-text-sm">{t('bestScore')}</div>
              </div>
              <div>
                <div className="text-lg sm:text-2xl font-bold landscape-text-base">{gameProgress.gamesPlayed || 0}</div>
                <div className="text-xs sm:text-sm opacity-80 landscape-text-sm">{t('gamesPlayed')}</div>
              </div>
            </div>
          </div>

          {/* Difficulty Selection */}
          <div className="bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-8 shadow-2xl">
            <h2 className="text-lg sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6 text-center landscape-text-base">{t('chooseDifficulty')}</h2>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mb-4 sm:mb-8">
              {Object.entries(difficultySettings).map(([level, settings]) => (
                <button
                  key={level}
                  onClick={() => {
                    playClickSound()
                    setDifficulty(level)
                  }}
                  className={`p-3 sm:p-6 rounded-xl sm:rounded-2xl border-2 transition-all duration-200 portrait-game-card ${
                    difficulty === level
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-xl sm:text-2xl mb-1 sm:mb-2">
                      {level === 'easy' ? '😊' : level === 'medium' ? '🤔' : '😤'}
                    </div>
                    <div className="font-bold text-gray-800 capitalize mb-1 sm:mb-2 text-sm sm:text-base landscape-text-sm portrait-text-sm">{t(level)}</div>
                    <div className="text-xs sm:text-sm text-gray-600 landscape-text-sm portrait-text-sm">
                      {settings.pairs} {t('pairs')} • {settings.gridCols}×{settings.gridRows}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="text-center">
              <button
                onClick={startGame}
                disabled={availableImages.length < difficultySettings[difficulty].pairs}
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {t('startGame')}
              </button>

              {availableImages.length < difficultySettings[difficulty].pairs && (
                <p className="text-red-500 text-sm mt-2">
                  {t('needMoreImages')} ({availableImages.length}/{difficultySettings[difficulty].pairs})
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen p-2 sm:p-4 relative landscape-optimized memory-game-portrait"
      style={{
        backgroundImage: 'url(/images/background-game.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/30 via-purple-500/30 to-pink-500/30"></div>

      {/* CSS for sparkle animation */}
      <style jsx>{`
        @keyframes sparkle {
          0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
          }
        }

        @keyframes celebration {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
          100% {
            transform: scale(1);
          }
        }

        .celebration-card {
          animation: celebration 0.6s ease-in-out;
        }

        .sparkle-container {
          position: absolute;
          inset: 0;
          pointer-events: none;
          overflow: hidden;
          border-radius: 1rem;
        }
      `}</style>

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Game Header */}
        <div className="flex flex-col sm:flex-row items-center justify-between mb-3 sm:mb-6 gap-3 sm:gap-0">
          <button
            onClick={() => {
              playClickSound()
              setGameState('menu')
            }}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 sm:w-6 sm:h-6" />
            <span className="font-bold text-sm sm:text-base">{t('backToMenu')}</span>
          </button>

          {/* Game Stats */}
          <div className="flex items-center gap-3 sm:gap-6 bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl px-3 sm:px-6 py-2 sm:py-3">
            <div className="flex items-center gap-1 sm:gap-2 text-white">
              <Clock className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-bold text-sm sm:text-base landscape-text-sm">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex items-center gap-1 sm:gap-2 text-white">
              <Target className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-bold text-sm sm:text-base landscape-text-sm">{moves} <span className="hidden sm:inline">{t('moves')}</span></span>
            </div>
            <div className="flex items-center gap-1 sm:gap-2 text-white">
              <Star className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="font-bold text-sm sm:text-base landscape-text-sm portrait-text-sm">{score}</span>
            </div>
          </div>

          <button
            onClick={resetGame}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <RotateCcw className="w-4 h-4 sm:w-6 sm:h-6" />
            <span className="font-bold text-sm sm:text-base">{t('restart')}</span>
          </button>
        </div>

        {/* Game Board */}
        <div
          className="grid gap-2 sm:gap-4 lg:gap-6 mx-auto memory-cards-grid-portrait"
          style={{
            gridTemplateColumns: `repeat(${difficultySettings[difficulty].gridCols}, 1fr)`,
            maxWidth: `${difficultySettings[difficulty].gridCols * (window.innerWidth < 640 ? 70 : window.innerWidth < 1024 ? 120 : 160)}px`
          }}
        >
          {cards.map((card, index) => {
            const isFlipped = flippedCards.includes(index) || matchedCards.includes(index)
            const isSparklingCard = sparklingCards.includes(index)

            return (
              <div
                key={card.id}
                onClick={() => flipCard(index)}
                className={`aspect-square cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                  isSparklingCard ? 'celebration-card' : ''
                }`}
                style={{ perspective: '1000px' }}
              >
                {/* Card Container with 3D flip effect */}
                <div
                  className={`
                    relative w-full h-full transition-transform duration-700
                    ${isFlipped ? 'rotate-y-180' : ''}
                    ${matchedCards.includes(index) ? 'ring-4 ring-green-400 rounded-2xl' : ''}
                  `}
                  style={{
                    transformStyle: 'preserve-3d',
                    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
                  }}
                >
                  {/* Sparkle Effect for Matched Cards */}
                  {isSparklingCard && (
                    <div className="sparkle-container">
                      {[...Array(8)].map((_, i) => (
                        <Sparkle key={i} delay={i * 0.2} />
                      ))}
                    </div>
                  )}

                  {/* Card Back */}
                  <div
                    className="absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg"
                    style={{ backfaceVisibility: 'hidden' }}
                  >
                    <div className="text-3xl sm:text-4xl lg:text-6xl drop-shadow-lg">🎈</div>
                  </div>

                  {/* Card Front */}
                  <div
                    className="absolute inset-0 w-full h-full rounded-xl sm:rounded-2xl bg-white p-1 sm:p-2 shadow-lg"
                    style={{
                      backfaceVisibility: 'hidden',
                      transform: 'rotateY(180deg)'
                    }}
                  >
                    <img
                      src={card.image.url || card.image.dataUrl}
                      alt={card.image.name}
                      className="w-full h-full object-cover rounded-lg sm:rounded-xl"
                    />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Game Completed Modal */}
        {gameState === 'completed' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('congratulations')}</h2>
              <p className="text-gray-600 mb-6">{t('memoryGameCompleted')}</p>

              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-2 gap-4 text-center mb-4">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                    <div className="text-sm text-gray-600">{t('time')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{moves}</div>
                    <div className="text-sm text-gray-600">{t('moves')}</div>
                  </div>
                </div>

                {/* Score Breakdown */}
                {scoreBreakdown && (
                  <div className="border-t pt-4">
                    <div className="text-sm text-gray-600 space-y-2">
                      <div className="flex justify-between">
                        <span>Pasangan ditemukan:</span>
                        <span className="font-bold">+{scoreBreakdown.baseScore}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bonus waktu:</span>
                        <span className="font-bold text-blue-600">+{scoreBreakdown.timeBonus}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bonus efisiensi:</span>
                        <span className="font-bold text-green-600">+{scoreBreakdown.moveBonus}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bonus tingkat kesulitan:</span>
                        <span className="font-bold text-orange-600">+{scoreBreakdown.difficultyBonus}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between text-lg font-bold">
                        <span>Total Skor:</span>
                        <span className="text-purple-600">{scoreBreakdown.finalScore}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex gap-3">
                <button
                  onClick={startGame}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200"
                >
                  {t('playAgain')}
                </button>
                <button
                  onClick={() => {
                    playClickSound()
                    navigate('/dashboard')
                  }}
                  className="flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('backToDashboard')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MemoryCardGame
