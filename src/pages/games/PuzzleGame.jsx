import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON>Left, RotateCcw, Trophy, Clock, Shuffle } from 'lucide-react'
import {
  getImageCollection,
  getGameProgress,
  updateGameProgress,
  unlockAchievement
} from '../../utils/localStorage'
import { playClickSound, playSuccessSound } from '../../utils/audio'
import { useLanguage } from '../../contexts/LanguageContext'

const PuzzleGame = ({ childName }) => {
  const [gameState, setGameState] = useState('menu') // menu, playing, completed
  const [selectedImage, setSelectedImage] = useState(null)
  const [puzzlePieces, setPuzzlePieces] = useState([])
  const [draggedPiece, setDraggedPiece] = useState(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [gameStartTime, setGameStartTime] = useState(null)
  const [difficulty, setDifficulty] = useState('easy')
  const [availableImages, setAvailableImages] = useState([])
  const [gameProgress, setGameProgress] = useState({})
  const [score, setScore] = useState(0)
  const workspaceRef = useRef(null)
  const { t } = useLanguage()
  const navigate = useNavigate()

  // Difficulty settings
  const difficultySettings = {
    easy: { gridSize: 3, timeBonus: 200 },
    medium: { gridSize: 4, timeBonus: 300 },
    hard: { gridSize: 5, timeBonus: 500 }
  }

  useEffect(() => {
    if (childName) {
      const collection = getImageCollection(childName)
      const allImages = collection.themes?.reduce((acc, theme) => {
        return [...acc, ...theme.images]
      }, []) || []
      setAvailableImages(allImages)

      const progress = getGameProgress(childName)
      setGameProgress(progress.puzzle || { completed: 0, favoriteImage: null, bestTime: null })
    }
  }, [childName])

  // Timer effect
  useEffect(() => {
    let interval = null
    if (gameState === 'playing' && gameStartTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - gameStartTime) / 1000))
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [gameState, gameStartTime])

  // Check for completion
  useEffect(() => {
    if (gameState !== 'playing' || puzzlePieces.length === 0) return

    const totalPieces = difficultySettings[difficulty].gridSize ** 2
    const solvedCount = puzzlePieces.filter(piece => piece.solved).length

    if (solvedCount === totalPieces) {
      setTimeout(() => {
        setGameState('completed')
        playSuccessSound()

        const finalTime = Math.floor((Date.now() - gameStartTime) / 1000)
        setTimeElapsed(finalTime)

        const finalScore = Math.max(difficultySettings[difficulty].timeBonus - finalTime, 50)
        setScore(finalScore)

        const newProgress = {
          ...gameProgress,
          completed: (gameProgress.completed || 0) + 1,
          bestTime: gameProgress.bestTime ? Math.min(gameProgress.bestTime, finalTime) : finalTime,
          favoriteImage: selectedImage?.id
        }

        updateGameProgress(childName, 'puzzle', newProgress)
        setGameProgress(newProgress)

        if (newProgress.completed >= 3) {
          unlockAchievement(childName, 'puzzleMaster')
        }
      }, 500)
    }
  }, [puzzlePieces, gameState, difficulty, gameStartTime, gameProgress, selectedImage, childName])

  const createPuzzlePieces = (image) => {
    const gridSize = difficultySettings[difficulty].gridSize
    const pieces = []
    const pieceWidth = 100
    const pieceHeight = 100

    // Workspace dimensions for random placement
    const workspaceWidth = 500
    const workspaceHeight = 350

    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        // Random position in workspace (not in correct position)
        const randomX = Math.random() * (workspaceWidth - pieceWidth)
        const randomY = Math.random() * (workspaceHeight - pieceHeight)

        pieces.push({
          id: `${row}-${col}`,
          x: randomX,
          y: randomY,
          width: pieceWidth,
          height: pieceHeight,
          correctRow: row,
          correctCol: col,
          solvedX: col * pieceWidth + 50, // Add some offset for solved area
          solvedY: row * pieceHeight + 50,
          solved: false,
          visible: true,
          image: image
        })
      }
    }

    return pieces
  }

  const startGame = (image) => {
    playClickSound()
    setSelectedImage(image)
    const pieces = createPuzzlePieces(image)
    setPuzzlePieces(pieces)
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(Date.now())
    setGameState('playing')
  }

  const handleMouseDown = (e, piece) => {
    if (piece.solved) return

    setDraggedPiece(piece)
    const rect = workspaceRef.current.getBoundingClientRect()
    const offsetX = e.clientX - rect.left - piece.x
    const offsetY = e.clientY - rect.top - piece.y
    setDragOffset({ x: offsetX, y: offsetY })

    // Move piece to end of array (on top)
    setPuzzlePieces(prev => {
      const filtered = prev.filter(p => p.id !== piece.id)
      return [...filtered, { ...piece, visible: true }]
    })
  }

  const handleMouseMove = (e) => {
    if (!draggedPiece || draggedPiece.solved) return

    const rect = workspaceRef.current.getBoundingClientRect()
    const newX = e.clientX - rect.left - dragOffset.x
    const newY = e.clientY - rect.top - dragOffset.y

    // Constrain to workspace bounds
    const constrainedX = Math.max(0, Math.min(newX, rect.width - draggedPiece.width))
    const constrainedY = Math.max(0, Math.min(newY, rect.height - draggedPiece.height))

    setPuzzlePieces(prev =>
      prev.map(piece =>
        piece.id === draggedPiece.id
          ? { ...piece, x: constrainedX, y: constrainedY }
          : piece
      )
    )
  }

  const handleMouseUp = () => {
    if (!draggedPiece || draggedPiece.solved) return

    // Check if piece is close to its correct position
    const tolerance = 30
    const piece = puzzlePieces.find(p => p.id === draggedPiece.id)

    if (piece &&
        Math.abs(piece.x - piece.solvedX) <= tolerance &&
        Math.abs(piece.y - piece.solvedY) <= tolerance) {

      // Snap to correct position
      setPuzzlePieces(prev =>
        prev.map(p =>
          p.id === draggedPiece.id
            ? { ...p, x: p.solvedX, y: p.solvedY, solved: true }
            : p
        )
      )
      playSuccessSound()
    }

    setDraggedPiece(null)
    setDragOffset({ x: 0, y: 0 })
  }

  const resetGame = () => {
    playClickSound()
    setGameState('menu')
    setSelectedImage(null)
    setPuzzlePieces([])
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(null)
  }

  const shufflePieces = () => {
    playClickSound()
    const pieces = createPuzzlePieces(selectedImage)
    setPuzzlePieces(pieces)
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (gameState === 'menu') {
    return (
      <div
        className="min-h-screen p-4 relative"
        style={{
          backgroundImage: 'url(/images/background-game.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"></div>

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => {
                playClickSound()
                navigate('/dashboard')
              }}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-bold">{t('backToDashboard')}</span>
            </button>

            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">🧩 {t('puzzleGame')}</h1>
              <p className="text-white/80">{t('puzzleDesc')}</p>
            </div>

            <div className="w-32"></div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8">
            <div className="grid grid-cols-3 gap-6 text-center text-white">
              <div>
                <div className="text-2xl font-bold">{gameProgress.completed || 0}</div>
                <div className="text-sm opacity-80">{t('puzzlesCompleted')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {gameProgress.bestTime ? formatTime(gameProgress.bestTime) : '--:--'}
                </div>
                <div className="text-sm opacity-80">{t('bestTime')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{availableImages.length}</div>
                <div className="text-sm opacity-80">{t('availableImages')}</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-2xl mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseDifficulty')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(difficultySettings).map(([level, settings]) => (
                <button
                  key={level}
                  onClick={() => {
                    playClickSound()
                    setDifficulty(level)
                  }}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 ${
                    difficulty === level
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {level === 'easy' ? '😊' : level === 'medium' ? '🤔' : '😤'}
                    </div>
                    <div className="font-bold text-gray-800 capitalize mb-2">{t(level)}</div>
                    <div className="text-sm text-gray-600">
                      {settings.gridSize}×{settings.gridSize} {t('pieces')}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseImage')}</h2>

            {availableImages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <div className="text-4xl mb-4">📸</div>
                <p>{t('noImagesAvailable')}</p>
                <p className="text-sm mt-2">{t('generateSomeImages')}</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {availableImages.map((image) => (
                  <button
                    key={image.id}
                    onClick={() => startGame(image)}
                    className="group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    <img
                      src={image.url || image.dataUrl}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                      <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                        <span className="text-2xl">🧩</span>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                      <p className="text-white text-xs font-bold truncate">{image.name}</p>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen p-4 relative"
      style={{
        backgroundImage: 'url(/images/background-game.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"></div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => {
              playClickSound()
              setGameState('menu')
            }}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
            <span className="font-bold">{t('backToMenu')}</span>
          </button>

          <div className="flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3">
            <div className="flex items-center gap-2 text-white">
              <Clock className="w-5 h-5" />
              <span className="font-bold">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex items-center gap-2 text-white">
              <Trophy className="w-5 h-5" />
              <span className="font-bold">{puzzlePieces.filter(p => p.solved).length}/{puzzlePieces.length}</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={shufflePieces}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <Shuffle className="w-6 h-6" />
              <span className="font-bold">{t('shuffle')}</span>
            </button>
            <button
              onClick={resetGame}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <RotateCcw className="w-6 h-6" />
              <span className="font-bold">{t('restart')}</span>
            </button>
          </div>
        </div>

        {/* Game Area */}
        <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
          <div
            ref={workspaceRef}
            className="relative w-full h-96 bg-white/5 rounded-2xl border-2 border-dashed border-white/30 overflow-hidden cursor-crosshair"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {/* Reference image in background */}
            <div className="absolute inset-4 opacity-20 rounded-xl overflow-hidden pointer-events-none">
              <img
                src={selectedImage.url || selectedImage.dataUrl}
                alt="Reference"
                className="w-full h-full object-contain"
              />
            </div>

            {/* Solved area outline */}
            <div
              className="absolute border-2 border-green-400/50 rounded-lg bg-green-400/10"
              style={{
                left: '50px',
                top: '50px',
                width: `${difficultySettings[difficulty].gridSize * 100}px`,
                height: `${difficultySettings[difficulty].gridSize * 100}px`
              }}
            />

            {/* Render puzzle pieces */}
            {puzzlePieces.map((piece) => (
              <div
                key={piece.id}
                className={`absolute cursor-move transition-all duration-200 ${
                  piece.solved ? 'z-10' : 'z-20'
                } ${draggedPiece?.id === piece.id ? 'scale-110 shadow-2xl' : 'hover:scale-105'}`}
                style={{
                  left: `${piece.x}px`,
                  top: `${piece.y}px`,
                  width: `${piece.width}px`,
                  height: `${piece.height}px`
                }}
                onMouseDown={(e) => handleMouseDown(e, piece)}
              >
                <div
                  className={`w-full h-full rounded-lg overflow-hidden border-2 ${
                    piece.solved
                      ? 'border-green-500 shadow-green-500/50'
                      : 'border-white shadow-lg'
                  }`}
                  style={{
                    backgroundImage: `url(${selectedImage.url || selectedImage.dataUrl})`,
                    backgroundPosition: `${-(piece.correctCol * (100 / difficultySettings[difficulty].gridSize))}% ${-(piece.correctRow * (100 / difficultySettings[difficulty].gridSize))}%`,
                    backgroundSize: `${difficultySettings[difficulty].gridSize * 100}% ${difficultySettings[difficulty].gridSize * 100}%`,
                    backgroundRepeat: 'no-repeat'
                  }}
                />
                {piece.solved && (
                  <div className="absolute inset-0 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <div className="text-green-600 text-2xl">✓</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Game Completed Modal */}
        {gameState === 'completed' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('puzzleCompleted')}</h2>
              <p className="text-gray-600 mb-6">{t('greatJob')}</p>

              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                    <div className="text-sm text-gray-600">{t('completionTime')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{score}</div>
                    <div className="text-sm text-gray-600">{t('score')}</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setGameState('menu')}
                  className="flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200"
                >
                  {t('playAgain')}
                </button>
                <button
                  onClick={() => {
                    playClickSound()
                    navigate('/dashboard')
                  }}
                  className="flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('backToDashboard')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PuzzleGame