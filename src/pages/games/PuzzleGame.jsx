import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { <PERSON>Left, RotateCcw, Trophy, Clock, Shuffle } from 'lucide-react'
import {
  getImageCollection,
  getGameProgress,
  updateGameProgress,
  unlockAchievement
} from '../../utils/localStorage'
import { playClickSound, playSuccessSound } from '../../utils/audio'
import { useLanguage } from '../../contexts/LanguageContext'
// Simple puzzle implementation without external libraries

const PuzzleGame = ({ childName }) => {
  const [gameState, setGameState] = useState('menu') // menu, playing, completed
  const [selectedImage, setSelectedImage] = useState(null)
  const [timeElapsed, setTimeElapsed] = useState(0)
  const [gameStartTime, setGameStartTime] = useState(null)
  const [difficulty, setDifficulty] = useState('easy')
  const [availableImages, setAvailableImages] = useState([])
  const [gameProgress, setGameProgress] = useState({})
  const [score, setScore] = useState(0)
  const [puzzlePieces, setPuzzlePieces] = useState([])
  const [draggedPiece, setDraggedPiece] = useState(null)
  const [selectedPiece, setSelectedPiece] = useState(null) // For mobile tap-to-select
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640)
  const puzzleContainerRef = useRef(null)
  const { t } = useLanguage()
  const navigate = useNavigate()

  // Difficulty settings - pieces count for children
  const difficultySettings = {
    easy: { pieces: 4, cols: 2, rows: 2, timeBonus: 300 },    // 2x2 square
    medium: { pieces: 9, cols: 3, rows: 3, timeBonus: 200 },  // 3x3 square
    hard: { pieces: 16, cols: 4, rows: 4, timeBonus: 100 }    // 4x4 square
  }

  useEffect(() => {
    if (childName) {
      const collection = getImageCollection(childName)
      const allImages = collection.themes?.reduce((acc, theme) => {
        return [...acc, ...theme.images]
      }, []) || []
      setAvailableImages(allImages)

      const progress = getGameProgress(childName)
      setGameProgress(progress.puzzle || { completed: 0, favoriteImage: null, bestTime: null })
    }
  }, [childName])

  // Timer effect
  useEffect(() => {
    let interval = null
    if (gameState === 'playing' && gameStartTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - gameStartTime) / 1000))
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [gameState, gameStartTime])

  // Responsive detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Initialize headbreaker puzzle
  // Initialize simple drag-and-drop puzzle
  const initializePuzzle = (image) => {
    console.log('Initializing simple puzzle with image:', image)

    const { cols, rows } = difficultySettings[difficulty]

    // Create puzzle pieces
    const newPieces = []
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const pieceId = row * cols + col
        newPieces.push({
          id: pieceId,
          row,
          col,
          currentRow: row,
          currentCol: col,
          correctPosition: { row, col },
          isPlaced: false,
          image: image.url || image.dataUrl
        })
      }
    }

    // Shuffle pieces
    const shuffledPieces = [...newPieces]
    for (let i = shuffledPieces.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      // Swap positions
      const tempRow = shuffledPieces[i].currentRow
      const tempCol = shuffledPieces[i].currentCol
      shuffledPieces[i].currentRow = shuffledPieces[j].currentRow
      shuffledPieces[i].currentCol = shuffledPieces[j].currentCol
      shuffledPieces[j].currentRow = tempRow
      shuffledPieces[j].currentCol = tempCol
    }

    setPuzzlePieces(shuffledPieces)
    console.log('Puzzle pieces created:', shuffledPieces)
  }

  // Handle drag and drop
  const handleDragStart = (e, piece) => {
    setDraggedPiece(piece)
    const rect = e.target.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
  }

  const handleDragOver = (e) => {
    e.preventDefault()
  }

  const handleDrop = (e, targetRow, targetCol) => {
    e.preventDefault()
    if (!draggedPiece) return

    // Find the piece currently in the target position
    const targetPiece = puzzlePieces.find(p => p.currentRow === targetRow && p.currentCol === targetCol)

    if (targetPiece && targetPiece.id !== draggedPiece.id) {
      // Swap positions
      const newPieces = puzzlePieces.map(piece => {
        if (piece.id === draggedPiece.id) {
          return { ...piece, currentRow: targetRow, currentCol: targetCol }
        } else if (piece.id === targetPiece.id) {
          return { ...piece, currentRow: draggedPiece.currentRow, currentCol: draggedPiece.currentCol }
        }
        return piece
      })

      setPuzzlePieces(newPieces)
      playClickSound()

      // Check if puzzle is completed
      checkPuzzleCompletion(newPieces)
    }

    setDraggedPiece(null)
  }

  const checkPuzzleCompletion = (pieces) => {
    const isCompleted = pieces.every(piece =>
      piece.currentRow === piece.correctPosition.row &&
      piece.currentCol === piece.correctPosition.col
    )

    if (isCompleted) {
      setTimeout(() => {
        setGameState('completed')
        playSuccessSound()

        const finalTime = Math.floor((Date.now() - gameStartTime) / 1000)
        setTimeElapsed(finalTime)

        const finalScore = Math.max(difficultySettings[difficulty].timeBonus - finalTime, 50)
        setScore(finalScore)

        const newProgress = {
          ...gameProgress,
          completed: (gameProgress.completed || 0) + 1,
          bestTime: gameProgress.bestTime ? Math.min(gameProgress.bestTime, finalTime) : finalTime,
          favoriteImage: selectedImage?.id
        }

        updateGameProgress(childName, 'puzzle', newProgress)
        setGameProgress(newProgress)

        if (newProgress.completed >= 3) {
          unlockAchievement(childName, 'puzzleMaster')
        }
      }, 500)
    }
  }

  // Mobile touch handlers for tap-to-select and swap
  const handlePieceClick = (piece, targetRow, targetCol) => {
    playClickSound()

    if (selectedPiece) {
      // If a piece is already selected, try to swap
      if (selectedPiece.id !== piece.id) {
        // Swap pieces
        const updatedPieces = puzzlePieces.map(p => {
          if (p.id === selectedPiece.id) {
            return { ...p, currentRow: targetRow, currentCol: targetCol }
          }
          if (p.id === piece.id) {
            return { ...p, currentRow: selectedPiece.currentRow, currentCol: selectedPiece.currentCol }
          }
          return p
        })

        setPuzzlePieces(updatedPieces)
        setSelectedPiece(null)

        // Check if puzzle is completed
        checkPuzzleCompletion(updatedPieces)
      } else {
        // Deselect if clicking the same piece
        setSelectedPiece(null)
      }
    } else {
      // Select the piece
      setSelectedPiece(piece)
    }
  }



  const startGame = (image) => {
    playClickSound()
    setSelectedImage(image)
    setTimeElapsed(0)
    setScore(0)
    setGameStartTime(Date.now())
    setGameState('playing')

    // Initialize puzzle after state is set
    setTimeout(() => {
      initializePuzzle(image)
    }, 100)
  }



  const shufflePieces = () => {
    playClickSound()
    if (puzzlePieces.length > 0) {
      const shuffled = [...puzzlePieces]
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1))
        // Swap positions
        const tempRow = shuffled[i].currentRow
        const tempCol = shuffled[i].currentCol
        shuffled[i].currentRow = shuffled[j].currentRow
        shuffled[i].currentCol = shuffled[j].currentCol
        shuffled[j].currentRow = tempRow
        shuffled[j].currentCol = tempCol
      }
      setPuzzlePieces(shuffled)
    }
  }

  const resetGame = () => {
    playClickSound()
    if (selectedImage) {
      initializePuzzle(selectedImage)
    } else {
      setGameState('menu')
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (gameState === 'menu') {
    return (
      <div
        className="min-h-screen p-4 relative"
        style={{
          backgroundImage: 'url(/images/background-game.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"></div>

        <div className="max-w-6xl mx-auto relative z-10">
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => {
                playClickSound()
                navigate('/dashboard')
              }}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <ArrowLeft className="w-6 h-6" />
              <span className="font-bold">{t('backToDashboard')}</span>
            </button>

            <div className="text-center">
              <h1 className="text-3xl font-bold text-white mb-2">🧩 {t('puzzleGame')}</h1>
              <p className="text-white/80">{t('puzzleDesc')}</p>
            </div>

            <div className="w-32"></div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8">
            <div className="grid grid-cols-3 gap-6 text-center text-white">
              <div>
                <div className="text-2xl font-bold">{gameProgress.completed || 0}</div>
                <div className="text-sm opacity-80">{t('puzzlesCompleted')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {gameProgress.bestTime ? formatTime(gameProgress.bestTime) : '--:--'}
                </div>
                <div className="text-sm opacity-80">{t('bestTime')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{availableImages.length}</div>
                <div className="text-sm opacity-80">{t('availableImages')}</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-2xl mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseDifficulty')}</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(difficultySettings).map(([level, settings]) => (
                <button
                  key={level}
                  onClick={() => {
                    playClickSound()
                    setDifficulty(level)
                  }}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 ${
                    difficulty === level
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">
                      {level === 'easy' ? '😊' : level === 'medium' ? '🤔' : '😤'}
                    </div>
                    <div className="font-bold text-gray-800 capitalize mb-2">{t(level)}</div>
                    <div className="text-sm text-gray-600">
                      {settings.cols}x{settings.rows} ({settings.pieces} {t('pieces')})
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-3xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">{t('chooseImage')}</h2>

            {availableImages.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                <div className="text-4xl mb-4">📸</div>
                <p>{t('noImagesAvailable')}</p>
                <p className="text-sm mt-2">{t('generateSomeImages')}</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {availableImages.map((image) => (
                  <button
                    key={image.id}
                    onClick={() => startGame(image)}
                    className="group relative aspect-square rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    <img
                      src={image.url || image.dataUrl}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                      <div className="bg-white/90 backdrop-blur-sm rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity">
                        <span className="text-2xl">🧩</span>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                      <p className="text-white text-xs font-bold truncate">{image.name}</p>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen p-4 relative"
      style={{
        backgroundImage: 'url(/images/background-game.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 via-blue-500/30 to-purple-500/30"></div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => {
              playClickSound()
              setGameState('menu')
            }}
            className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
          >
            <ArrowLeft className="w-6 h-6" />
            <span className="font-bold">{t('backToMenu')}</span>
          </button>

          <div className="flex items-center gap-6 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3">
            <div className="flex items-center gap-2 text-white">
              <Clock className="w-5 h-5" />
              <span className="font-bold">{formatTime(timeElapsed)}</span>
            </div>
            <div className="flex items-center gap-2 text-white">
              <Trophy className="w-5 h-5" />
              <span className="font-bold">{difficultySettings[difficulty].cols}x{difficultySettings[difficulty].rows} ({difficultySettings[difficulty].pieces} {t('pieces')})</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={shufflePieces}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <Shuffle className="w-6 h-6" />
              <span className="font-bold">{t('shuffle')}</span>
            </button>
            <button
              onClick={resetGame}
              className="flex items-center gap-2 text-white hover:text-white/80 transition-colors"
            >
              <RotateCcw className="w-6 h-6" />
              <span className="font-bold">{t('restart')}</span>
            </button>

          </div>
        </div>

        {/* Game Area - Puzzle Grid */}
        <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-6">
          <div className="mb-4 text-center">
            <h3 className="text-white font-bold text-lg">{t('puzzleGame')}</h3>
            <p className="text-white/70 text-sm hidden sm:block">{t('dragPiecesToSolve')}</p>
            <p className="text-white/70 text-sm sm:hidden">Ketuk untuk memilih, ketuk lagi untuk menukar posisi</p>
          </div>

          {puzzlePieces.length > 0 && (
            <div className="flex justify-center px-4">
              <div
                ref={puzzleContainerRef}
                className="bg-white rounded-2xl p-3 sm:p-4 md:p-6 shadow-2xl max-w-full"
                style={{
                  display: 'grid',
                  gridTemplateColumns: `repeat(${difficultySettings[difficulty].cols}, 1fr)`,
                  gap: isMobile ? '2px' : '6px',
                  width: 'fit-content'
                }}
              >
                {Array.from({ length: difficultySettings[difficulty].pieces }).map((_, index) => {
                  const { cols, rows } = difficultySettings[difficulty]
                  const row = Math.floor(index / cols)
                  const col = index % cols
                  const piece = puzzlePieces.find(p => p.currentRow === row && p.currentCol === col)

                  return (
                    <div
                      key={index}
                      className="relative w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36 border-2 border-gray-300 rounded-lg overflow-hidden bg-gray-100"
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDrop(e, row, col)}
                    >
                      {piece && (
                        <div
                          draggable
                          onDragStart={(e) => handleDragStart(e, piece)}
                          onClick={() => handlePieceClick(piece, row, col)}
                          className={`w-full h-full cursor-move relative group transition-all duration-200 ${
                            selectedPiece?.id === piece.id ? 'ring-4 ring-blue-500 ring-opacity-70' : ''
                          }`}
                          style={{
                            backgroundImage: `url(${piece.image})`,
                            backgroundSize: `${cols * 100}% ${rows * 100}%`,
                            backgroundPosition: `${cols > 1 ? piece.correctPosition.col * 100 / (cols - 1) : 0}% ${rows > 1 ? piece.correctPosition.row * 100 / (rows - 1) : 0}%`
                          }}
                        >
                          {/* Piece number for debugging */}
                          <div className="absolute top-1 left-1 bg-black/50 text-white text-xs px-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                            {piece.id + 1}
                          </div>

                          {/* Selected piece indicator */}
                          {selectedPiece?.id === piece.id && (
                            <div className="absolute inset-0 border-2 border-blue-500 bg-blue-500/20 rounded">
                              <div className="absolute top-1 right-1 text-blue-600 animate-pulse">
                                👆
                              </div>
                            </div>
                          )}

                          {/* Correct position indicator */}
                          {piece.currentRow === piece.correctPosition.row && piece.currentCol === piece.correctPosition.col && (
                            <div className="absolute inset-0 border-2 border-green-500 bg-green-500/20 rounded">
                              <div className="absolute top-1 right-1 text-green-600">
                                ✓
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </div>

        {/* Game Completed Modal */}
        {gameState === 'completed' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('puzzleCompleted')}</h2>
              <p className="text-gray-600 mb-6">{t('greatJob')}</p>

              <div className="bg-gray-50 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                    <div className="text-sm text-gray-600">{t('completionTime')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{score}</div>
                    <div className="text-sm text-gray-600">{t('score')}</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setGameState('menu')}
                  className="flex-1 bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-200"
                >
                  {t('playAgain')}
                </button>
                <button
                  onClick={() => {
                    playClickSound()
                    navigate('/dashboard')
                  }}
                  className="flex-1 border-2 border-gray-300 text-gray-700 py-3 rounded-xl font-bold hover:bg-gray-50 transition-colors"
                >
                  {t('backToDashboard')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default PuzzleGame