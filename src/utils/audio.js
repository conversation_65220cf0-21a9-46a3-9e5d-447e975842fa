// Audio management for KidzPlay AI
class AudioManager {
  constructor() {
    this.bgMusic = null
    this.soundEffects = {}
    this.isMuted = false
    this.bgMusicVolume = 0.3 // 30% volume for background music
    this.sfxVolume = 0.5 // 50% volume for sound effects
    this.userHasInteracted = false

    this.initializeAudio()
    this.setupUserInteractionListener()
  }

  // Setup user interaction listener
  setupUserInteractionListener() {
    const enableAudio = () => {
      this.userHasInteracted = true
      this.playBackgroundMusic()
      // Remove listeners after first interaction
      document.removeEventListener('click', enableAudio)
      document.removeEventListener('touchstart', enableAudio)
      document.removeEventListener('keydown', enableAudio)
    }

    document.addEventListener('click', enableAudio)
    document.addEventListener('touchstart', enableAudio)
    document.addEventListener('keydown', enableAudio)
  }

  initializeAudio() {
    // Initialize background music with fallback
    try {
      const musicPath = import.meta.env.PROD ? '/music/music-bgm.mp3' : '/music/music-bgm.mp3'
      this.bgMusic = new Audio(musicPath)
      this.bgMusic.loop = true
      this.bgMusic.volume = this.bgMusicVolume

      // Add error handling for audio loading
      this.bgMusic.addEventListener('error', (e) => {
        console.log('Background music failed to load:', e)
        this.bgMusic = null
      })

      this.bgMusic.addEventListener('canplaythrough', () => {
        console.log('Background music loaded successfully')
      })
    } catch (error) {
      console.log('Error initializing background music:', error)
      this.bgMusic = null
    }

    // Create sound effects using Web Audio API for better performance
    this.createSoundEffect('click', this.generateClickSound())
    this.createSoundEffect('success', this.generateSuccessSound())
    this.createSoundEffect('cardMatch', this.generateCardMatchSound())
    this.createSoundEffect('sparkle', this.generateSparkleSound())
    this.createSoundEffect('cardFlip', this.generateCardFlipSound())
    this.createSoundEffect('achievement', this.generateAchievementSound())
    this.createSoundEffect('packOpen', this.generatePackOpenSound())
  }

  // Generate click sound using Web Audio API
  generateClickSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1)

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.sfxVolume * 0.3, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.1)
    }
  }

  // Generate success sound
  generateSuccessSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(523, audioContext.currentTime) // C5
      oscillator.frequency.setValueAtTime(659, audioContext.currentTime + 0.1) // E5
      oscillator.frequency.setValueAtTime(784, audioContext.currentTime + 0.2) // G5

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.sfxVolume * 0.4, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.3)
    }
  }

  // Generate card match sound (more celebratory)
  generateCardMatchSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      // Create multiple oscillators for a richer sound
      const oscillators = []
      const gainNodes = []

      // Main melody
      const mainOsc = audioContext.createOscillator()
      const mainGain = audioContext.createGain()
      mainOsc.connect(mainGain)
      mainGain.connect(audioContext.destination)

      // Harmony
      const harmonyOsc = audioContext.createOscillator()
      const harmonyGain = audioContext.createGain()
      harmonyOsc.connect(harmonyGain)
      harmonyGain.connect(audioContext.destination)

      // Play a happy chord progression
      const notes = [
        { main: 523, harmony: 659 }, // C5 + E5
        { main: 659, harmony: 784 }, // E5 + G5
        { main: 784, harmony: 1047 } // G5 + C6
      ]

      notes.forEach((note, index) => {
        const time = audioContext.currentTime + index * 0.15
        mainOsc.frequency.setValueAtTime(note.main, time)
        harmonyOsc.frequency.setValueAtTime(note.harmony, time)
      })

      // Set up gain envelopes
      mainGain.gain.setValueAtTime(0, audioContext.currentTime)
      mainGain.gain.linearRampToValueAtTime(this.sfxVolume * 0.3, audioContext.currentTime + 0.01)
      mainGain.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5)

      harmonyGain.gain.setValueAtTime(0, audioContext.currentTime)
      harmonyGain.gain.linearRampToValueAtTime(this.sfxVolume * 0.2, audioContext.currentTime + 0.01)
      harmonyGain.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5)

      mainOsc.start(audioContext.currentTime)
      mainOsc.stop(audioContext.currentTime + 0.5)
      harmonyOsc.start(audioContext.currentTime)
      harmonyOsc.stop(audioContext.currentTime + 0.5)
    }
  }

  // Generate sparkle sound
  generateSparkleSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // High pitched twinkling sound
      oscillator.frequency.setValueAtTime(1200, audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(2000, audioContext.currentTime + 0.1)
      oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2)

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.sfxVolume * 0.15, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.2)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.2)
    }
  }

  // Generate card flip sound
  generateCardFlipSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(300, audioContext.currentTime)
      oscillator.frequency.linearRampToValueAtTime(600, audioContext.currentTime + 0.05)
      oscillator.frequency.linearRampToValueAtTime(200, audioContext.currentTime + 0.1)

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.sfxVolume * 0.2, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.1)
    }
  }

  // Generate achievement sound
  generateAchievementSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // Play a triumphant melody
      const notes = [523, 659, 784, 1047] // C5, E5, G5, C6
      notes.forEach((freq, index) => {
        oscillator.frequency.setValueAtTime(freq, audioContext.currentTime + index * 0.15)
      })

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.sfxVolume * 0.5, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.6)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.6)
    }
  }

  // Generate pack opening sound
  generatePackOpenSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)()

    return () => {
      if (this.isMuted) return

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.frequency.setValueAtTime(200, audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.3)
      oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.5)

      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(this.sfxVolume * 0.4, audioContext.currentTime + 0.1)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.5)

      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.5)
    }
  }

  createSoundEffect(name, generator) {
    this.soundEffects[name] = generator
  }

  // Play background music
  async playBackgroundMusic() {
    if (this.isMuted || !this.bgMusic) {
      console.log('Cannot play background music:', { muted: this.isMuted, bgMusic: !!this.bgMusic })
      return
    }

    try {
      // Only try to play if user has interacted with the page
      if (this.userHasInteracted) {
        console.log('Attempting to play background music...')
        await this.bgMusic.play()
        console.log('Background music started successfully')
      } else {
        console.log('Waiting for user interaction to play background music')
      }
    } catch (error) {
      console.log('Background music autoplay blocked by browser:', error)
      // We'll try to play it on first user interaction
      this.userHasInteracted = false
    }
  }

  // Stop background music
  stopBackgroundMusic() {
    if (this.bgMusic) {
      this.bgMusic.pause()
      this.bgMusic.currentTime = 0
    }
  }

  // Play sound effect
  playSoundEffect(name) {
    if (this.isMuted || !this.soundEffects[name]) return

    try {
      this.soundEffects[name]()
    } catch (error) {
      console.log('Sound effect error:', error)
    }
  }

  // Toggle mute
  toggleMute() {
    this.isMuted = !this.isMuted

    if (this.isMuted) {
      this.stopBackgroundMusic()
    } else {
      this.playBackgroundMusic()
    }

    return this.isMuted
  }

  // Set volumes
  setBgMusicVolume(volume) {
    this.bgMusicVolume = Math.max(0, Math.min(1, volume))
    if (this.bgMusic) {
      this.bgMusic.volume = this.bgMusicVolume
    }
  }

  setSfxVolume(volume) {
    this.sfxVolume = Math.max(0, Math.min(1, volume))
  }

  // Try to resume audio context (for browsers that suspend it)
  resumeAudioContext() {
    if (window.AudioContext || window.webkitAudioContext) {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)()
      if (audioContext.state === 'suspended') {
        audioContext.resume()
      }
    }
  }
}

// Create global audio manager instance
const audioManager = new AudioManager()

// Export functions for easy use
export const playBackgroundMusic = () => audioManager.playBackgroundMusic()
export const stopBackgroundMusic = () => audioManager.stopBackgroundMusic()
export const playClickSound = () => audioManager.playSoundEffect('click')
export const playSuccessSound = () => audioManager.playSoundEffect('success')
export const playCardMatchSound = () => audioManager.playSoundEffect('cardMatch')
export const playSparkleSound = () => audioManager.playSoundEffect('sparkle')
export const playCardFlipSound = () => audioManager.playSoundEffect('cardFlip')
export const playAchievementSound = () => audioManager.playSoundEffect('achievement')
export const playPackOpenSound = () => audioManager.playSoundEffect('packOpen')
export const toggleMute = () => audioManager.toggleMute()
export const setBgMusicVolume = (volume) => audioManager.setBgMusicVolume(volume)
export const setSfxVolume = (volume) => audioManager.setSfxVolume(volume)
export const resumeAudioContext = () => audioManager.resumeAudioContext()

export default audioManager