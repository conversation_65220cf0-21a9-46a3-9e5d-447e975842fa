name = "kidzplayai"
compatibility_date = "2024-01-01"

[env.production]
routes = [
  { pattern = "kidzplayai.pages.dev/*", zone_name = "pages.dev" }
]

[[env.production.rules]]
type = "Text"
globs = ["**/*.html", "**/*.txt"]
compress = true

[[env.production.rules]]
type = "Data"
globs = ["**/*.jpg", "**/*.jpeg", "**/*.png", "**/*.gif", "**/*.webp", "**/*.mp3", "**/*.wav", "**/*.ogg"]
compress = false
